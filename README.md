# A1D API Service

A1D API 是一个基于 Cloudflare Workers 构建的服务转发和鉴权系统。本服务提供了安全可靠的 API 网关功能，支持多种认证方式和服务集成。

## 功能特性

- 🔐 多重认证支持
  - Supabase Token 认证
  - API Key 认证
  - Canva 认证体系集成
  - JWT 认证
- 🚀 服务集成
  - AWS SQS 消息队列
  - R2 对象存储
  - KV 键值存储
  - Hyperdrive 数据库访问
  - WebSocket 实时通信
  - Scheduled 定时任务
  - 队列处理系统
- ⚡ 高性能
  - 基于 Cloudflare Workers 的边缘计算
  - 全球分布式部署
  - 批处理任务优化
- 🛠 开发友好
  - TypeScript 支持
  - 完整的类型定义
  - 详细的日志追踪
  - OpenAPI 文档支持（Hono 框架）

## 应用服务

- 图像处理
  - 图像放大 (Image Upscaler)
  - 背景移除 (Remove BG)
  - 图像矢量化 (Image Vectorization)
  - **图像扩展 (Image Extends)** - 基于 Ideogram V3 Reframe API
  - 图像代理 (Image Proxy)
  - 图像生成 (Image Generator)
  - Gemini 图像生成
- 视频处理
  - 视频放大 (Video Upscaler)
- AI 服务
  - 速写绘画 (SpeedPainter)
  - 图像后文本检测 (Text Behind Image)
- 系统服务
  - 文件上传（标准和 Base64）
  - 任务管理（取消、状态跟踪）
  - 用户信息和积分管理
  - Stripe 支付集成
  - 任务数据分析

## 📚 文档

### API 文档
- [Ideogram V3 Reframe API 参考](docs/api-reference.md) - 详细的 API 参数说明和示例
- [Ideogram V3 集成指南](docs/ideogram-v3-reframe-integration.md) - 完整的集成文档
- [测试示例](docs/test-examples.md) - 实用的测试用例和示例

## 技术架构

### 核心组件

- **Worker**: Cloudflare Workers 服务运行环境
- **KV**: 键值存储系统
- **R2**: 对象存储服务
- **Hyperdrive**: 数据库连接加速服务
- **Queue**: 任务队列处理系统
- **Scheduled**: 定时任务处理

### 项目结构

```
api-cf/
├── worker/
│   ├── api-service/       # API 主服务
│   │   ├── src/
│   │   │   ├── endpoints/    # API 端点定义
│   │   │   ├── middlewares/  # 中间件（认证、授权等）
│   │   │   ├── model/       # 数据模型
│   │   │   ├── service/     # 业务逻辑
│   │   │   ├── utils/       # 工具函数
│   │   │   ├── index.ts     # 主入口
│   │   │   ├── types.ts     # 类型定义
│   │   │   ├── queue.ts     # 队列处理
│   │   │   └── scheduled.ts # 定时任务
│   │   └── wrangler.toml    # 服务配置
│   └── auth-service/      # 身份认证服务
│       ├── src/
│       │   ├── endpoints/    # 认证端点
│       │   ├── middlewares/  # 认证中间件
│       │   ├── service/      # 认证业务逻辑
│       │   └── index.ts      # 主入口
│       └── wrangler.toml     # 服务配置
├── docs/                   # 项目文档
└── db/                     # 数据库相关
```

## 开发环境设置

### 前置要求

- Node.js >= 16
- npm >= 7
- Wrangler CLI

### 安装步骤

1. 克隆项目
```bash
git clone [repository-url]
cd a1d-api-cf
```

2. 安装依赖
```bash
npm install
```

3. 生成类型文件
```bash
npm run cf-typegen
```

### 配置说明

项目包含两个服务：API Service 和 Auth Service，每个服务都需要进行相应的配置。配置分为两部分：

1. `wrangler.*.toml` 文件配置：用于非敏感配置
2. Wrangler Secrets：用于敏感信息配置

#### API Service 配置

##### wrangler.prod.toml 配置

```toml
name = "a1d-api-prod"                        # 服务名称
compatibility_date = "2024-09-23"            # 兼容性日期
compatibility_flags = ["nodejs_compat"]       # 兼容性标志

# KV 命名空间配置
[[kv_namespaces]]
binding = "MY_KV_NAMESPACE"
id = "31c243739e884bbc87d8af7da76fa7f7"

# R2 存储桶配置
[[r2_buckets]]
binding = "MY_BUCKET"
bucket_name = "a1d-api-prod"

# Hyperdrive 配置
[[hyperdrive]]
binding = "HYPERDRIVE"
id = "2fbf161a2e554824a198d55e04ddeb7d"

# 队列配置
[[queues.producers]]
binding = "TASK_QUEUE"
queue = "a1d-api-prod"
max_batch_size = 5
max_batch_timeout = 5

[[queues.consumers]]
queue = "a1d-api-prod"
max_retries = 3
retry_delay = 1
dead_letter_queue = "a1d-api-prod-dead"

# 服务绑定
[[services]]
binding = "AUTH_SERVICE"
service = "a1d-auth-prod"
entrypoint = "AuthService"

# 定时任务配置
[triggers]
crons = [
    "0 9 * * *",     # 每天早上 9 点执行任务分析
    "*/5 * * * *"    # 每 5 分钟执行一次任务清理
]
```

##### 敏感配置（通过 wrangler secret 设置）

```bash
# 认证相关
wrangler secret put JWT_SECRET               # JWT 签名密钥
wrangler secret put SUPABASE_URL             # Supabase URL
wrangler secret put SUPABASE_SERVICE_ROLE_KEY # Supabase 服务角色密钥
wrangler secret put SUPABASE_JWT_SECRET      # Supabase JWT 密钥
wrangler secret put FIXED_SIGN               # 固定签名密钥
wrangler secret put ADMIN_KEY                # 管理员密钥

# 第三方服务
wrangler secret put FAL_API_KEY              # FAL API 密钥
wrangler secret put RMBG_API_URL             # Remove Background API URL
wrangler secret put RMBG_TOKEN               # Remove Background API Token
wrangler secret put GEMINI_API_KEY           # Gemini AI API Key
wrangler secret put IMAGE_EXTENDS_TOKEN      # 图像扩展服务 Token
wrangler secret put IMAGE_VECTORIZATION_TOKEN # 图像矢量化服务 Token

# 存储配置
wrangler secret put S3_ACCESS_KEY_ID         # S3 访问密钥 ID
wrangler secret put S3_SECRET_ACCESS_KEY     # S3 访问密钥

# 数据库连接
wrangler secret put DATABASE_URL             # 数据库连接 URL
wrangler secret put HYPERDRIVE_CONNECTION_STRING # Hyperdrive 连接字符串
```

#### Auth Service 配置

##### wrangler.prod.toml 配置

```toml
name = "a1d-auth-prod"                       # 服务名称
compatibility_date = "2024-09-23"            # 兼容性日期
compatibility_flags = ["nodejs_compat"]       # 兼容性标志

[vars]
EXPIRATION_TTL = 3600                        # Token 过期时间（秒）

# KV 命名空间配置
[[kv_namespaces]]
binding = "MY_KV_NAMESPACE"
id = "d9fa57d69a134163a6ddc36d04fcdba8"
```

##### 敏感配置（通过 wrangler secret 设置）

```bash
# 认证相关
wrangler secret put JWT_SECRET               # JWT 签名密钥
wrangler secret put SUPABASE_URL             # Supabase URL
wrangler secret put SUPABASE_SERVICE_ROLE_KEY # Supabase 服务角色密钥
wrangler secret put SUPABASE_JWT_SECRET      # Supabase JWT 密钥
```

> 注意：
> 1. 不要将敏感信息（如密钥、Token）添加到 wrangler.toml 文件中
> 2. wrangler.toml 文件可以有多个环境版本（如 prod、test）
> 3. 使用 `wrangler secret put` 添加的敏感配置在部署时会自动加密
> 4. 某些绑定（如 KV、R2）需要提前在 Cloudflare 控制台创建并获取 ID

## 部署

1. 确保已登录 Cloudflare
```bash
wrangler whoami
```

2. 部署服务
本地测试通过后，在 github 上把代码 merge 到 test 分支后，就会自动部署到 test 环境了。

3. 查看日志（可选）
```bash
wrangler tail
```

## API 认证

### 支持的认证方式

1. **Supabase Token**
   - 支持 Supabase 原生 token
   - 系统会自动转换为内部 JWT token

2. **API Key**
   - 通过 `Authorization` 头部传递
   - 格式：`Bearer <api-key>`

3. **Canva 认证**
   - 完全兼容 Canva 的认证体系
   - 支持 Canva 应用集成

## 监控和日志

- 使用 `wrangler tail` 查看实时日志
- Cloudflare Dashboard 提供详细的性能监控
- 支持自定义日志级别和格式

## API 端点文档

API 服务提供了以下主要端点：

### 认证相关
- `/api/exchange-token` - 交换 Supabase token 为内部 JWT
- `/api/gen-api-key` - 生成 API Key
- `/api/get-api-key` - 获取用户 API Key
- `/api/update-api-key` - 更新 API Key

### 用户和积分
- `/api/get-user-info` - 获取用户信息
- `/api/get-credit` - 获取用户积分
- `/api/stripe/add-pay-as-you-go` - Stripe 按量付费积分添加
- `/api/stripe/reset-plan-credits` - Stripe 订阅计划积分重置

### 任务管理
- `/api/task/:taskId` - 获取任务信息
- `/api/task/:taskId/sse` - 获取任务状态（SSE 实时推送）
- `/api/task/:taskId/cancel` - 取消任务
- `/api/data/task-data-analysis` - 任务数据分析

### 文件上传
- `/api/uploads/:file_name` - 文件上传
- `/api/file-upload-base64` - Base64 编码文件上传

### 图像处理
- `/api/image-upscaler` - 图像放大服务
- `/api/remove-bg` - 背景移除服务
- `/api/image-vectorization` - 图像矢量化服务
- `/api/image-extends` - 图像扩展服务
- `/api/image-proxy` - 图像代理服务
- `/api/image-generator` - 图像生成服务
- `/api/gemini-image-generation` - Gemini 图像生成

### AI 服务
- `/api/speedpainter` - 速写绘画服务
- `/api/text-behind-image` - 图像后文本检测服务

### 视频处理
- `/api/video-upscaler` - 视频放大服务