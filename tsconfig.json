{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "allowSyntheticDefaultImports": true, "types": ["@cloudflare/workers-types", "vitest/globals"], "lib": ["ES2022", "WebWorker"]}, "include": ["worker/**/*.ts", "test/**/*.ts", "**/*.test.ts"], "exclude": ["node_modules", "dist"]}