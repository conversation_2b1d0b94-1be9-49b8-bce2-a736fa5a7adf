{"name": "worker", "version": "0.0.0", "private": true, "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "start": "wrangler dev", "cf-typegen": "wrangler types", "test": "vitest", "test:api-service": "cd worker/api-service && vitest run", "test:auth-service": "cd worker/auth-service && vitest run", "test:watch": "vitest --watch", "test:coverage": "vitest run --coverage", "lint": "eslint . --ext .ts,.js --fix", "type-check": "tsc --noEmit", "prepare": "husky install", "pre-commit": "lint-staged"}, "dependencies": {"@supabase/supabase-js": "^2.45.1", "aws4fetch": "^1.0.20", "chanfana": "^2.0.2", "hono": "^4.4.11", "jose": "^5.6.3", "nanoid": "^5.0.7", "uuid": "^10.0.0", "zod": "^3.23.8"}, "devDependencies": {"@cloudflare/workers-types": "^4.20240620.0", "@types/lodash": "^4.17.7", "@types/node": "20.8.3", "@types/service-worker-mock": "^2.0.1", "@vitest/coverage-v8": "1.5.3", "@vitest/ui": "1.5.3", "eslint": "^8.57.0", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "typescript": "^5.4.5", "vitest": "1.5.3", "wrangler": "^3.60.3", "msw": "^2.2.0", "@cloudflare/vitest-pool-workers": "^0.2.0", "husky": "^9.0.11", "lint-staged": "^15.2.2"}, "lint-staged": {"*.{ts,js}": ["eslint --fix", "npm run type-check"], "worker/api-service/src/**/*.{ts,js}": ["npm run test:api-service"], "worker/auth-service/src/**/*.{ts,js}": ["npm run test:auth-service"]}}