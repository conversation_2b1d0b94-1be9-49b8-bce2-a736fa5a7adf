-- Create scene_endpoints table for storing CMS configuration
CREATE TABLE scene_endpoints (
    id SERIAL PRIMARY KEY,
    scene_id VARCHAR(50) NOT NULL UNIQUE,
    endpoint_url VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index for faster scene_id lookups
CREATE INDEX idx_scene_endpoints_scene_id ON scene_endpoints(scene_id);

-- Insert initial data from existing code configuration
-- Note: Replace the CMS_BASE_URL with actual base URL when executing this script
INSERT INTO scene_endpoints (scene_id, endpoint_url, description) VALUES
('relight', 'https://cms-base-url/Relight_Template', 'Relight templates and presets'),
('picadabra', 'https://cms-base-url/picadabra_usecases', 'Picadabra use cases and examples'),
('canva-sp', 'https://cms-base-url/whiteboard_animation_template', 'Canva whiteboard animation template'); 