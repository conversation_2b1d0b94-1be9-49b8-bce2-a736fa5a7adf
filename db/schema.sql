-- 创建a1d_user表
create table if not exists a1d_user (
    uid uuid primary key default gen_random_uuid(),
    account_id varchar(256) unique,
    created_at timestamp with time zone default current_timestamp,
    updated_at timestamp with time zone default current_timestamp
);

-- 创建apikey表
create table if not exists api_key (
    id uuid primary key default gen_random_uuid(),
    api_key varchar(256) unique,
    uid varchar(256) not null,
    account_id varchar(256) not null,
    name varchar(256),
    scope varchar(64) default 'API',
    status varchar(64) default 'ACTIVE',
    deleted_at timestamp with time zone,
    created_at timestamp with time zone default current_timestamp,
    updated_at timestamp with time zone default current_timestamp
);

-- 为apikey表的uid字段创建索引
create index if not exists idx_api_key_uid on api_key(uid);

-- 为apikey表的account_id字段创建索引
create index if not exists idx_api_key_account_id on api_key(account_id);

-- 创建canva_app表
create table if not exists canva_app (
    id uuid primary key default gen_random_uuid(),
    app_id varchar(256) unique,
    app_name varchar(256),
    created_at timestamp with time zone default current_timestamp,
    updated_at timestamp with time zone default current_timestamp
);

CREATE TABLE credit (
    id SERIAL PRIMARY KEY,
    uid VARCHAR(256) UNIQUE NOT NULL,
    account_id VARCHAR(256) UNIQUE NOT NULL,
    pending_credits INT NOT NULL DEFAULT 0 CHECK (pending_credits >= 0),
    plan_credits INT NOT NULL DEFAULT 0 CHECK (plan_credits >= 0),
    pay_as_go_credits INT NOT NULL DEFAULT 0 CHECK (pay_as_go_credits >= 0),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建 credit_audit 表
CREATE TABLE credit_audit (
    id SERIAL PRIMARY KEY,
    uid VARCHAR(256) NOT NULL,
    account_id VARCHAR(256) NOT NULL,
    task_id VARCHAR(256) NULL,
    app VARCHAR(64) NULL,
    operation_type VARCHAR(64) NOT NULL, -- 操作类型，例如 'deduct', 'add', 'reset'
    operation_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    credits_before JSON NOT NULL, -- 操作前的积分
    credits_after JSON NOT NULL, -- 操作后的积分
    description VARCHAR(1024) -- 操作描述
);
CREATE INDEX idx_credit_audit_account_id ON credit_audit(account_id);
CREATE INDEX idx_credit_audit_task_id ON credit_audit(task_id);

-- 创建使用记录表
CREATE TABLE task (
    id SERIAL PRIMARY KEY,
    uid VARCHAR(256) NOT NULL,
    task_id VARCHAR(256) NOT NULL,
    app VARCHAR(64) NOT NULL,
    source VARCHAR(64) NOT NULL,
    credit DECIMAL(10,2) NOT NULL DEFAULT 0 CHECK (credit >= 0),
    status VARCHAR(64) NOT NULL DEFAULT 'WAITING',
    error JSONB NULL,
    input_params JSONB NULL,
    result JSONB NULL,
    extra_info JSONB NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, 
    deleted_at TIMESTAMP WITH TIME ZONE,
    CONSTRAINT usage_history_task_unique UNIQUE (uid, task_id, app, source)
);

CREATE TABLE credit_rules (
    id INTEGER PRIMARY KEY,
    app VARCHAR(50) NOT NULL,
    source VARCHAR(50) NOT NULL DEFAULT 'ALL',
    base_credits INTEGER NOT NULL,
    has_dynamic_rule BOOLEAN DEFAULT false,
    dynamic_rule_config JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(app, source)
);

CREATE TABLE task_daily_stats (
    stats_date DATE NOT NULL,
    uid VARCHAR(256) NOT NULL,
    app VARCHAR(64) NOT NULL,
    source VARCHAR(64) NOT NULL,
    success_task_count INTEGER NOT NULL DEFAULT 0,
    failed_task_count INTEGER NOT NULL DEFAULT 0,
    canceled_task_count INTEGER NOT NULL DEFAULT 0,
    success_credit_cost DECIMAL(10,2) NOT NULL DEFAULT 0,
    failed_credit_cost DECIMAL(10,2) NOT NULL DEFAULT 0,
    canceled_credit_cost DECIMAL(10,2) NOT NULL DEFAULT 0,
    total_task_count INTEGER NOT NULL DEFAULT 0,
    total_credit_cost DECIMAL(10,2) NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (stats_date, uid, app, source)
);

-- Create index for better query performance
CREATE INDEX idx_task_daily_stats_date ON task_daily_stats(stats_date);
CREATE INDEX idx_task_daily_stats_uid ON task_daily_stats(uid);
CREATE INDEX idx_task_daily_stats_app ON task_daily_stats(app);
CREATE INDEX idx_task_daily_stats_source ON task_daily_stats(source);

-- Create scene_endpoints table for storing CMS configuration
CREATE TABLE IF NOT EXISTS scene_endpoints (
    id SERIAL PRIMARY KEY,
    scene_id VARCHAR(50) NOT NULL UNIQUE,
    endpoint_url VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index for faster scene_id lookups
CREATE INDEX IF NOT EXISTS idx_scene_endpoints_scene_id ON scene_endpoints(scene_id);
