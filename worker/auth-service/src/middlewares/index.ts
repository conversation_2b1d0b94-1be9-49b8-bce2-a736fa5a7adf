import { Context } from 'hono';
import { doVerifyToken } from '../service/jwt';
import { TokenValidationError } from '../service/jwt/errors';

export async function authMiddleware(c: Context, next: () => Promise<void>) {
	const authHeader = c.req.header('Authorization');

	// 如果没有 Authorization header，返回错误
	if (!authHeader) {
		return c.json({ 
			error: 'Missing authorization header',
			code: 'MISSING_AUTH_HEADER'
		}, 401);
	}

	// 检查 Bearer token 格式
	let token = authHeader.trim();
	console.log('Auth header:', authHeader);
	if (token.startsWith('Bearer ')) {
		token = token.slice(7).trim();
	}

	try {
		console.log('Verifying token:', token);
		// 验证 token
		const payload = await doVerifyToken(token, c.env);

		// 如果返回的是 Response 对象，直接返回
		if (payload instanceof Response) {
			return payload;
		}

		// 将 token 信息存储在 context 中
		c.set('jwtPayload', payload);
		console.log('jwtPayload', payload);

		await next();
	} catch (error) {
		console.error('Token verification failed:', error);
		
		// 处理特定的验证错误
		if (error instanceof TokenValidationError) {
			return c.json({ 
				error: error.message,
				code: error.code
			}, 401);
		}

		// 处理其他未知错误
		return c.json({ 
			error: 'Authentication failed',
			code: 'AUTH_FAILED'
		}, 401);
	}
}





