import { convertParams } from 'chanfana';
import { z } from 'zod';

// FileBody
interface ParameterType {
	default?: string | number | boolean;
	description?: string;
	example?: string | number | boolean;
	required?: boolean;
	deprecated?: boolean;
}
interface StringParameterType extends ParameterType {
	format?: string;
}
export function FileBody(params?: StringParameterType): z.ZodString {
	return convertParams<z.ZodString>(z.string(), params);
}

// 直接使用 Env 类型
export type Bindings = Env;

// Cache
export type CacheHandler = {
	get: (key: string) => Promise<string | null>;
	set: (key: string, value: string, ttl?: number) => Promise<void>;
	del: (key: string) => Promise<void>;
};
 
export enum SOURCE {
	WEB = 'web',
	CANVA = 'canva',
	API = 'api'
}
