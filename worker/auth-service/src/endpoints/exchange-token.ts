import { OpenAPIRoute, contentJson } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings } from '../types';
import { doExchangeToken } from '../service/exchange-token';

export class ExchangeToken extends OpenAPIRoute {
        schema = {
                summary: '获取A1D 统一下发的 token',
                description: '更换不同的渠道来源的 token 为 A1D token, source 目前只支持 web',
                tags: ['exchange-token'],
                request: {
                        body: contentJson(
                                z.object({
                                        token: z.string().describe('渠道的 token')
                                })
                        ),
                },
                response: {
                        200: contentJson({
                                token: z.string().describe('A1D 签发的新 token')
                        }),
                        400: contentJson({
                                error: z.string().describe('请求错误信息')
                        }),
                        401: contentJson({
                                error: z.string().describe('认证错误信息')
                        }),
                        500: contentJson({
                                error: z.string().describe('服务器内部错误信息')
                        })
                }
        };

        async handle(c: Context<{ Bindings: Bindings }>) {
                try {
                        console.log('Exchange token request received');
                        console.log('Environment:', c.env);
                        const data = await this.getValidatedData<typeof this.schema>();
                        console.log('Validated data:', data);
                        const { token } = data.body;
                        const newToken = await doExchangeToken(token, c.env);
                        return c.json({ token: newToken });
                } catch (error: any) {
                        console.error('Token exchange failed:', error);
                        if (error instanceof z.ZodError) {
                                return c.json({ error: error.errors[0].message }, 400);
                        }
                        return c.json({ error: 'Token exchange failed' }, 500);
                }
        }
}
