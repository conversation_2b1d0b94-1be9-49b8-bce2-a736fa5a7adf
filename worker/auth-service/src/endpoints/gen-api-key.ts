import { OpenAPIRoute, contentJson } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings } from '../types';
import { doGenApiKey } from '../service/apiKey';
import { ApiKey } from '../service/supabase/models';


export class GenApiKey extends OpenAPIRoute {
        schema = {
                summary: '获取APIKEY',
                description: '如果用户已经有 API Key， 那么就直接返回，没有就生成一个 API Key',
                tags: ['api-key'],
                request: {
                        query: z.object({
                                scope: z.string().optional().describe('API KEY 的作用域'),
                                name: z.string().optional().describe('API KEY 的名称')

                        })
                },
                response: {
                        200: contentJson({
                                success: z.boolean().describe('是否成功'),
                                code: z.string().optional(),
                                message: z.string().optional(),
                                data: z.object({
                                        apiKey: z.string().describe('A1D 生成的API KEY'),
                                        name: z.string().describe('API KEY 的名称'),
                                        scope: z.string().describe('API KEY 的作用域'),
                                        status: z.string().describe('API KEY 的状态'),
                                        uid: z.string().describe('API KEY 的用户 ID'),
                                        account_id: z.string().describe('API KEY 的账户 ID'),
                                        created_at: z.string().describe('API KEY 的创建时间'),
                                        updated_at: z.string().describe('API KEY 的更新时间')
                                })
                        }),
                        400: contentJson({
                                error: z.string().describe('请求错误信息')
                        }),
                        401: contentJson({
                                error: z.string().describe('认证错误信息')
                        }),
                        500: contentJson({
                                error: z.string().describe('服务器内部错误信息')
                        })
                }
        };

        async handle(c: Context<{ Bindings: Bindings }>) {
                try {
                        const payload = c.get('jwtPayload');
                        const data = await this.getValidatedData<typeof this.schema>();
                        const { scope, name } = data.query;
                        const insertApiKey = {
                                scope,
                                name,
                                uid: payload.uid,
                                account_id: payload.account_id
                        } as ApiKey;

                        const apiKey = await doGenApiKey(c.env, insertApiKey);
                        return c.json({ success: true, data: apiKey });
                } catch (error: any) {
                        console.error('Gen API KEY Failed:', error);
                        if (error instanceof z.ZodError) {
                                return c.json({ error: error.errors[0].message }, 400);
                        }
                        return c.json({ error: 'Gen API KEY Failed' }, 500);
                }
        }
}
