import { WorkerEntrypoint } from 'cloudflare:workers';
import { JWTPayload } from 'jose';
import { doVerifyToken } from './jwt';
import { doGenApiKey, doUpdateApiKey, doGetApiKeys } from './apiKey';
import { doExchangeToken } from './exchange-token';
import { ApiKey } from './supabase/models';


export class AuthService extends WorkerEntrypoint<Env> {
        async doExchangeToken(token: string): Promise<string> {
                const result = await doExchangeToken(token, this.env);
                return result;
        }

        async doVerifyToken(token: string): Promise<JWTPayload | Response> {
                return await doVerifyToken(token, this.env);
        }

        async doGenApiKey(apiKey: ApiKey): Promise<ApiKey> {
                return await doGenApiKey(this.env, apiKey);
        }

        async doUpdateApiKey(apiKey: Api<PERSON><PERSON>): Promise<ApiKey> {
                return await doUpdate<PERSON>pi<PERSON>ey(this.env, apiKey);
        }

        async doGetApiKeys(uid: string): Promise<ApiKey[]> {
                return await doGetApiKeys(this.env, uid);
        }
}
