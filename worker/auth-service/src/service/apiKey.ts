import { nanoid } from 'nanoid';
import { doUpdateApiKeyInDB, doInsertApiKeyInDB, doGetApiKeysInDB } from './supabase';
import { ApiKey } from '../service/supabase/models';

// Generate API Key
export async function doGenApiKey(env: Env, apiKey: ApiKey): Promise<ApiKey> {
        const API_KEY = `${nanoid()}`; // nanoid 默认生成21个字符
        const newApiKey = {
                api_key: API_KEY,
                uid: apiKey.uid,
                scope: apiKey.scope || 'API',
                status: 'ACTIVE',
                name: apiKey.name,
                account_id: apiKey.account_id,
                created_at: new Date(),
                updated_at: new Date(),
        } as ApiKey;

        return await doInsertApiKeyInDB(newApiKey, env);
}

// Update API Key
export async function doUpdateApiKey(env: Env, apiKey: ApiKey) : Promise<ApiKey> {

        const updateApiKey = {
                name: apiKey.name,
                id: apiKey.id,
                status: apiKey.status,
                api_key: apiKey.api_key,
                deleted_at: apiKey.deleted_at,
                uid: apiKey.uid
        } as ApiKey;

        return await doUpdateApiKeyInDB(updateApiKey, env);
}

// Generate API Key
export async function doGetApiKeys(env: Env, uid: string): Promise<ApiKey[]> {

        let apiKeys = await doGetApiKeysInDB(env, uid);
        // 这里处理掉一些字段，只保留，api_key，name，scope，created_at, api_key 只保留前面 4 个字符，其他的使用 * 替换
        apiKeys = apiKeys.map((apiKey) => {
                return {
                        id: apiKey.id,
                        api_key: apiKey.api_key.slice(0, 4) + '********',
                        name: apiKey.name,
                        scope: apiKey.scope,
                        created_at: apiKey.created_at
                } as ApiKey;
        });
        return apiKeys;
}