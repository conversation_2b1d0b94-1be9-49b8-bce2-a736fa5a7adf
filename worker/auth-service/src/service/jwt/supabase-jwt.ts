
import { jwtVerify, JWTVerifyResult } from 'jose';

interface SupabaseUser {
        id?: string;
        email?: string;
        aud?: string;
        role?: string;
        sub?: string;  // Often used as user ID in JWT
        // Other potential properties...
}

/**
 * 解析Supabase JWT令牌
 * @param {string} token - 要解析的JWT令牌
 * @param {string} jwtSecret - 用于验证令牌的密钥
 * @returns {Promise<TokenPayload>} 解析后的令牌payload
 */

// 步骤1：验证 Supabase 签发的 token
export async function doVerifySupabaseToken(token: string, env: Env): Promise<SupabaseUser> {
        if (!env.SUPABASE_JWT_SECRET) {
                throw new Error('SUPABASE_JWT_SECRET is not set in the environment');
        }

        try {
                const secretKey = new TextEncoder().encode(env.SUPABASE_JWT_SECRET);
                const { payload } = await jwtVerify(token, secretKey) as JWTVerifyResult & { payload: SupabaseUser };

                // Check for essential properties
                if (!payload.sub) {
                        throw new Error('Invalid token payload: missing user identifier');
                }

                return payload;
        } catch (error) {
                console.error('Error parsing token:', error);
                throw error;
        }

}


// 步骤2：从验证后的用户信息中提取必要信息
export function extractSupabaseUserInfo(user: SupabaseUser): { accountId: string } {
        return {
                accountId: user.sub || '',
        };
}