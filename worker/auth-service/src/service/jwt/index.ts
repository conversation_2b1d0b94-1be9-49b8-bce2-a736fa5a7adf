import { SignJWT, decodeJwt, JWTPayload, jwtVerify } from 'jose';
import { v4 as uuidv4 } from 'uuid';
import { doGetA1dUser, doInsertA1dUser, doGetCanvaApp, doGetA1dKey } from '../supabase';
import { A1dUser } from '../supabase/models';
import { doVerifySupabaseToken, extractSupabaseUserInfo } from './supabase-jwt';
import { doVerifyCanvaToken } from './canva-jwt';
import { TokenValidationError } from './errors';

// 步骤3：生成新的 token
async function doGenerateNewToken(a1dUser: { uid: string, accountId: string }, env: Env): Promise<string> {
    if (!env.JWT_SECRET) {
        console.error('JWT_SECRET is not set in the environment');
        throw new TokenValidationError('JWT_SECRET is not configured', 'JWT_SECRET_MISSING');
    }

    const jwtSecret = env.JWT_SECRET.trim();
    if (jwtSecret.length === 0) {
        console.error('JWT_SECRET is empty');
        throw new TokenValidationError('JWT_SECRET is empty', 'JWT_SECRET_EMPTY');
    }

    console.log('Generating new token for user:', a1dUser);

    try {
        const newToken = await new SignJWT(a1dUser)
            .setProtectedHeader({ alg: 'HS256', type: 'JWT' })
            .setIssuedAt()
            .setAudience(env.URL_PREFIX)
            .setExpirationTime('7d')
            .sign(new TextEncoder().encode(jwtSecret));
        return newToken;
    } catch (error) {
        console.error('Error generating new token:', error);
        throw new TokenValidationError('Failed to generate new token', 'TOKEN_GENERATION_FAILED');
    }
}

// 验证 web token
async function doVerifyWebToken(token: string, env: Env): Promise<JWTPayload> {
    if (!env.JWT_SECRET) {
        throw new TokenValidationError('JWT secret is not configured', 'JWT_SECRET_MISSING');
    }

    try {
        const decoded = await jwtVerify(token, new TextEncoder().encode(env.JWT_SECRET));
        return decoded.payload as JWTPayload;
    } catch (error) {
        console.error('Token verification failed:', error);

        if (error instanceof Error) {
            // 处理具体的错误类型
            if (error.name === 'JWTExpired') {
                throw new TokenValidationError('Token has expired', 'TOKEN_EXPIRED');
            }
            if (error.name === 'JWSSignatureVerificationFailed') {
                throw new TokenValidationError('Invalid token signature', 'INVALID_SIGNATURE');
            }
            if (error.name === 'JWTMalformed') {
                throw new TokenValidationError('Malformed token', 'MALFORMED_TOKEN');
            }
            if (error.name === 'JWTInvalid') {
                throw new TokenValidationError('Invalid token format', 'INVALID_TOKEN');
            }
        }

        // 未知错误类型
        throw new TokenValidationError('Token verification failed', 'UNKNOWN_ERROR');
    }
}

export async function doWebTokenExchange(token: string, env: Env): Promise<string> {
    if (!env.SUPABASE_JWT_SECRET) {
        throw new TokenValidationError('Supabase configuration is incomplete', 'SUPABASE_CONFIG_MISSING');
    }

    try {
        const user = await doVerifySupabaseToken(token, env);
        const extractUserInfo = extractSupabaseUserInfo(user);
        // 获取这个用户是否存在在a1d_user表中
        let a1dUser = await doGetA1dUser(env, undefined, extractUserInfo.accountId);
        if (!a1dUser) {
            // 不存在，则在 A1D_USER 表中插入
            console.log('Inserting new user into A1D_USER table');
            const newA1dUser: A1dUser = {
                uid: uuidv4(),
                account_id: extractUserInfo.accountId
            }
            await doInsertA1dUser(newA1dUser, env);
            a1dUser = newA1dUser;
        }
        const newToken = await doGenerateNewToken({ uid: a1dUser.uid, accountId: a1dUser.account_id }, env);
        return newToken;
    } catch (error) {
        console.error('Detailed error in webTokenExchange:', error);
        if (error instanceof Error) {
            console.error('Error name:', error.name);
            console.error('Error message:', error.message);
            console.error('Error stack:', error.stack);
        }
        throw error;
    }
}

// 定义一个接口来描述我们期望的 JWT payload 结构
interface CustomJWTPayload extends JWTPayload {
    source?: string;
    aud?: string;
}

export async function doVerifyToken(token: string, env: Env): Promise<JWTPayload | Response> {
    try {
        // 先判断 Token 是否以API_KEY 开头  
        const API_KEY_PREFIX = 'KEY ';
        if (token.startsWith(API_KEY_PREFIX)) {
            // 从数据库中查询
            const apiKey = token.substring(API_KEY_PREFIX.length);
            const apiKeyRecord = await doGetA1dKey(env, undefined, apiKey);
            if (apiKeyRecord) {
                return { uid: apiKeyRecord.uid, apiKey: apiKey, accountId: apiKeyRecord.account_id } as JWTPayload;
            }
            throw new TokenValidationError('Invalid API key', 'INVALID_API_KEY');
        }

        if (token.startsWith('Bearer ')) {
            token = token.slice(7).trim();
        }

        // 对 token 进行 decode，看 payload 中是否有 source 这个值
        const decoded = decodeJwt(token);
        const payload = decoded as CustomJWTPayload;

        console.log(decoded);
        const aud: string = payload.aud || '';

        if (aud === env.URL_PREFIX) {
            try {
                return await doVerifyWebToken(token, env);
            } catch (error) {
                if (error instanceof TokenValidationError) {
                    return new Response(JSON.stringify({
                        error: error.message,
                        code: error.code
                    }), {
                        status: 401,
                        headers: { 'Content-Type': 'application/json' }
                    });
                }
                throw error;
            }
        }

        // 最后判断是属于 canva 的 user
        if (aud) {
            const app = await doGetCanvaApp(env, aud);
            if (app) {
                try {
                    const canvaPayload = await doVerifyCanvaToken(token, app.app_id, env);
                    // Add source field to indicate it's from Canva
                    return {
                        ...canvaPayload,
                        source: 'canva'
                    };
                } catch (error) {
                    if (error instanceof Error && error.message === 'Token has expired') {
                        return new Response(JSON.stringify({
                            error: 'Token has expired',
                            code: 'TOKEN_EXPIRED'
                        }), {
                            status: 401,
                            headers: { 'Content-Type': 'application/json' }
                        });
                    }
                    throw error;
                }
            }
        }

        throw new TokenValidationError('Invalid token audience', 'INVALID_AUDIENCE');
    } catch (error) {
        console.error('Token verification error:', error);
        
        if (error instanceof TokenValidationError) {
            return new Response(JSON.stringify({
                error: error.message,
                code: error.code
            }), {
                status: 401,
                headers: { 'Content-Type': 'application/json' }
            });
        }

        return new Response(JSON.stringify({
            error: 'Token verification failed',
            code: 'VERIFICATION_FAILED'
        }), {
            status: 401,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}
