import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../../types';
import { JwksClient } from './JwksClient';
import { decode, verify } from 'hono/jwt';
import { JWTPayload } from 'hono/utils/jwt/types';

const CACHE_EXPIRY_MS = 5 * 60 * 1_000; // 5 minutes

async function doGetCanvaActivePublicKey({ appId, token, cacheHandler }: { appId: string; token: string; cacheHandler: CacheHandler }) {
	const decoded = decode(token);

	if (!decoded) {
		throw new Error('Invalid token');
	}

	const { kid } = decoded.header as any;

	const jwks = new JwksClient({
		jwksUri: `https://api.canva.com/rest/v1/apps/${appId}/jwks`,
		cacheHandler,
	});

	const key = await jwks.getSigningKey(kid);
	return key.getPublicKey();
}

export async function doVerifyCanvaToken(token: string, appId: string, env: Env): Promise<JWTPayload> {
	function cacheFactory(env: Env) {
		const prefix = 'PUB_KEY_' + appId + '_';
		return {
			get(key: string) {
				return env.MY_KV_NAMESPACE.get(prefix + key);
			},
			set(key: string, value: string, ttl = CACHE_EXPIRY_MS) {
				return env.MY_KV_NAMESPACE.put(prefix + key, value, { expirationTtl: ttl });
			},
			del(key: string) {
				return env.MY_KV_NAMESPACE.delete(prefix + key);
			},
		};
	}
	try {
		const publicKey = await doGetCanvaActivePublicKey({
			appId,
			token,
			cacheHandler: cacheFactory(env),
		});
		const verified = await verify(token, publicKey, 'RS256');
		return verified;
	} catch (error) {
		console.info('Token verification failed:', error);
		if (error instanceof Error && error.name === 'JwtTokenExpired') {
			throw new Error('Token has expired');
		}
		throw error;
	}
}
