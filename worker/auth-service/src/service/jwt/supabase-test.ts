const { SignJWT, jwtVerify } = require('jose');
const { createSecretKey } = require('crypto');
const crypto = require('crypto');

/**
 * @typedef {Object} TokenPayload
 * @property {string} sub
 * @property {string} aud
 * @property {number} [exp]
 * @property {number} [iat]
 * @property {string} email
 * @property {string} [phone]
 * @property {Object} app_metadata
 * @property {string} app_metadata.provider
 * @property {string[]} [app_metadata.providers]
 * @property {Object} user_metadata
 * @property {string} [user_metadata.avatar_url]
 * @property {string} [user_metadata.email]
 * @property {boolean} [user_metadata.email_verified]
 * @property {string} [user_metadata.full_name]
 * @property {string} [user_metadata.iss]
 * @property {string} [user_metadata.name]
 * @property {boolean} [user_metadata.phone_verified]
 * @property {string} [user_metadata.picture]
 * @property {string} [user_metadata.provider_id]
 * @property {string} [user_metadata.sub]
 * @property {string} role
 * @property {string} [aal]
 * @property {Array<{method: string, timestamp: number}>} [amr]
 * @property {string} [session_id]
 * @property {boolean} [is_anonymous]
 */

/**
 * @param {string} userId
 * @param {string} jwtSecret
 * @param {string} email
 * @param {string} [expiresIn='1h']
 * @returns {Promise<string>}
 */
async function generateSupabaseToken(userId, jwtSecret, email, expiresIn = '24h') {
  const secretKey = createSecretKey(Buffer.from(jwtSecret, 'utf-8'));

  /** @type {TokenPayload} */
  const payload = {
    aud: 'authenticated',
    sub: userId,
    email: email,
    app_metadata: {
      provider: 'google',
      providers: ['google']
    },
    user_metadata: {
      avatar_url: 'https://lh3.googleusercontent.com/a/ACg8ocJ9Qe2O2zLaeIIBCPTMarLyZ4c5UUjsJERM10GSor6Pt5q_vd4=s96-c',
      email: email,
      email_verified: true,
      full_name: 'Deniffer Alan',
      name: 'Deniffer Alan',
      phone_verified: false,
      picture: 'https://lh3.googleusercontent.com/a/ACg8ocJ9Qe2O2zLaeIIBCPTMarLyZ4c5UUjsJERM10GSor6Pt5q_vd4=s96-c',
      provider_id: '113346134363726229934',
      sub: '113346134363726229934'
    },
    role: 'authenticated',
    aal: 'aal1',
    amr: [
      {
        method: 'oauth',
        timestamp: Math.floor(Date.now() / 1000)
      }
    ],
    session_id: crypto.randomUUID(),
    is_anonymous: false
  };

  const jwt = await new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256', type: 'JWT' })
    .setExpirationTime(expiresIn)
    .setIssuedAt()
    .sign(secretKey);

  return jwt;
}

/**
 * @param {string} token
 * @param {string} jwtSecret
 * @returns {Promise<TokenPayload>}
 */
async function parseSupabaseToken(token, jwtSecret) {
  const secretKey = createSecretKey(Buffer.from(jwtSecret, 'utf-8'));

  try {
    const { payload } = await jwtVerify(token, secretKey);
    return payload;
  } catch (error) {
    console.error('Error parsing token:', error);
    throw error;
  }
}

async function main() {
  const jwtSecret = 'hJvNxzZhyNAPX+20iVkWLc9p3MYrCZ8FXcBXDXXSQ32k2JvcrMWU2U57z8dp58emfllv4VKwZiWZg6GhqmORAQ==';
  const userId = '3ab95a1a-52a2-4f0d-96ab-49eb9eb18f11';
  const email = '<EMAIL>';

  try {
    const token = await generateSupabaseToken(userId, jwtSecret, email);
    console.log('Generated Supabase token:', token);

    const parsedPayload = await parseSupabaseToken(token, jwtSecret);
    console.log('Parsed token payload:', parsedPayload);
  } catch (error) {
    console.error('Error:', error);
  }
}

main();
