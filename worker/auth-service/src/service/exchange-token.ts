import { doWebTokenExchange } from "./jwt";

export async function doExchangeToken(token: string, env: Env): Promise<string> {

        try {
                const newToken = await doWebTokenExchange(token, env);
                return newToken;
        } catch (error: any) {
                console.error('Token exchange failed:', error);
                throw new Error(`Error: ${error}`);
        }
}