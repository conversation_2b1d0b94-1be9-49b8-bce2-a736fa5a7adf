export interface A1dUser {
        uid?: string;
        account_id?: string;
        created_at?: Date;
        updated_at?: Date;
}

export interface ApiKey {
        id: string;
        uid: string;
        api_key: string;
        scope: string,
        status: string,
        name?:string,
        account_id: string,
        deleted_at?: Date,
        created_at?: Date;
        updated_at?: Date;
}