import { createClient, SupabaseClient } from '@supabase/supabase-js'
import { A1dUser, ApiKey } from './models';
const A1D_USER_TABLE = 'a1d_user';
const CANVA_APP_TABLE = 'canva_app';
const API_KEY_TABLE = 'api_key';

let supabaseClient: SupabaseClient | null = null;

function getClient(env: Env): SupabaseClient {
    if (!supabaseClient) {
        supabaseClient = createClient(env.SUPABASE_URL, env.SUPABASE_SERVICE_ROLE_KEY);
    }
    return supabaseClient;
}

// 插入a1d_user
export async function doInsertA1dUser(user: A1dUser, env: Env) {
    try {
        const { data, error } = await getClient(env)
            .from(A1D_USER_TABLE)
            .insert(user)
            .select()
            .single();

        if (error) throw error;
        return data;
    } catch (error) {
        console.error("Error inserting user into Supabase:", error);
        throw error;
    }
}


// 更新 a1d_user
export async function doUpdateA1dUser(user: A1dUser, env: Env) {
    try {
        const { data, error } = await getClient(env)
            .from(A1D_USER_TABLE)
            .update(user)
            .eq('uid', user.uid)
            .select()
            .single();

        if (error) throw error;
        return data;
    } catch (error) {
        console.error("Error updating user in Supabase:", error);
        throw error;
    }
}

// 查询函数
export async function doGetA1dUser(
    env: Env,
    uid?: string,
    accountId?: string
) {
    try {
        let query = getClient(env)
            .from(A1D_USER_TABLE)
            .select('*');

        if (uid) {
            query = query.eq('uid', uid);
        }
        if (accountId) {
            query = query.eq('account_id', accountId);
        }

        const { data, error } = await query.maybeSingle();

        if (error) throw error;

        return data || null;
    } catch (error) {
        console.error("Error retrieving user from Supabase:", error);
        throw error;
    }
}


// 查询函数
export async function doGetA1dKey(
    env: Env,
    uid?: string,
    apiKey?: string
) {
    try {
        let query = getClient(env)
            .from(API_KEY_TABLE)
            .select('*');

        if (uid) {
            query = query.eq('uid', uid);
        }
        if (apiKey) {
            query = query.eq('api_key', apiKey);
        }

        const { data, error } = await query.maybeSingle();

        if (error) throw error;

        return data || null;
    } catch (error) {
        console.error("Error retrieving user from Supabase:", error);
        throw error;
    }
}


// 插入a1d_user
export async function doInsertApiKeyInDB(apiKey: ApiKey, env: Env): Promise<ApiKey> {
    try {
        const { data, error } = await getClient(env)
            .from(API_KEY_TABLE)
            .insert(apiKey)
            .select()
            .single();

        if (error) throw error;
        return data as ApiKey;
    } catch (error) {
        console.error("Error inserting API key into Supabase:", error);
        throw error;
    }
}

// 更新 api_key
export async function doUpdateApiKeyInDB(apiKey: ApiKey, env: Env): Promise<ApiKey> {
    try {
        // Collect fields that need to be updated
        const updateFields = {
            ...(apiKey.name !== undefined && { name: apiKey.name }),
            ...(apiKey.status !== undefined && { status: apiKey.status }),
            ...(apiKey.deleted_at !== undefined && { deleted_at: apiKey.deleted_at }),
        };

        // If no fields to update, return empty object
        if (Object.keys(updateFields).length === 0) {
            return {} as ApiKey;
        }

        // Add updated_at and perform update
        const updateData = {
            ...updateFields,
            updated_at: new Date(),
        };

        const { data, error } = await getClient(env)
            .from(API_KEY_TABLE)
            .update(updateData)
            .eq('id', apiKey.id)
            .eq('uid', apiKey.uid)
            .select()
            .single();

        if (error) throw error;
        return data;
    } catch (error) {
        console.error("Error updating API key in Supabase:", error);
        throw error;
    }
}

// 查询 api_key
export async function doGetApiKeysInDB(env: Env, uid: string): Promise<ApiKey[]> {
    try {
        const { data, error } = await getClient(env)
            .from(API_KEY_TABLE)
            .select('*')
            .eq('uid', uid)
            .eq('status', 'ACTIVE')
            .is('deleted_at', null);

        if (error) throw error;
        return data || [];
    } catch (error) {
        console.error("Error fetching API keys from Supabase:", error);
        throw error;
    }
}

// 查询 canva_app
export async function doGetCanvaApp(env: Env, appId: string) {
    try {
        const { data, error } = await getClient(env).from(CANVA_APP_TABLE).select('*').eq('app_id', appId).maybeSingle();
        if (error) throw error;
        return data;
    } catch (error) {
        console.error("Error retrieving canva app from Supabase:", error);
        throw error;
    }
}
