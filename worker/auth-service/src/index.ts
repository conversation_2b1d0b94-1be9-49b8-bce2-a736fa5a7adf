/**
 * Run `npm run dev` in your terminal to start a development server
 * Open a browser tab at http://localhost:8787/ to see your worker in action
 * Run `npm run deploy` to publish your worker
 *
 * Bind resources to your worker in `wrangler.toml`.
 *
 * Learn more at https://developers.cloudflare.com/workers/
 */

import { fromHono } from 'chanfana';
import { Hono } from 'hono';
import { cors } from 'hono/cors';
// import { upgradeWebSocket } from 'hono/cloudflare-workers';
import { ExchangeToken } from './endpoints/exchange-token';
import { GenApiKey } from './endpoints/gen-api-key';
import { authMiddleware } from './middlewares';

// Star a Hono app
const app = new Hono();

app.use(
	'/api/*',
	cors({
		credentials: true,
		origin: '*',
	})
);


// Setup OpenAPI registry
const options = {
	docs_url: '/docs',
	schema: {
		info: {
			title: 'A1d Auth Worker API',
			version: '1.0',
		},
		servers: [
			{
				url: '/',
				description: 'Development server',
			},
			{
				url: 'https://auth-cf-testk-xshar.workers.dev/',
				description: 'Production server',
			},
		],
		security: [
			{
				BearerAuth: [],
			},
		],
	},
};
const openapi = fromHono(app, options);

openapi.registry.registerComponent('securitySchemes', 'BearerAuth', {
	type: 'http',
	scheme: 'bearer',
	bearerFormat: 'JWT',
});


// 注册 exchange-token 端点
openapi.post('/api/exchange-token', ExchangeToken);
openapi.post('/api/gen-api-key', authMiddleware, GenApiKey as any);

// export { TaskInfoDurableObject };

// Export the Hono app
export default app;

export { AuthService } from './service/authService';
