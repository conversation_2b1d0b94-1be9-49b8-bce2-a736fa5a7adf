# A1D Auth Service

A Cloudflare Worker-based authentication service that provides token exchange and API key management functionality.

## Overview

The A1D Auth Service is a serverless authentication service built on Cloudflare Workers. It provides two main functionalities:

1. Token Exchange: Convert third-party tokens into A1D tokens
2. API Key Management: Generate and manage API keys for users

## Authentication

### JWT Token Authentication

The service uses JWT (JSON Web Tokens) for authentication. All protected endpoints require a valid JWT token in the Authorization header.

#### Token Format
```
Authorization: Bearer <jwt_token>
```

#### Token Structure
The JWT token contains the following claims:
- `uid`: User ID
- `account_id`: Account ID
- `exp`: Expiration time
- `iat`: Issued at time
- `iss`: Issuer (A1D Auth Service)

#### Token Validation
- Tokens are signed using the `JWT_SECRET` environment variable
- Tokens must be valid and not expired
- The issuer must match the service's domain

### API Key Authentication

API keys are used for service-to-service authentication. Each API key has:
- A unique identifier
- A scope defining its permissions
- An associated user and account
- An expiration time

#### API Key Format
```
Authorization: Bearer <api_key>
```

#### API Key Scopes
- `read`: Read-only access
- `write`: Write access
- `admin`: Full administrative access

### Token Exchange Flow

1. Client sends a third-party token to `/api/exchange-token`
2. Service validates the third-party token
3. Service generates a new JWT token with user claims
4. Client receives the new JWT token
5. Client uses the JWT token for subsequent requests

### API Key Generation Flow

1. Authenticated user requests an API key via `/api/gen-api-key`
2. Service validates user's JWT token
3. Service generates a new API key with specified scope
4. API key is stored in the database
5. User receives the API key for service-to-service authentication

## API Endpoints

### 1. Exchange Token

**Endpoint:** `POST /api/exchange-token`

Exchange a third-party token for an A1D token.

#### Request Body
```json
{
    "token": "string" // The third-party token to exchange
}
```

#### Response
```json
{
    "token": "string" // The new A1D token
}
```

#### Error Responses
- 400: Invalid request
- 401: Authentication error
- 500: Server error

### 2. Generate API Key

**Endpoint:** `POST /api/gen-api-key`

Generate a new API key for a user. If the user already has an API key, it will be returned instead.

#### Query Parameters
- `scope` (optional): The scope of the API key
- `name` (optional): The name of the API key

#### Response
```json
{
    "success": true,
    "data": {
        "apiKey": "string",
        "name": "string",
        "scope": "string",
        "status": "string",
        "uid": "string",
        "account_id": "string",
        "created_at": "string",
        "updated_at": "string"
    }
}
```

#### Error Responses
- 400: Invalid request
- 401: Authentication error
- 500: Server error

## Environment Variables

The service requires the following environment variables:

- `URL_PREFIX`: Base URL for the service
- `EXPIRATION_TTL`: Token expiration time in seconds
- `JWT_SECRET`: Secret for JWT token generation
- `SUPABASE_URL`: Supabase database URL
- `SUPABASE_SERVICE_ROLE_KEY`: Supabase service role key
- `SUPABASE_JWT_SECRET`: Supabase JWT secret

## Development

### Prerequisites
- Node.js
- npm
- Wrangler CLI

### Setup
1. Install dependencies:
```bash
npm install
```

2. Configure environment variables in `wrangler.toml`

3. Start development server:
```bash
npm run dev
```

### Deployment
```bash
npm run deploy
```

## Testing

The service uses Vitest for testing. Tests are located in the `src` directory with the `.test.ts` extension.

### Running Tests
```bash
# Run tests in watch mode
npm test

# Run tests with coverage
npm run test:coverage
```

### Test Structure
- Tests are organized by endpoint
- Each test file includes:
  - Mock setup for dependencies
  - Test cases for successful operations
  - Test cases for error handling
  - Environment variable mocking

### Test Coverage
- The test suite aims to cover:
  - Endpoint functionality
  - Error handling
  - Input validation
  - Service integration

## Security

- All endpoints are protected with CORS
- API key generation requires authentication
- Tokens are validated before exchange
- Sensitive data is stored securely in Supabase
- JWT tokens are signed with a secure secret
- API keys are stored with proper scoping and access controls
- All authentication errors are properly handled and logged

## Error Handling

The service provides detailed error messages for:
- Invalid requests
- Authentication failures
- Server errors

All errors are logged for debugging purposes. 