import { beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest'

// Auth Service specific test setup
beforeAll(async () => {
  // Setup for Auth service tests
})

afterAll(async () => {
  // Cleanup for Auth service tests
})

beforeEach(async () => {
  // Setup before each Auth service test
})

afterEach(async () => {
  // Cleanup after each Auth service test
})

// Mock KV namespace
export const mockKV = {
  get: vi.fn(),
  put: vi.fn(),
  delete: vi.fn(),
  list: vi.fn(),
}

// Mock Supabase client
export const mockSupabase = {
  from: vi.fn(() => ({
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    single: vi.fn(),
    then: vi.fn(),
  })),
}

export function createMockAuthEnv(overrides: Record<string, any> = {}) {
  return {
    JWT_SECRET: 'test-secret',
    SUPABASE_URL: 'https://test.supabase.co',
    SUPABASE_SERVICE_ROLE_KEY: 'test-key',
    SUPABASE_JWT_SECRET: 'test-jwt-secret',
    EXPIRATION_TTL: 3600,
    MY_KV_NAMESPACE: mockKV,
    ...overrides,
  }
}