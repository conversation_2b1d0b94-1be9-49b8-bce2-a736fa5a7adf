// Generated by Wrangler on Fri Jul 26 2024 22:11:06 GMT+0800 (China Standard Time)
// by running `wrangler types`

interface Env {
	ENV: string;
	MY_KV_NAMESPACE: KVNamespace;
	AUTH_SERVICE: Fetcher;
	FAL_API_KEY: string;
	MY_BUCKET: R2Bucket;
	CF_ACCOUNT_ID: string;
	S3_BUCKET: 'a1d-api-test';
	S3_ACCESS_KEY_ID: string;
	S3_SECRET_ACCESS_KEY: string;
	FIXED_SIGN: string;
	RMBG_API_URL: string;
	RMBG_TOKEN: string;
	SUPABASE_URL: string;
	SUPABASE_SERVICE_ROLE_KEY: string;
	JWT_SECRET: string;
	SUPABASE_JWT_SECRET: string;
	URL_PREFIX: string;
	TASK_QUEUE: Queue;
	ADMIN_KEY: string;
	'302_API_KEY': string;
	R2_PUBLIC_DOMAIN: string;
	VIDEO_UPSCALER_API_URL: string;
	VIDEO_UPSCALER_TOKEN: string;
	POSTHOG_API_HOST?: string;
	POSTHOG_ASSET_HOST?: string;
}
