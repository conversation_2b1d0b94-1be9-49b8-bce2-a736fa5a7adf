#:schema node_modules/wrangler/config-schema.json
name = "a1d-api-test"
main = "src/index.ts"
compatibility_date = "2024-11-11"
compatibility_flags = ["nodejs_compat"]
tail_consumers = [{ service = "a1d-api-test-tail" }]

# Automatically place your workloads in an optimal location to minimize latency.
# If you are running back-end logic in a Worker, running it closer to your back-end infrastructure
# rather than the end user may result in better performance.
# Docs: https://developers.cloudflare.com/workers/configuration/smart-placement/#smart-placement
# [placement]
# mode = "smart"

# Variable bindings. These are arbitrary, plaintext strings (similar to environment variables)
# Docs:
# - https://developers.cloudflare.com/workers/wrangler/configuration/#environment-variables
# Note: Use secrets to store sensitive data.
# - https://developers.cloudflare.com/workers/configuration/secrets/
# [vars]
# BUCKET_NAME = "a1d-relight-prod"
# EXPIRATION_TTL = 3600

# Bind the Workers AI model catalog. Run machine learning models, powered by serverless GPUs, on Cloudflare's global network
# Docs: https://developers.cloudflare.com/workers/wrangler/configuration/#workers-ai
# [ai]
# binding = "AI"

# Bind an Analytics Engine dataset. Use Analytics Engine to write analytics within your Pages Function.
# Docs: https://developers.cloudflare.com/workers/wrangler/configuration/#analytics-engine-datasets
#[[analytics_engine_datasets]]
#binding = "BILLING"

# Bind a headless browser instance running on Cloudflare's global network.
# Docs: https://developers.cloudflare.com/workers/wrangler/configuration/#browser-rendering
# [browser]
# binding = "MY_BROWSER"

# Bind a D1 database. D1 is Cloudflare's native serverless SQL database.
# Docs: https://developers.cloudflare.com/workers/wrangler/configuration/#d1-databases
# [[d1_databases]]
# binding = "MY_DB"
# database_name = "my-database"
# database_id = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"

# Bind a dispatch namespace. Use Workers for Platforms to deploy serverless functions programmatically on behalf of your customers.
# Docs: https://developers.cloudflare.com/workers/wrangler/configuration/#dispatch-namespace-bindings-workers-for-platforms
# [[dispatch_namespaces]]
# binding = "MY_DISPATCHER"
# namespace = "my-namespace"

# Bind a Durable Object. Durable objects are a scale-to-zero compute primitive based on the actor model.
# Durable Objects can live for as long as needed. Use these when you need a long-running "server", such as in realtime apps.
# Docs: https://developers.cloudflare.com/workers/wrangler/configuration/#durable-objects
# [[durable_objects.bindings]]
# name = "TASK_INFO_DURABLE_OBJECT"
# class_name = "TaskInfoDurableObject"

# Durable Object migrations.
# Docs: https://developers.cloudflare.com/workers/wrangler/configuration/#migrations
# [[migrations]]
# tag = "v1"
# new_classes = ["TaskInfoDurableObject"]

# Bind a Hyperdrive configuration. Use to accelerate access to your existing databases from Cloudflare Workers.
# Docs: https://developers.cloudflare.com/workers/wrangler/configuration/#hyperdrive
# [[hyperdrive]]
# binding = "MY_HYPERDRIVE"
# id = "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"

# Bind a KV Namespace. Use KV as persistent storage for small key-value pairs.
# Docs: https://developers.cloudflare.com/workers/wrangler/configuration/#kv-namespaces
[[kv_namespaces]]
binding = "MY_KV_NAMESPACE"
id = "419e916eb83b48cebce6d6b9df832428"

# Bind an mTLS certificate. Use to present a client certificate when communicating with another service.
# Docs: https://developers.cloudflare.com/workers/wrangler/configuration/#mtls-certificates
# [[mtls_certificates]]
# binding = "MY_CERTIFICATE"
# certificate_id = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"

# Bind a Queue producer. Use this binding to schedule an arbitrary task that may be processed later by a Queue consumer.
# Docs: https://developers.cloudflare.com/workers/wrangler/configuration/#queues
[[queues.producers]]
binding = "TASK_QUEUE"
queue = "a1d-api-test"
max_batch_size = 5  # 设置批处理大小为 5 条消息
max_batch_timeout = 1  # 设置批处理超时时间为 5 秒，也就是如果 5s 内的消息没有满足 max_batch_size 的条数，就触发批处理

# Bind a Queue consumer. Queue Consumers can retrieve tasks scheduled by Producers to act on them.
# Docs: https://developers.cloudflare.com/workers/wrangler/configuration/#queues
# !!! WARNING !!! 1200 requests / 5 mins
[[queues.consumers]]
queue = "a1d-api-test"
# # Optional
# visibility_timeout_ms = 600000
# max_retries = 2
# dead_letter_queue = "a1d-iu-dead"
# # Optional
# visibility_timeout_ms = 10000
max_retries = 3
retry_delay = 1

# Bind an R2 Bucket. Use R2 to store arbitrarily large blobs of data, such as files.
# Docs: https://developers.cloudflare.com/workers/wrangler/configuration/#r2-buckets
[[r2_buckets]]
binding = "MY_BUCKET"
bucket_name = "a1d-api-test"

# Bind another Worker service. Use this binding to call another Worker without network overhead.
# Docs: https://developers.cloudflare.com/workers/wrangler/configuration/#service-bindings
# [[services]]
# binding = "MY_SERVICE"
# service = "my-service"

# Bind a Vectorize index. Use to store and query vector embeddings for semantic search, classification and other vector search use-cases.
# Docs: https://developers.cloudflare.com/workers/wrangler/configuration/#vectorize-indexes
# [[vectorize]]
# binding = "MY_INDEX"
# index_name = "my-index"


[[services]]
binding = "AUTH_SERVICE"
service = "a1d-auth-test"
entrypoint = "AuthService"

[vars]
ENV="test"
EXPIRATION_TTL=3600
URL_PREFIX="https://a1d.ai"
CF_ACCOUNT_ID="2da2eb61458c5f71b03602c372d3f703"
S3_BUCKET="a1d-api-test"
S3_ACCESS_KEY_ID="eaf9b1777e306c3579caddb2e87b4ec9"
RMBG_API_URL="https://app-rb.a1d.ai"
R2_PUBLIC_DOMAIN="https://api-test-storage.a1d.ai"

SUPABASE_URL="https://pvbnuwdjsqicesdaprhp.supabase.co"

# Video Upscaler API 配置
VIDEO_UPSCALER_API_URL="https://app-vu-2.a1d.ai/api/task"

# PostHog Analytics Proxy 配置
POSTHOG_API_HOST="us.i.posthog.com"
POSTHOG_ASSET_HOST="us-assets.i.posthog.com"

# wrangler.toml (wrangler v3.88.0^)
[observability]
enabled = true
head_sampling_rate = 0.2


[triggers]
crons = [
    "0 1 * * *",  # 每天早上 9 点执行任务分析
    "*/5 * * * *" # 每 5 分钟执行一次任务清理
] 