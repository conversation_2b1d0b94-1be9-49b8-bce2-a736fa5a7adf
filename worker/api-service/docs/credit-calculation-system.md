# 信用计算系统文档

## 系统概述

信用计算系统是 A1D API 服务的核心组件，负责计算用户执行各种 AI 任务所需的信用点数。系统支持多种应用类型，并提供动态计算规则来根据任务参数精确计算信用消耗。

## 系统架构

```mermaid
graph TB
    A[API请求] --> B[calculate-credits 接口]
    C[任务请求] --> D[creditEnoughMiddleware 中间件]
    
    B --> E[doCalculateCredits 服务]
    D --> F[doGetNeededCredits 函数]
    
    E --> G[doCalculateSingleTaskCredits]
    F --> G
    
    G --> H[doGetCreditRule 数据库查询]
    G --> I[doCalculateDynamicCredits 动态计算]
    
    H --> J[(credit_rules 表)]
    I --> K[特定应用计算逻辑]
    
    K --> L[返回所需信用点数]
```

## 核心组件

### 1. API 接口层
- **文件**: `src/endpoints/calculate-credits.ts`
- **功能**: 提供 `/api/calculate-credits` 接口，允许批量计算任务信用点数
- **用途**: 前端在提交任务前预估信用消耗

### 2. 中间件层
- **文件**: `src/middlewares/credit.ts`
- **功能**: `creditEnoughMiddleware` 中间件，在任务执行前检查用户信用余额
- **用途**: 确保用户有足够信用点数执行任务

### 3. 服务层
- **文件**: `src/service/credit/calculate-credits.ts`
- **功能**: 核心信用计算逻辑，支持单个和批量任务计算
- **用途**: 提供统一的信用计算服务

### 4. 动态计算器
- **文件**: `src/service/credit/dynamic-calculator.ts`
- **功能**: 根据不同应用类型和参数进行动态信用计算
- **用途**: 处理复杂的参数化信用计算逻辑

## 支持的应用类型

### 基础应用
| 应用代码 | 应用名称 | 参数要求 | 计算方式 |
|---------|---------|---------|---------|
| `iu` | 图片放大器 | `scale: number` | 基于放大倍数的动态计算 |
| `sp` | 速绘 | `sketchDuration?: number, colorFillDuration?: number` | 基于时长的动态计算 |
| `vu` | 视频放大器 | `duration?: number, quality?: string` | 基于时长和质量的计算 |
| `remove-bg` | 移除背景 | 无特殊参数 | 固定信用点数 |
| `image-vectorization` | 图片矢量化 | 无特殊参数 | 固定信用点数 |
| `image-extends` | 图片扩展 | 无特殊参数 | 固定信用点数 |
| `text-behind-image` | 文字后置 | 无特殊参数 | 固定信用点数 |
| `image-generator` | 图片生成器 | `prompt?: string` | 固定信用点数 |

### 动态计算规则

#### 图片放大器 (iu)
```typescript
// 配置示例
{
  "scale_multiplier": {
    "2": 1,    // 2倍放大: 1 信用点
    "4": 2,    // 4倍放大: 2 信用点
    "8": 4,    // 8倍放大: 4 信用点
    "16": 8    // 16倍放大: 8 信用点
  }
}
```

#### 速绘 (sp)
```typescript
// 配置示例
{
  "duration_multiplier": 0.5  // 每秒 0.5 信用点
}

// 计算公式: (sketchDuration + colorFillDuration) * duration_multiplier
// 默认值: sketchDuration = 3, colorFillDuration = 3
```

## API 使用方法

### 1. 计算信用接口

**请求路径**: `POST /api/calculate-credits`

**请求格式**:
```json
{
  "data": [
    {
      "app": "iu",
      "source": "web",
      "scale": 4
    },
    {
      "app": "sp",
      "source": "web",
      "sketchDuration": 5,
      "colorFillDuration": 3
    }
  ]
}
```

**响应格式**:
```json
{
  "needCredits": 6
}
```

### 2. 支持的参数

每个任务都包含以下基础字段：
- `app`: 应用类型（必填）
- `source`: 来源渠道（可选，默认为 "web"）

根据不同应用类型，还需要提供特定参数：

**图片放大器**:
```json
{
  "app": "iu",
  "scale": 2  // 放大倍数: 2, 4, 8, 16
}
```

**速绘**:
```json
{
  "app": "sp",
  "sketchDuration": 3,      // 可选，默认 3 秒
  "colorFillDuration": 3    // 可选，默认 3 秒
}
```

**视频放大器**:
```json
{
  "app": "vu",
  "duration": 60,     // 可选，视频时长（秒）
  "quality": "1080P"  // 可选，视频质量
}
```

## 工作流程

### 1. 预计算流程
1. 用户在前端选择任务参数
2. 前端调用 `/api/calculate-credits` 接口
3. 系统返回所需信用点数
4. 前端显示信用消耗预估

### 2. 任务执行流程
1. 用户提交任务请求
2. `creditEnoughMiddleware` 中间件拦截请求
3. 查询用户当前信用余额
4. 计算任务所需信用点数
5. 比较余额与需求，决定是否允许执行
6. 执行任务或返回信用不足错误

### 3. 信用计算流程
1. 根据 `app` 和 `source` 查询数据库中的信用规则
2. 检查是否有动态计算规则
3. 如果有动态规则，根据任务参数进行计算
4. 如果没有动态规则，使用基础信用点数
5. 返回最终计算结果

## 数据库设计

### credit_rules 表结构
```sql
CREATE TABLE credit_rules (
  id SERIAL PRIMARY KEY,
  app VARCHAR(50) NOT NULL,           -- 应用类型
  source VARCHAR(50) NOT NULL,        -- 来源渠道
  base_credits INTEGER NOT NULL,      -- 基础信用点数
  has_dynamic_rule BOOLEAN DEFAULT FALSE,  -- 是否有动态规则
  dynamic_rule_config JSONB,          -- 动态规则配置
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(app, source)
);
```

### 配置示例
```sql
-- 图片放大器动态规则
INSERT INTO credit_rules (app, source, base_credits, has_dynamic_rule, dynamic_rule_config) 
VALUES (
  'iu', 
  'web', 
  1, 
  true, 
  '{"scale_multiplier": {"2": 1, "4": 2, "8": 4, "16": 8}}'
);

-- 速绘动态规则
INSERT INTO credit_rules (app, source, base_credits, has_dynamic_rule, dynamic_rule_config)
VALUES (
  'sp',
  'web',
  3,
  true,
  '{"duration_multiplier": 0.5}'
);

-- 固定信用规则
INSERT INTO credit_rules (app, source, base_credits, has_dynamic_rule)
VALUES ('remove-bg', 'web', 1, false);
```

## 错误处理

### 常见错误类型
1. **无效应用类型**: 不支持的 app 值
2. **缺少必要参数**: 动态计算缺少必要参数（如 scale）
3. **无效参数值**: 参数值超出支持范围
4. **数据库错误**: 查询信用规则失败
5. **信用不足**: 用户余额不足以执行任务

### 错误响应格式
```json
{
  "error": "Invalid scale value: 3. Supported scales: 2, 4, 8, 16"
}
```

## 扩展性设计

### 添加新应用类型
1. 在 `TaskItemSchema` 中添加新的 discriminated union 分支
2. 在 `dynamic-calculator.ts` 中添加对应的计算逻辑
3. 在数据库中插入相应的信用规则

### 添加新的动态规则
1. 在数据库的 `dynamic_rule_config` 字段中配置新规则
2. 在 `doCalculateDynamicCredits` 函数中添加处理逻辑
3. 更新相关的 TypeScript 类型定义

## 性能优化

1. **数据库索引**: 在 `(app, source)` 上创建唯一索引
2. **缓存策略**: 可考虑将信用规则缓存到 KV 存储
3. **批量计算**: 支持一次性计算多个任务的信用需求
4. **异步处理**: 大批量计算可考虑异步处理

## 监控和日志

系统在关键节点添加了详细的日志记录：
- 信用计算请求和结果
- 动态规则匹配情况
- 错误和异常信息
- 性能指标（计算耗时等）

这些日志有助于系统监控、问题排查和性能优化。 