# Fal API 重构总结

## 概述

为了减少代码重复并提高可维护性，我们将三个使用 fal.ai API 的服务文件中的共同逻辑抽象到了一个通用的工具类中。

## 重构的文件

### 新增文件
- `src/utils/fal-api.ts` - 通用的 fal API 工具类

### 重构的文件
- `src/service/app/image-vectorization.ts` - 图片矢量化服务
- `src/service/app/image-upscaler.ts` - 图片放大服务 (Clarity Upscaler)
- `src/service/app/image-extends.ts` - 图片扩展服务 (Ideogram)
- `src/service/app/image-generator.ts` - 图片生成服务 (Recraft V3)

## FalApiClient 工具类

### 主要功能

1. **统一的任务创建** - `doCreateTask()`
2. **统一的任务状态查询** - `doGetTaskStatus()`
3. **端点 URL 构建** - `buildEndpoint()`
4. **预定义的端点常量** - `FAL_ENDPOINTS`

### 核心接口

```typescript
// 创建任务选项
interface FalCreateTaskOptions {
  endpoint: string;
  apiKey: string;
  payload: Record<string, any>;
  taskName?: string;
  initialStatus?: TaskStatus;
}

// 获取任务状态选项
interface FalGetTaskOptions {
  endpoint: string;
  apiKey: string;
  taskId: string;
  taskName?: string;
  resultProcessor?: (result: any) => TaskInfoResult;
}
```

### 使用示例

#### 创建任务
```typescript
return await FalApiClient.doCreateTask({
  endpoint: FalApiClient.buildEndpoint(FAL_ENDPOINTS.IMAGE2SVG),
  apiKey: env.FAL_API_KEY,
  payload: inputData,
  taskName: 'Image Vectorization',
  initialStatus: TaskStatus.PROCESSING
});
```

#### 获取任务状态
```typescript
return await FalApiClient.doGetTaskStatus({
  endpoint: FalApiClient.buildEndpoint(FAL_ENDPOINTS.IMAGE2SVG),
  apiKey: env.FAL_API_KEY,
  taskId: taskId,
  taskName: 'Image Vectorization',
  resultProcessor: customResultProcessor // 可选的自定义结果处理器
});
```

## 支持的端点

```typescript
export const FAL_ENDPOINTS = {
  CLARITY_UPSCALER: 'clarity-upscaler',
  IMAGE2SVG: 'image2svg',
  IDEOGRAM_V3_REFRAME: 'ideogram/v3/reframe',
  IDEOGRAM: 'ideogram',
  RECRAFT_V3: 'recraft-v3'
} as const;
```

## 自定义结果处理器

对于有特殊响应格式或状态映射需求的服务，可以提供自定义的结果处理器：

```typescript
const resultProcessor = (result: any): TaskInfoResult => {
  // 自定义的结果处理逻辑
  if (result.images && result.images.length > 0) {
    return {
      taskId: taskId,
      status: TaskStatus.FINISHED,
      imageUrl: result.images[0].url,
      mimeType: result.images[0].content_type
    };
  }
  
  return {
    taskId: taskId,
    status: TaskStatus.PROCESSING
  };
};
```

## 重构带来的好处

1. **代码复用** - 消除了重复的 HTTP 请求和错误处理逻辑
2. **统一的错误处理** - 所有 fal API 调用都使用相同的错误处理模式
3. **更好的可维护性** - 修改 fal API 调用逻辑只需要在一个地方进行
4. **类型安全** - 提供了完整的 TypeScript 类型定义
5. **灵活性** - 支持自定义结果处理器来处理特殊情况
6. **一致的日志记录** - 统一的日志格式和错误信息

## 迁移指南

如果需要添加新的 fal API 服务：

1. 在 `FAL_ENDPOINTS` 中添加新的端点常量
2. 使用 `FalApiClient.doCreateTask()` 创建任务
3. 使用 `FalApiClient.doGetTaskStatus()` 查询任务状态
4. 如果有特殊的响应格式，提供自定义的 `resultProcessor`

## 注意事项

- 所有的 fal API 调用都需要提供有效的 API key
- 自定义结果处理器应该处理所有可能的响应状态
- 错误处理已经内置在工具类中，但特殊情况仍需要在结果处理器中处理 