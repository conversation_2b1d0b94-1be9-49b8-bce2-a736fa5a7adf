// Task
export enum TaskStatus {
	FAILED = 'FAILED',
	WAITING = 'WAITING',
	PROCESSING = 'PROCESSING',
	FINISHED = 'FINISHED',
	SUCCESS = 'SUCCESS',
	UNKNOWN = 'UNKNOWN',
	CANCEL = 'CANCEL',
	ERROR = 'ERROR',
	INIT = 'INIT'
}

export type TaskInfoRequest = {
	taskId: string;
	source: string;
	uid: string;
	app: string;
	status: TaskStatus;
	accountId: string;
	params: Record<string, unknown>;
};

export type TaskInfoResult = {
	taskId: string;
	status: TaskStatus;
	mimeType?: string;
	imageUrl?: string;
	thumbUrl?: string;
	duration?: number;
	videoUrl?: string;
	sketchImageUrl?: string;
	colorImageUrl?: string;
	imageUrls?: string[];
	message?: string;
	nsfw?: boolean;
	error?: any;
}

export interface TaskQueueMessage {
	taskId: string;
	uid?: string;
	app?: string;
	credit?: number;
	source?: string;
	inputParams?: any;
	result?: any;
	error?: any;
	status?: TaskStatus;
	accountId?: string;
}