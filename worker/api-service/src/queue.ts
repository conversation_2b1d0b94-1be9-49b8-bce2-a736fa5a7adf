import { MessageBatch } from '@cloudflare/workers-types';
import { doPreDeductCredit } from './service/credit/service';
import { TaskStatus, TaskQueueMessage, TaskInfoResult } from './model/task';
import { APP } from './types';
import { doGetTask, doInsertTask, doUpdateTask, Task } from './service/supabase/task';
import { isTerminalState } from './utils/task-utils';
import { doUpdateTaskStatus } from './service/task/create-task';
import { UrlProcessor } from './utils/url-processor';

/**
 * 任务处理工具类 - 负责处理任务的输入参数和结果
 */
class TaskProcessor {
  /**
   * 处理任务输入参数中的URL
   */
  static async processInputParams(task: TaskQueueMessage, env: Env): Promise<Record<string, any> | null> {
    console.log('Processing input_params for task:', task);
    const inputParams = task.inputParams;

    // 如果没有输入参数，返回null
    if (!inputParams) {
      return null;
    }

    let modified = false;
    const processedParams = { ...inputParams };

    // 处理图片URL
    if (inputParams.imageUrl && !inputParams.originalImageUrl) {
      const newUrl = await UrlProcessor.processUrlField(env, inputParams.imageUrl, 'image', task.app);
      if (newUrl) {
        processedParams.originalImageUrl = inputParams.imageUrl;
        processedParams.imageUrl = newUrl;
        modified = true;
      }
    }

    // 处理视频URL
    if (inputParams.videoUrl && !inputParams.originalVideoUrl) {
      const newUrl = await UrlProcessor.processUrlField(env, inputParams.videoUrl, 'video', task.app);
      if (newUrl) {
        processedParams.originalVideoUrl = inputParams.videoUrl;
        processedParams.videoUrl = newUrl;
        modified = true;
      }
    }

    return modified ? processedParams : null;
  }

  /**
   * 处理任务结果中的URL，使用统一的 UrlProcessor 方法
   */
  static async processTaskResult(task: TaskInfoResult, env: Env, app?: string): Promise<Partial<TaskInfoResult> | null> {
    console.log('Processing result URLs for task:', task);

    // 只处理终态任务的结果
    if (!isTerminalState(task.status as TaskStatus)) {
      return null;
    }

    // 使用统一的URL处理方法
    const processedTask = await UrlProcessor.processTaskResultWithUrlConversion(task, env, app);

    // 提取变化的部分
    const changes: Partial<TaskInfoResult> = {};
    const urlFields = ['imageUrl', 'thumbUrl', 'videoUrl', 'sketchImageUrl', 'colorImageUrl'];
    let hasChanges = false;

    for (const field of urlFields) {
      if ((processedTask as any)[field] !== (task as any)[field]) {
        (changes as any)[field] = (processedTask as any)[field];
        hasChanges = true;

        // 同时包含原始URL字段
        const originalField = `original${field.charAt(0).toUpperCase() + field.slice(1)}`;
        if ((processedTask as any)[originalField]) {
          (changes as any)[originalField] = (processedTask as any)[originalField];
        }
      }
    }
    // 处理 imageUrls 数组字段
    if (processedTask.imageUrls !== task.imageUrls) {
      changes.imageUrls = processedTask.imageUrls;
      hasChanges = true;

      // 如果有原始 imageUrls，也包含进来
      if ((processedTask as any).originalImageUrls) {
        (changes as any).originalImageUrls = (processedTask as any).originalImageUrls;
      }
    }


    return hasChanges ? changes : null;
  }
}

/**
 * 任务队列处理 - 处理批量任务消息
 */
export async function handleTaskQueue(batch: MessageBatch<TaskQueueMessage>, env: Env): Promise<void> {
  console.log(`Processing batch with ${batch.messages.length} messages`);

  for (const message of batch.messages) {
    try {
      const task = message.body;
      logTaskReceived(task);

      // 处理任务输入参数
      await processTaskInputs(task, env);

      // 检查任务是否存在
      const existingRecord = await doGetTask(env, task.taskId);
      const taskData = createTaskData(task);

      if (!existingRecord) {
        // 新任务 - 创建记录并预扣除积分
        await handleNewTask(taskData, task, env);
      } else {
        // 现有任务 - 根据状态更新
        await handleExistingTask(task, existingRecord, taskData, env);
      }

      console.log(`Task ${task.taskId} processed successfully`);
      message.ack(); // 标记消息为已处理
    } catch (error) {
      console.error('Error processing queue message:', { error, task: message.body });
      message.retry(); // 出错时重试
    }
  }
}

/**
 * 记录接收到的任务信息
 */
function logTaskReceived(task: TaskQueueMessage): void {
  console.log('Received task:', {
    taskId: task.taskId,
    uid: task.uid,
    app: task.app,
    credit: task.credit,
    source: task.source,
    status: task.status,
    hasResult: !!task.result,
    hasError: !!task.error
  });
}

/**
 * 处理任务的输入参数和结果URL
 */
async function processTaskInputs(task: TaskQueueMessage, env: Env): Promise<void> {
  // 处理输入参数中的URL
  const processedInputParams = await TaskProcessor.processInputParams(task, env);
  if (processedInputParams) {
    console.log('Modified input_params with original URLs preserved');
    task.inputParams = processedInputParams;
  }

  // 如果task自身包含URL字段（如videoUrl、sketchImageUrl等），需要先处理
  // 注意：当从SpeedPainter API直接接收结果时，结果是在task本身而不是task.result中
  const taskAsResult = task as unknown as TaskInfoResult;
  if ((taskAsResult.videoUrl || taskAsResult.imageUrl || taskAsResult.sketchImageUrl ||
    taskAsResult.colorImageUrl || taskAsResult.thumbUrl ||
    (taskAsResult.imageUrls && taskAsResult.imageUrls.length > 0)) &&
    isTerminalState(task.status as TaskStatus)) {
    console.log('Task itself contains URL fields, processing them...');
    const processedTaskUrls = await TaskProcessor.processTaskResult(taskAsResult, env, task.app);
    if (processedTaskUrls) {
      console.log('Modified task URLs with original URLs preserved');
      // 将处理后的URL合并回task
      Object.assign(task, processedTaskUrls);
    }
  }

  // 处理task.result中的URL
  if (task.result) {
    const processedUrls = await TaskProcessor.processTaskResult(task.result as TaskInfoResult, env, task.app);
    if (processedUrls) {
      console.log('Modified result URLs with original URLs preserved');
      // 合并处理后的URL到结果中，而不是替换整个结果对象
      task.result = { ...(task.result as object), ...processedUrls };
    }
  }
}

/**
 * 创建任务数据对象
 */
function createTaskData(task: TaskQueueMessage): Task {
  return {
    uid: task.uid as string,
    task_id: task.taskId,
    app: task.app as string,
    credit: task.credit || 0,
    source: task.source as string,
    status: (task.status || TaskStatus.WAITING) as string,
    result: task.result,
    error: task.error,
    input_params: task.inputParams
  };
}

/**
 * 处理新任务
 */
async function handleNewTask(taskData: Task, task: TaskQueueMessage, env: Env): Promise<void> {
  console.log('Task does not exist, inserting new record...');
  await doInsertTask(taskData, env);

  // 预扣除新任务的积分
  if (task.credit as number >= 1) {
    await deductCreditsForTask(task, env);
  }
}

/**
 * 为任务预扣除积分
 */
async function deductCreditsForTask(task: TaskQueueMessage, env: Env): Promise<void> {
  console.log('Pre-deducting credits for new task...');
  const creditResult = await doPreDeductCredit(env, {
    accountId: task.accountId as string,
    credits: task.credit as number,
    app: task.app as APP,
    taskId: task.taskId,
    uid: task.uid as string
  });

  if (!creditResult.success) {
    console.error('Failed to pre-deduct credits:', creditResult.message);
    throw new Error(`Failed to pre-deduct credits: ${creditResult.message}`);
  }
}

/**
 * 处理现有任务
 */
async function handleExistingTask(
  task: TaskQueueMessage,
  existingRecord: Task,
  taskData: Task,
  env: Env
): Promise<void> {
  if (!isTerminalState(task.status as TaskStatus)) {
    // 只有在非终态时更新
    console.log('Task exists and not in terminal state, updating record...');

    // 创建符合TaskUpdateInput类型的对象
    const updateData = {
      task_id: taskData.task_id,
      uid: taskData.uid,
      app: taskData.app,
      source: taskData.source,
      status: task.status as TaskStatus,
      result: taskData.result,
      error: taskData.error,
      input_params: taskData.input_params,
      credit: taskData.credit
    };

    await doUpdateTask(updateData, env);
  } else {
    // 处理终态任务
    console.log(`Updating task ${task.taskId} to terminal state: ${task.status}`);

    // 解决循环引用问题
    if (task.result === task) {
      const { result: _result, ...taskWithoutResult } = task;
      task.result = taskWithoutResult;
    }

    await doUpdateTaskStatus(task as TaskInfoResult, env);
  }
}
