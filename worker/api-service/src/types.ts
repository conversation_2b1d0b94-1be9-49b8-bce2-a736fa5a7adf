import { convertParams } from 'chanfana';
import { z } from 'zod';
// import { TaskInfoDurableObject } from './durableObjects';
import { AuthService } from '../../auth-service/src/service/authService';

// FileBody
interface ParameterType {
	default?: string | number | boolean;
	description?: string;
	example?: string | number | boolean;
	required?: boolean;
	deprecated?: boolean;
}
interface StringParameterType extends ParameterType {
	format?: string;
}
export function FileBody(params?: StringParameterType): z.ZodString {
	return convertParams<z.ZodString>(z.string(), params);
}

export const R2_PREFIX = 'uploads';
export const R2_PREFIX_RESULT = 'result';

// // 导入自动生成的 worker-configuration.d.ts
type NewEnv = Omit<Env, 'TASK_INFO_DURABLE_OBJECT'> & {
	// TASK_INFO_DURABLE_OBJECT: DurableObjectNamespace<TaskInfoDurableObject>;
	AUTH_SERVICE: Service<AuthService>;
};
export type Bindings = Pick<NewEnv, keyof NewEnv> & {
	AUTH_SERVICE: Fetcher;
	FIXED_SIGN: string;
	FAL_KEY: string;
};

export type Variables = {
	// stub: DurableObjectStub<TaskInfoDurableObject>;
};

// Cache
export type CacheHandler = {
	get: (key: string) => Promise<string | null>;
	set: (key: string, value: string, ttl?: number) => Promise<void>;
	del: (key: string) => Promise<void>;
};

export enum SOURCE {
	WEB = 'web',
	CANVA = 'canva',
	API = 'api',
	FRAMER = 'framer',
	FIGMA = 'figma',
	RAPIDAPI = 'rapidapi',
	MCP = 'mcp'
}

export enum APP {
	IMAGE_UPSCALER = 'iu',
	SPEEDPAINTER = 'sp',
	VIDEO_UPSCALER = 'vu',
	REMOVE_BG = 'remove-bg',
	IMAGE_VECTORIZATION = 'image-vectorization',
	IMAGE_EXTENDS = 'image-extends',
	IMAGE_RELIGHTER = 'image-relighter',
	TEXT_BEHIND_IMAGE = 'text-behind-image',
	IMAGE_GENERATOR = "image-generator",
	CHAT_2_DESIGN = "chat-2-design",
	AI_302 = '302'
}

export enum VIDEO_UPSCALER_MODEL {
	GENERAL = 'general',
	ANIME = 'anime',
	PORTRAIT = 'portrait'
}

export enum VIDEO_QUALITY {
	'720P' = '720P',
	'1080P' = '1080P',
	'4K' = '4K'
}