/**
 * Global configuration for channel-specific system prompts
 * Different channels can have different system prompts for AI interactions
 */

export interface SystemPromptConfig {
    default: string;
    channels: Record<string, string>;
}

export const SYSTEM_PROMPTS: SystemPromptConfig = {
    // Default system prompt used when no channel is specified (can be empty)
    default: "",
    
    // Channel-specific system prompts
    channels: {
        // // Canva channel prompt
        canva: "Please consider the user's language environment. Unless the user explicitly requests a reply in a specific language, respond in the language used by the user.",
    
        // // Web channel prompt
        // web: "You are a helpful AI assistant for web users. Generate images based on user prompts and provide them in a web-friendly format. Respond with image URLs in markdown format.",
        
        // // API channel prompt
        // api: "You are an AI image generation API service. Generate images efficiently based on the provided prompts. Return image URLs in markdown format for easy parsing.",
        
        // // Mobile channel prompt
        // mobile: "You are an AI assistant optimized for mobile users. Generate images that work well on mobile devices with appropriate sizes and formats. Respond with image URLs in markdown format.",
        
        // // Custom channel examples
        // marketing: "You are an AI assistant specialized in marketing content. Generate images that are compelling for marketing campaigns, advertisements, and social media. Consider brand aesthetics and target audience. Respond with image URLs in markdown format.",
        
        // education: "You are an AI assistant for educational content. Generate clear, informative images suitable for teaching and learning. Focus on clarity and educational value. Respond with image URLs in markdown format."
    }
};

/**
 * Get system prompt for a specific channel
 * @param channel - The channel identifier (e.g., 'canva', 'web', 'api')
 * @param customPrompt - Optional custom prompt that overrides the channel prompt
 * @returns The appropriate system prompt
 */
export function getSystemPrompt(channel?: string, customPrompt?: string): string {
    // If custom prompt is provided, use it
    if (customPrompt) {
        return customPrompt;
    }
    
    // If channel is specified and exists in config, use channel-specific prompt
    if (channel && SYSTEM_PROMPTS.channels[channel]) {
        return SYSTEM_PROMPTS.channels[channel];
    }
    
    // Otherwise, use default prompt
    return SYSTEM_PROMPTS.default;
}

/**
 * Add or update a channel-specific system prompt at runtime
 * @param channel - The channel identifier
 * @param prompt - The system prompt for this channel
 */
export function setChannelPrompt(channel: string, prompt: string): void {
    SYSTEM_PROMPTS.channels[channel] = prompt;
}

/**
 * Get all available channels
 * @returns Array of channel names
 */
export function getAvailableChannels(): string[] {
    return Object.keys(SYSTEM_PROMPTS.channels);
}