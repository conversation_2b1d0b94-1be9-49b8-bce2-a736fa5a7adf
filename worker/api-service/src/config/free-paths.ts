/**
 * Configuration for free API paths in the 302 proxy
 * These paths will not deduct any credits when called
 */

export const FREE_302_PATHS = [
    '/v1/chat/completions'
];

/**
 * Check if a given path is free (no credits required)
 * @param path - The API path to check
 * @returns true if the path is free, false otherwise
 */
export function isFree302Path(path: string): boolean {
    // Normalize the path by removing leading/trailing slashes and collapsing multiple slashes
    const normalizedPath = path
        .replace(/\/+/g, '/') // Replace multiple slashes with single slash
        .replace(/^\/+|\/+$/g, ''); // Remove leading/trailing slashes
    
    return FREE_302_PATHS.some(freePath => {
        const normalizedFreePath = freePath
            .replace(/\/+/g, '/') // Replace multiple slashes with single slash
            .replace(/^\/+|\/+$/g, ''); // Remove leading/trailing slashes
        return normalizedPath === normalizedFreePath || 
               normalizedPath.startsWith(normalizedFreePath + '/');
    });
}