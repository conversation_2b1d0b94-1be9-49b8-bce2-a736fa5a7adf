import { TaskStatus } from '../model/task';

export const TERMINAL_STATES = [TaskStatus.FINISHED, TaskStatus.SUCCESS, TaskStatus.FAILED, TaskStatus.CANCEL] as const;
export const UPDATABLE_STATES = [TaskStatus.WAITING, TaskStatus.PROCESSING] as const;
export const CREDIT_DEDUCTION_STATES = [TaskStatus.FINISHED, TaskStatus.SUCCESS] as const;

export type TerminalTaskStatus = typeof TERMINAL_STATES[number];
export type UpdatableTaskStatus = typeof UPDATABLE_STATES[number];
export type CreditDeductionTaskStatus = typeof CREDIT_DEDUCTION_STATES[number];

/**
 * Check if the task status is in terminal state
 */
export function isTerminalState(status: TaskStatus): status is TerminalTaskStatus {
    return TERMINAL_STATES.includes(status as TerminalTaskStatus);
}

/**
 * Check if the task status is updatable
 */
export function isUpdatableState(status: TaskStatus): status is UpdatableTaskStatus {
    return UPDATABLE_STATES.includes(status as UpdatableTaskStatus);
}

/**
 * Check if the task status should trigger credit deduction
 */
export function isCreditDeductionState(status: TaskStatus): status is CreditDeductionTaskStatus {
    return CREDIT_DEDUCTION_STATES.includes(status as CreditDeductionTaskStatus);
}
