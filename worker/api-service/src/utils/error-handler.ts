import { SupabaseError } from '../service/supabase/errors';

export function handleSupabaseError(error: SupabaseError): Response {
    switch (error.type) {
        case 'NOT_FOUND':
            return new Response(JSON.stringify({ 
                error: error.message 
            }), {
                status: 404,
                headers: { 'Content-Type': 'application/json' }
            });
        case 'DATABASE_ERROR':
            return new Response(JSON.stringify({ 
                error: 'Service temporarily unavailable'
            }), {
                status: 503,
                headers: { 'Content-Type': 'application/json' }
            });
        default:
            return new Response(JSON.stringify({ 
                error: 'Internal server error'
            }), {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            });
    }
} 