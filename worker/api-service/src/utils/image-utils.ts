import { nanoid } from 'nanoid';
import { R2_PREFIX } from '../types';
import { UrlProcessor } from './url-processor';

const OUTPUT_FORMATS = {
    jpeg: "image/jpeg",
    jpg: "image/jpeg",
    png: "image/png",
    webp: "image/webp",
} as const;

interface ImageConvertResponse {
    success: boolean;
    data: string;
}

export async function doConvertImageFormat(env: Env, imageUrl: string, targetFormat?: string): Promise<string> {
    try {
        if (!targetFormat) {
            return imageUrl;
        }

        console.log('doConvertImageFormat', imageUrl, targetFormat);

        // 调用转换 API
        const response = await fetch('https://79174glh8b.execute-api.us-east-1.amazonaws.com/dev/v1/image-convert', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                imageUrl: imageUrl,
                targetType: targetFormat
            })
        });
        console.log('3. 转换API响应状态:', response.status);

        if (!response.ok) {
            throw new Error(`Failed to convert image: ${response.statusText}`);
        }

        const result = await response.json() as ImageConvertResponse;

        console.log(result);

        if (!result.success) {
            throw new Error('Image conversion failed');
        }

        // 从 base64 字符串中提取实际的图片数据
        const base64Data = result.data.split(',')[1];
        const imageData = Buffer.from(base64Data, 'base64');

        // 创建文件名
        const originalFilename = imageUrl.split('/').pop() || 'image';
        const newFilename = `${nanoid()}_${Date.now()}_${originalFilename.split('.')[0]}.${targetFormat}`;
        const pathname = `${R2_PREFIX}/converted/${newFilename}`;

        // 设置 content-type
        const headers = new Headers();
        headers.set('content-type', OUTPUT_FORMATS[targetFormat as keyof typeof OUTPUT_FORMATS]);

        // 上传到 R2
        await env.MY_BUCKET.put(pathname, imageData, { httpMetadata: headers });


        // 获取公开访问URL
        const r2_url = UrlProcessor.buildR2Url(env.R2_PUBLIC_DOMAIN, pathname);

        return r2_url;

    } catch (error: any) {
        console.error('Error converting image format:', error);
        throw new Error(`Failed to convert image format: ${error.message}`);
    }
} 