import { TaskInfoResult, TaskStatus } from "../model/task";

// 通用的 fal API 响应类型
export interface FalCreateTaskResponse {
	request_id: string;
	status?: string;
}

export interface FalTaskStatusResponse {
	status: string;
	request_id: string;
	output?: any;
}

// 通用的 fal API 结果类型
export interface FalImageResult {
	images?: Array<{
		url: string;
		content_type?: string;
		file_name?: string;
		file_size?: number;
		width?: number;
		height?: number;
	}>;
	image?: {
		url: string;
		content_type?: string;
		file_name?: string;
		file_size?: number;
		width?: number;
		height?: number;
	};
	timings?: {
		inference?: number;
	};
}

// fal API 配置选项
export interface FalApiOptions {
	endpoint: string;
	apiKey: string;
	taskName?: string; // 用于日志记录
}

// 创建任务的选项
export interface FalCreateTaskOptions extends FalApiOptions {
	payload: Record<string, any>;
	initialStatus?: TaskStatus;
}

// 获取任务状态的选项
export interface FalGetTaskOptions extends FalApiOptions {
	taskId: string;
	statusEndpoint?: string; // 可选的自定义状态查询端点
	resultProcessor?: (result: any) => TaskInfoResult;
}

/**
 * fal API 工具类
 * 提供统一的 fal API 调用接口
 */
export class FalApiClient {
	/**
	 * 创建 fal 任务
	 */
	static async doCreateTask(options: FalCreateTaskOptions): Promise<TaskInfoResult> {
		const { endpoint, apiKey, payload, taskName = 'fal task', initialStatus = TaskStatus.PROCESSING } = options;

		try {
			const response = await fetch(endpoint, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Key ${apiKey}`
				},
				body: JSON.stringify(payload)
			});

			if (!response.ok) {
				const errorText = await response.text();
				console.error(`${taskName} API error:`, response.status, errorText);
				throw new Error(`${taskName} failed: ${response.status} ${response.statusText} - ${errorText}`);
			}

			const result = await response.json() as FalCreateTaskResponse;
			console.log(`${taskName} queue response:`, result);

			return {
				taskId: result.request_id,
				status: initialStatus
			} as TaskInfoResult;

		} catch (error) {
			console.error(`Create ${taskName} error:`, error);
			throw new Error(`Create ${taskName} failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	/**
	 * 获取 fal 任务状态
	 */
	static async doGetTaskStatus(options: FalGetTaskOptions): Promise<TaskInfoResult> {
		const { endpoint, apiKey, taskId, statusEndpoint: customStatusEndpoint, taskName = 'fal task', resultProcessor } = options;
		// 使用自定义状态端点，如果没有提供则使用默认格式
		const statusEndpoint = customStatusEndpoint || `${endpoint}/requests/${taskId}`;

		try {
			const response = await fetch(statusEndpoint, {
				headers: {
					'Authorization': `Key ${apiKey}`,
					'Content-Type': 'application/json'
				}
			});

			if (!response.ok) {
				if (response.status === 404) {
					return {
						taskId: taskId,
						status: TaskStatus.PROCESSING
					};
				}
				const errorText = await response.text();
				console.error(`${taskName} status check error:`, response.status, errorText);
				throw new Error(`HTTP error! status: ${response.status}`);
			}

			const result = await response.json();

			// 检查结果是否有效
			if (!result) {
				return {
					taskId: taskId,
					status: TaskStatus.FAILED,
					error: 'Invalid response from server'
				};
			}

			// 如果提供了自定义结果处理器，使用它
			if (resultProcessor) {
				return resultProcessor(result);
			}

			// 默认的结果处理逻辑
			return this.processDefaultResult(taskId, result);

		} catch (error) {
			console.error(`Error in get ${taskName} status:`, error);
			return {
				taskId: taskId,
				status: TaskStatus.FAILED,
				error: error instanceof Error ? error.message : 'Unknown error occurred'
			};
		}
	}

	/**
	 * 默认的结果处理逻辑
	 */
	private static processDefaultResult(taskId: string, result: FalImageResult): TaskInfoResult {
		// 检查是否有图片结果 (支持 images 数组或单个 image 对象)
		const imageData = result.images?.[0] || result.image;
		
		if (imageData?.url) {
			return {
				taskId: taskId,
				status: TaskStatus.FINISHED,
				imageUrl: imageData.url,
				mimeType: imageData.content_type,
				duration: result.timings?.inference
			};
		}

		// 如果没有图片URL但有结果，说明任务还在处理中
		return {
			taskId: taskId,
			status: TaskStatus.PROCESSING
		};
	}

	/**
	 * 构建 fal API 端点 URL
	 */
	static buildEndpoint(service: string): string {
		return `https://queue.fal.run/fal-ai/${service}`;
	}

	/**
	 * 构建 fal API 状态查询端点 URL
	 * 某些服务的状态查询端点与创建端点不同
	 */
	static buildStatusEndpoint(service: string, taskId: string): string {
		// 对于 ideogram 服务，状态查询端点使用简化路径
		if (service.startsWith('ideogram/')) {
			return `https://queue.fal.run/fal-ai/ideogram/requests/${taskId}`;
		}
		
		// 默认格式
		return `https://queue.fal.run/fal-ai/${service}/requests/${taskId}`;
	}
}

/**
 * 常用的 fal API 端点
 */
export const FAL_ENDPOINTS = {
	CLARITY_UPSCALER: 'clarity-upscaler',
	IMAGE2SVG: 'image2svg',
	IDEOGRAM_V3_REFRAME: 'ideogram/v3/reframe',
	RECRAFT_V3: 'recraft/v3/text-to-image',
	ICLIGHT_V2: 'iclight-v2'
} as const; 