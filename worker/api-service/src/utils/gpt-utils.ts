export interface ChatCompletionMessage {
        role: 'user' | 'assistant' | 'system';
        content: string;
}

export interface ChatCompletionResponse {
        id: string;
        object: string;
        created: number;
        model: string;
        choices: Array<{
                index: number;
                message: ChatCompletionMessage;
                finish_reason: string;
        }>;
        usage: {
                prompt_tokens: number;
                completion_tokens: number;
                total_tokens: number;
        };
}


/**
 * 处理GPT请求 (支持SSE流式响应)
 * @returns 如果请求成功，返回一个 ReadableStream；否则抛出错误。
 */
export async function processGptRequest(
        messages: ChatCompletionMessage[],
        apiKey: string,
        model: string = "gpt-4o-image-generation"
): Promise<ReadableStream<Uint8Array>> {
        const requestBody: any = {
                model,
                messages,
                stream: true // 添加 stream 参数以启用 SSE
        };

        // 调用302.ai API
        const response = await fetch("https://api.302.ai/v1/chat/completions", {
                method: "POST",
                headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${apiKey}`,
                },
                body: JSON.stringify(requestBody),
        });

        if (!response.ok) {
                // 尝试读取错误信息，如果失败则使用通用错误
                let errorDetails = `Status: ${response.status} ${response.statusText}`;
                try {
                    const errorData = await response.json();
                    errorDetails += ` - ${JSON.stringify(errorData)}`;
                } catch (e) {
                    // 如果无法解析 JSON，可能响应体为空或不是 JSON 格式
                    const textError = await response.text().catch(() => 'Unable to read error body');
                    errorDetails += ` - ${textError}`;
                }
                throw new Error(`GPT API 请求失败: ${errorDetails}`);
        }

        // 检查 response.body 是否为 null
        if (!response.body) {
            throw new Error("GPT API 响应体为空，无法获取流数据");
        }

        // 直接返回响应体流
        return response.body;
}

/**
 * 从响应中提取图片URL
 */
export function extractImageUrls(content: string): string[] {
        const regex = /!\[.*?\]\((https:\/\/[^)]+)\)/g;
        const urlSet = new Set<string>();
        let match;

        while ((match = regex.exec(content)) !== null) {
                urlSet.add(match[1]);
        }

        return Array.from(urlSet);
}

export async function streamGptRequest(
  messages: ChatCompletionMessage[],
  apiKey: string,
  model: string = "gpt-4o-image-generation",
  temperature: number = 0.7,
  maxTokens: number | null = null
): Promise<ChatCompletionResponse | null> {
  const requestId = crypto.randomUUID();
  console.log(`[${requestId}] [GPT 302.ai] Streaming request started`);

  const requestBody: any = {
    model,
    messages,
    temperature,
    stream: true,
  };

  if (maxTokens !== null) {
    requestBody.max_tokens = maxTokens;
  }

  console.log(`[${requestId}] [GPT 302.ai] Request body:`, {
    ...requestBody,
    messages: `[${messages.length} messages]`,
  });

  const response = await fetch("https://api.302.ai/v1/chat/completions", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${apiKey}`,
    },
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      `[${requestId}] GPT API request failed: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`
    );
  }

  if (!response.body) {
    throw new Error(`[${requestId}] Response body is null`);
  }

  let fullContent = "";
  let fullResponse: ChatCompletionResponse | null = null;
  let chunkCount = 0;
  let dataBuffer = "";

  const reader = response.body.getReader();
  const decoder = new TextDecoder();

  try {
    let done = false;

    while (!done) {
      const { value, done: doneReading } = await reader.read();
      chunkCount++;
      done = doneReading;

      if (done) {
        console.log(`[${requestId}] [GPT 302.ai] Stream completed after ${chunkCount} chunks`);
        break;
      }

      const chunk = decoder.decode(value, { stream: true });
      dataBuffer += chunk;

      let newLineIndex;
      while ((newLineIndex = dataBuffer.indexOf("\n")) !== -1) {
        const line = dataBuffer.substring(0, newLineIndex).trim();
        dataBuffer = dataBuffer.substring(newLineIndex + 1);

        if (line === "" || line === "data: [DONE]") continue;

        if (line.startsWith("data: ")) {
          try {
            const jsonData = line.replace("data: ", "");
            const data = JSON.parse(jsonData);

            if (data.choices && data.choices[0]?.delta?.content) {
              const content = data.choices[0].delta.content;
              fullContent += content;
            }

            if (data.id && data.model) {
              fullResponse = {
                ...data,
                choices: [
                  {
                    index: 0,
                    message: {
                      role: "assistant",
                      content: fullContent,
                    },
                    finish_reason: data.choices?.[0]?.finish_reason || "stop",
                  },
                ],
              };
            }
          } catch (error) {
            console.error(
              `[${requestId}] [GPT 302.ai] Error parsing stream data: ${error}, line: ${line}`
            );
            throw error;
          }
        }
      }
    }

    if (dataBuffer.trim() !== "") {
      const lines = dataBuffer
        .split("\n")
        .filter((line) => line.trim() !== "" && line.trim() !== "data: [DONE]");

      for (const line of lines) {
        if (line.startsWith("data: ")) {
          try {
            const jsonData = line.replace("data: ", "");
            const data = JSON.parse(jsonData);

            if (data.choices && data.choices[0]?.delta?.content) {
              const content = data.choices[0].delta.content;
              fullContent += content;
            }

            if (data.id && data.model) {
              fullResponse = {
                ...data,
                choices: [
                  {
                    index: 0,
                    message: {
                      role: "assistant",
                      content: fullContent,
                    },
                    finish_reason: data.choices?.[0]?.finish_reason || "stop",
                  },
                ],
              };
            }
          } catch (error) {
            console.error(
              `[${requestId}] [GPT 302.ai] Error parsing final buffer data: ${error}, line: ${line}`
            );
          }
        }
      }
    }
  } catch (error) {
    console.error(`[${requestId}] [GPT 302.ai] Error reading stream: ${error}`);
    throw new Error(`[${requestId}] Error processing stream: ${error}`);
  } finally {
    console.log(`[${requestId}] [GPT 302.ai] Streaming request completed`);
  }

  if (fullResponse && fullResponse.choices?.length > 0) {
    fullResponse.choices[0].message = {
      role: "assistant",
      content: fullContent,
    };
  }

  return fullResponse;
}