/**
 * 302.ai pricing utilities
 */

import { isFree302Path } from '../config/free-paths';

export interface Pricing302Response {
    code: number;
    msg: string;
    data: Array<{
        name: string;
        tag: string;
        pricing: {
            input: number;
            output: number;
        };
        pricing_prefix: string;
        pricing_suffix: string;
        description: string;
    }>;
}

/**
 * Fetch pricing from 302.ai API and calculate credits
 * @param path - API path to query pricing for
 * @returns Credits needed (converted from USD to credits)
 */
export async function fetch302PricingAndCalculateCredits(
    path: string
): Promise<number> {
    // Check if this is a free path
    if (isFree302Path(path)) {
        console.log(`Path ${path} is configured as free - returning 0 credits`);
        return 0;
    }
    
    try {
        const url = `https://api.302.ai/dashboard/prices?path=${encodeURIComponent(path)}`;
        
        const response = await fetch(url, {
            method: 'GET'
        });

        if (!response.ok) {
            console.warn(`302.ai API request failed: ${response.status} ${response.statusText}`);
            // Return default credits if API fails
            return convertUsdToCredits(0.5);
        }

        const data: Pricing302Response = await response.json();
        
        if (data.code !== 0 || !data.data || data.data.length === 0) {
            console.warn('302.ai API returned no pricing data, using default');
            // Return default credits if no data
            return convertUsdToCredits(0.5);
        }

        // Take the first pricing data's output value
        const outputPrice = data.data[0].pricing.output;
        
        // Convert USD to credits: USD * 2 / 0.02
        return convertUsdToCredits(outputPrice);
        
    } catch (error) {
        console.error('Error fetching 302.ai pricing:', error);
        // Return default credits on error
        return convertUsdToCredits(0.5);
    }
}

/**
 * Convert USD price to credits
 * 1 credit = 0.02 USD
 * @param usdPrice - Price in USD
 * @returns Credits needed
 */
function convertUsdToCredits(usdPrice: number): number {
    // Formula: USD * 2 / 0.02 = USD * 100
    const credits = (usdPrice * 2) / 0.02;
    return Math.ceil(credits); // Round up to ensure sufficient credits
}

/**
 * Calculate credits for 302 AI service
 * This function can be used by middleware and other parts of the application
 * @param path - API path to query pricing for
 * @returns Promise<number> Credits needed
 */
export async function calculate302Credits(
    path: string
): Promise<number> {
    return await fetch302PricingAndCalculateCredits(path);
}