import { TaskInfoResult } from '../model/task';
import { R2_PREFIX_RESULT } from '../types';
import { nanoid } from 'nanoid';
import { isTerminalState } from './task-utils';

/**
 * URL 处理工具类 - 负责处理任务结果中的 URL 转换
 */
export class UrlProcessor {
  /**
   * 安全地拼接 R2 公共域名和路径，避免双斜杠问题
   */
  static buildR2Url(r2PublicDomain: string, pathname: string): string {
    const baseUrl = r2PublicDomain.endsWith('/')
      ? r2PublicDomain.slice(0, -1)
      : r2PublicDomain;
    const cleanPathname = pathname.startsWith('/') ? pathname : `/${pathname}`;
    return `${baseUrl}${cleanPathname}`;
  }
  /**
   * 检查是否为 A1D URL
   */
  static isA1dUrl(url: string): boolean {
    try {
      if (!url || typeof url !== 'string') {
        console.warn(`[UrlProcessor] Invalid URL provided: ${url}`);
        return false;
      }

      const urlObj = new URL(url);
      console.log(`[UrlProcessor] Checking if URL is A1D: ${url}, hostname: ${urlObj.hostname}`);

      // 更精确的 A1D URL 判断，包括所有 a1d.ai 子域名
      const isA1dDomain = urlObj.hostname.endsWith('.a1d.ai') ||
        urlObj.hostname.endsWith('.a1d.net') ||
        urlObj.hostname === 'a1d.ai' ||
        urlObj.hostname === 'a1d.net';

      console.log(`[UrlProcessor] Is A1D domain: ${isA1dDomain}`);
      return isA1dDomain;
    } catch (e) {
      console.error(`[UrlProcessor] Error parsing URL ${url}:`, e);
      return false;
    }
  }

  /**
   * 复制文件到 R2 存储
   */
  static async copyFileToR2(env: Env, url: string, fileType: 'image' | 'video', app?: string): Promise<string | null> {
    try {
      console.log(`[UrlProcessor] Copying ${fileType} from ${url} to R2...`);

      // 设置请求头，模拟浏览器请求
      const fetchOptions = {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': fileType === 'image' ? 'image/*' : 'video/*',
          'Accept-Language': 'en-US,en;q=0.9',
          'Referer': 'https://a1d.ai/',
          'Origin': 'https://a1d.ai'
        }
      };

      console.log(`[UrlProcessor] Fetching file from ${url}...`);
      // 获取外部文件
      const response = await fetch(url, fetchOptions);
      console.log(`[UrlProcessor] Fetch response: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        this.logFetchError(url, fileType, response);
        return null;
      }

      // 生成文件名和路径
      const { newFileName, contentType } = this.getFileDetails(url, fileType);
      // 如果有 app 参数，则包含在路径中，否则使用 'unknown' 作为默认值
      const appPath = app || 'common';
      const pathname = `${R2_PREFIX_RESULT}/${appPath}/originals/${fileType}s/${newFileName}`;

      console.log(`[UrlProcessor] Generated file path: ${pathname}, contentType: ${contentType}`);

      // 获取文件数据并上传到R2
      console.log(`[UrlProcessor] Converting response to arrayBuffer...`);
      const fileData = await response.arrayBuffer();
      console.log(`[UrlProcessor] File size: ${fileData.byteLength} bytes`);

      console.log(`[UrlProcessor] Uploading to R2 bucket...`);
      await env.MY_BUCKET.put(pathname, fileData, {
        httpMetadata: { contentType }
      });
      console.log(`[UrlProcessor] Successfully uploaded to R2`);

      // 返回新的URL - 使用安全的URL拼接方法
      const r2Url = this.buildR2Url(env.R2_PUBLIC_DOMAIN, pathname);

      console.log(`[UrlProcessor] Successfully copied ${fileType} to R2: ${r2Url}`);

      return r2Url;
    } catch (error) {
      console.error(`[UrlProcessor] Error copying ${fileType} to R2 from ${url}:`, error);
      console.error(`[UrlProcessor] R2_PUBLIC_DOMAIN: ${env.R2_PUBLIC_DOMAIN}`);
      console.error(`[UrlProcessor] App: ${app || 'common'}`);
      return null;
    }
  }

  /**
   * 记录文件获取错误
   */
  private static logFetchError(url: string, fileType: string, response: Response): void {
    console.error(`Failed to fetch ${fileType} from ${url}: ${response.status} ${response.statusText}`);

    if (response.status === 403 && url.includes('cloudfront.net')) {
      console.log(`CloudFront 403 error for ${url} - this resource requires authorization. Skipping.`);
    }
  }

  /**
   * 获取文件详细信息（文件名和内容类型）
   */
  private static getFileDetails(url: string, fileType: 'image' | 'video'): {
    newFileName: string;
    contentType: string;
  } {
    // 移除 URL 中的查询参数（如 ?X-Amz-Algorithm=... 等）
    const cleanUrl = url.split('?')[0];

    // 解析清理后的URL获取文件名和扩展名
    const urlParts = cleanUrl.split('/');
    const fileName = urlParts[urlParts.length - 1];
    const fileExtension = fileName.includes('.')
      ? fileName.split('.').pop()
      : (fileType === 'image' ? 'jpg' : 'mp4');

    // 创建新文件名
    const newFileName = `${nanoid()}_${Date.now()}.${fileExtension}`;

    // 确定内容类型
    let contentType = 'application/octet-stream';
    if (fileType === 'image') {
      contentType = `image/${fileExtension === 'jpg' ? 'jpeg' : fileExtension}`;
    } else if (fileType === 'video') {
      contentType = `video/${fileExtension}`;
    }

    return { newFileName, contentType };
  }

  /**
   * 处理单个 URL 字段
   */
  static async processUrlField(env: Env, url: string, fileType: 'image' | 'video', app?: string): Promise<string | null> {
    if (!url || typeof url !== 'string') return null;
    if (this.isA1dUrl(url)) return url;
    return await this.copyFileToR2(env, url, fileType, app);
  }

  /**
   * 处理任务结果中的所有 URL 字段
   */
  static async processTaskResultUrls(
    task: TaskInfoResult,
    env: Env,
    app?: string,
    options: { skipSpecialFields?: boolean } = {}
  ): Promise<TaskInfoResult> {
    const { skipSpecialFields = false } = options;
    console.log(`[UrlProcessor] Processing URLs for task ${task.taskId}, status: ${task.status}, app: ${app}, skipSpecialFields: ${skipSpecialFields}`);


    // 检查是否为终态任务
    if (!isTerminalState(task.status)) {
      console.log(`[UrlProcessor] Task ${task.taskId} is not in terminal state, skipping URL processing`);
      return task;
    }

    console.log(`[UrlProcessor] Task ${task.taskId} is in terminal state, processing URLs`);

    // 创建副本避免修改原对象
    const processedTask = { ...task };

    // 处理各种 URL 字段
    // 根据skipSpecialFields选项决定是否包含特殊字段(sketchImageUrl, colorImageUrl, videoUrl)
    const urlFields: Array<{ field: keyof TaskInfoResult, type: 'image' | 'video' }> = [
      { field: 'imageUrl', type: 'image' },
      { field: 'thumbUrl', type: 'image' }
    ];

    // 只有在不跳过特殊字段时才添加到处理列表
    if (!skipSpecialFields) {
      urlFields.push(
        { field: 'videoUrl', type: 'video' },
        { field: 'sketchImageUrl', type: 'image' },
        { field: 'colorImageUrl', type: 'image' }
      );
    } else {
      // 记录跳过的字段
      if (task.videoUrl) {
        console.log(`[UrlProcessor] Skipping videoUrl processing as requested: ${task.videoUrl}`);
      }
      if (task.sketchImageUrl) {
        console.log(`[UrlProcessor] Skipping sketchImageUrl processing as requested: ${task.sketchImageUrl}`);
      }
      if (task.colorImageUrl) {
        console.log(`[UrlProcessor] Skipping colorImageUrl processing as requested: ${task.colorImageUrl}`);
      }
    }

    for (const { field, type } of urlFields) {
      const url = task[field] as string;
      console.log(`[UrlProcessor] Processing field ${field}, url: ${url}`);

      if (url && !this.isA1dUrl(url)) {
        console.log(`[UrlProcessor] Field ${field} contains non-A1D URL, processing...`);
        try {
          const newUrl = await this.copyFileToR2(env, url, type, app);

          if (newUrl) {
            // 保存原始URL
            const originalField = `original${field.charAt(0).toUpperCase() + field.slice(1)}` as keyof TaskInfoResult;
            (processedTask as any)[originalField] = url;
            (processedTask as any)[field] = newUrl;
            console.log(`[UrlProcessor] Processed ${field}: ${url} -> ${newUrl}`);
          } else {
            console.warn(`[UrlProcessor] Failed to process ${field}, keeping original URL: ${url}. Check network connectivity and R2 configuration.`);
          }
        } catch (error) {
          console.error(`[UrlProcessor] Error processing ${field}:`, error);
          // 如果处理失败，保持原URL
        }
      } else if (url) {
        console.log(`[UrlProcessor] Field ${field} contains A1D URL, skipping: ${url}`);
      } else {
        console.log(`[UrlProcessor] Field ${field} is empty or null`);
      }
    }

    // 处理 imageUrls 数组字段（用于 chat-2-design 等应用）
    if (processedTask.imageUrls && Array.isArray(processedTask.imageUrls)) {
      console.log(`[UrlProcessor] Processing imageUrls array with ${processedTask.imageUrls.length} URLs`);
      const processedImageUrls: string[] = [];
      const originalImageUrls: string[] = [];

      for (let i = 0; i < processedTask.imageUrls.length; i++) {
        const url = processedTask.imageUrls[i];
        if (url && !this.isA1dUrl(url)) {
          console.log(`[UrlProcessor] Processing imageUrls[${i}]: ${url}`);
          try {
            const newUrl = await this.copyFileToR2(env, url, 'image', app);
            if (newUrl) {
              processedImageUrls.push(newUrl);
              originalImageUrls.push(url);
              console.log(`[UrlProcessor] Processed imageUrls[${i}]: ${url} -> ${newUrl}`);
            } else {
              processedImageUrls.push(url);
              console.warn(`[UrlProcessor] Failed to process imageUrls[${i}], keeping original URL: ${url}`);
            }
          } catch (error) {
            console.error(`[UrlProcessor] Error processing imageUrls[${i}]:`, error);
            processedImageUrls.push(url);
          }
        } else if (url) {
          console.log(`[UrlProcessor] imageUrls[${i}] contains A1D URL, skipping: ${url}`);
          processedImageUrls.push(url);
        }
      }

      // 更新处理后的数组
      processedTask.imageUrls = processedImageUrls;
      // 保存原始 URLs（如果有任何被处理的话）
      if (originalImageUrls.length > 0) {
        (processedTask as any).originalImageUrls = originalImageUrls;
      }
    }

    console.log(`[UrlProcessor] Finished processing URLs for task ${task.taskId}`);
    return processedTask;
  }

  /**
   * 统一的任务结果处理方法 - 确保所有组件使用相同的URL转换逻辑
   * 这是所有组件应该使用的标准方法，用于处理任务结果中的URL转换
   *
   * @param taskResult 任务结果
   * @param env 环境变量
   * @param app 应用名称（可选，用于确定文件存储路径）
   * @param options 处理选项
   * @param options.skipSpecialFields 是否跳过特殊字段(sketchImageUrl, colorImageUrl, videoUrl)的转换（默认false）
   * @returns 处理后的任务结果，如果不是终态任务则返回原结果
   */
  static async processTaskResultWithUrlConversion(
    taskResult: TaskInfoResult,
    env: Env,

    app?: string,
    options: { skipSpecialFields?: boolean } = {}
  ): Promise<TaskInfoResult> {
    const { skipSpecialFields = false } = options;
    console.log(`[UrlProcessor] processTaskResultWithUrlConversion called for task ${taskResult.taskId}, status: ${taskResult.status}, app: ${app}, skipSpecialFields: ${skipSpecialFields}`);
    // 只处理终态任务
    if (!isTerminalState(taskResult.status)) {
      console.log(`[UrlProcessor] Task ${taskResult.taskId} is not in terminal state, returning original result`);
      return taskResult;
    }

    // 检查是否有任何URL需要处理
    // 根据skipSpecialFields选项决定是否检查特殊字段(sketchImageUrl, colorImageUrl, videoUrl)
    const hasImageUrlsToProcess = taskResult.imageUrls && Array.isArray(taskResult.imageUrls) &&
      taskResult.imageUrls.some(url => url && !this.isA1dUrl(url));

    const needsUrlProcessing = (
      (taskResult.imageUrl && !this.isA1dUrl(taskResult.imageUrl)) ||
      (taskResult.thumbUrl && !this.isA1dUrl(taskResult.thumbUrl)) ||
      (!skipSpecialFields && taskResult.videoUrl && !this.isA1dUrl(taskResult.videoUrl)) ||
      (!skipSpecialFields && taskResult.sketchImageUrl && !this.isA1dUrl(taskResult.sketchImageUrl)) ||
      (!skipSpecialFields && taskResult.colorImageUrl && !this.isA1dUrl(taskResult.colorImageUrl)) ||
      hasImageUrlsToProcess
    );

    if (!needsUrlProcessing) {
      console.log(`[UrlProcessor] All URLs are already A1D domains for task ${taskResult.taskId}`);
      return taskResult;
    }

    console.log(`[UrlProcessor] Processing URLs for terminal task ${taskResult.taskId}`);
    try {
      const processedResult = await this.processTaskResultUrls(taskResult, env, app, { skipSpecialFields });
      console.log(`[UrlProcessor] Successfully processed URLs for task ${taskResult.taskId}`);
      return processedResult;
    } catch (error) {
      console.error(`[UrlProcessor] Error processing URLs for task ${taskResult.taskId}:`, error);
      // 如果URL处理失败，返回原始结果而不是抛出错误
      // 这确保了系统的健壮性，即使URL转换失败也不会影响任务状态更新
      return taskResult;
    }
  }
}