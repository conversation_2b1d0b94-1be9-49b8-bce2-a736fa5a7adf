import { doAnalyzeTasksByDateRange } from './service/task/task-analysis';
import { handleStaleTasksCleanup } from './service/task/task-cleanup';

export async function handleScheduled(
    event: ScheduledEvent,
    env: Env
) {
    console.log('Scheduled event received:', {
        cron: event.cron,
        scheduledTime: event.scheduledTime,
        env: env.ENV
    });

    const cronTime = event.cron;

    try {
        // 每天早上 1 点执行任务分析
        if (cronTime === '0 1 * * *') {
            console.log('Starting daily task analysis...');
            await doAnalyzeTasksByDateRange(env);
            console.log('Daily task analysis completed');
        }

        // 每 5 分钟执行一次任务清理
        if (cronTime === '*/5 * * * *') {
            console.log('Starting task cleanup...');
            await handleStaleTasksCleanup(env);
            console.log('Task cleanup completed');
        }

        console.log('Scheduled task execution completed');
    } catch (error) {
        console.error('Error in scheduled tasks:', error);
        throw error;
    }
}
