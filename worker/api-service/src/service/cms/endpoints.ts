/**
 * CMS endpoint configuration
 *
 * This file contains the mapping of scene identifiers to their corresponding CMS endpoints.
 * Each scene maps to a specific CMS collection endpoint.
 */

import { getClient } from '../supabase';

// Scene to collection mapping
export interface CmsEndpointConfig {
  endpoint: string;
  description?: string;
}

// Interface for database scene endpoint records
export interface SceneEndpoint {
  id: number;
  scene_id: string;
  endpoint_url: string;
  description: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Get scene endpoints from database
 * 
 * @param env - Environment variables
 * @param scene - Optional scene identifier. If provided, returns endpoint for specific scene
 * @returns If scene is provided, returns the endpoint URL or null if not found.
 *          If scene is not provided, returns a record of all scene identifiers to their CMS endpoint configurations
 */
export async function getSceneEndpoints(env: Env, scene?: string): Promise<Record<string, CmsEndpointConfig> | string | null> {
  try {
    const supabase = getClient(env);
    const { data, error } = await supabase
      .from('scene_endpoints')
      .select('*');
    
    if (error) {
      console.error('Failed to fetch scene endpoints:', error);
      return scene ? null : {};
    }
    
    const endpoints = (data as SceneEndpoint[]).reduce((acc, item) => {
      acc[item.scene_id] = {
        endpoint: item.endpoint_url,
        description: item.description || undefined,
      };
      return acc;
    }, {} as Record<string, CmsEndpointConfig>);
    
    // If scene is provided, return the specific endpoint or null
    if (scene) {
      const sceneKey = scene.toLowerCase();
      const endpoint = endpoints[sceneKey];
      
      if (!endpoint) {
        console.log(`Scene endpoint not found for ${scene}`);
        return null;
      }
      
      return endpoint.endpoint;
    }
    
    // Otherwise return all endpoints
    return endpoints;
  } catch (error) {
    console.error('Error fetching scene endpoints:', error);
    return scene ? null : {};
  }
}

/**
 * Get scene template data from CMS
 * 
 * @param env - Environment variables
 * @param sceneType - Scene type (e.g., 'relight', 'picadabra', etc.)
 * @param sceneId - Scene ID to fetch template for
 * @returns Template data including prompt and other parameters, or null if not found
 */
export async function getSceneTemplate(env: Env, sceneType: string, sceneId: number): Promise<{ prompt?: string; [key: string]: any } | null> {
  try {
    // Get the CMS endpoint for the specified scene type
    const endpoint = await getSceneEndpoints(env, sceneType);
    
    if (!endpoint || typeof endpoint !== 'string') {
      console.error(`${sceneType} endpoint not found`);
      return null;
    }
    
    // Fetch template data from CMS with specific scene ID
    const finalEndpoint = `${endpoint}?id=${sceneId}`;
    console.log(`Fetching ${sceneType} template from: ${finalEndpoint}`);
    
    const response = await fetch(finalEndpoint);
    
    if (!response.ok) {
      console.error(`Failed to fetch template from CMS: ${response.status} ${response.statusText}`);
      return null;
    }
    
    const responseData = await response.json() as any;
    
    // Check if data exists and has items
    if (!responseData || !responseData.data || !Array.isArray(responseData.data) || responseData.data.length === 0) {
      console.log(`No template found for ${sceneType} scene ID: ${sceneId}`);
      return null;
    }
    
    // Return the first item which should contain the template data
    const template = responseData.data[0];
    console.log(`Found template for ${sceneType} scene ${sceneId}:`, template);
    
    return template;
  } catch (error) {
    console.error(`Error fetching ${sceneType} scene template:`, error);
    return null;
  }
}
