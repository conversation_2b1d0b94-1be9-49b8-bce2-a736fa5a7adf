import { doGetCreditRule, CreditRule } from '../supabase/credit-rules';
import { doCalculateDynamicCredits, DynamicCalculationParams } from './dynamic-calculator';
import { fetch302PricingAndCalculateCredits } from '../../utils/pricing-302';

export type TaskItem =
        | { app: 'iu'; source?: string; scale: number; }
        | { app: 'sp'; source?: string; sketchDuration?: number; colorFillDuration?: number; }
        | { app: 'vu'; source?: string; duration?: number; quality?: string; }
        | { app: 'remove-bg'; source?: string; }
        | { app: 'image-vectorization'; source?: string; }
        | { app: 'image-extends'; source?: string; }
        | { app: 'text-behind-image'; source?: string; }
        | { app: 'image-generator'; source?: string; }
        | { app: 'chat-2-design'; source?: string; prompt?: string; imageUrls?: string[]; }
        | { app: '302'; source?: string; path: string; }
        | { app: 'image-relighter'; source?: string; }
        | { app: string; source?: string;[key: string]: any; }; // 兜底类型

/**
 * 为单个任务计算 credits（数据库版本）
 * @param task - 任务信息
 * @param env - 环境变量
 * @returns 该任务需要的 credits
 */
async function doCalculateSingleTaskCredits(task: TaskItem, env: any): Promise<number> {
        const { app, source = 'web' } = task;

        console.log(`Calculating credits for task: app=${app}, source=${source}`);

        // Handle 302 AI app with real-time pricing
        if (app === '302') {
                const task302 = task as { app: '302'; path: string; };
                if (!task302.path) {
                        throw new Error('Path is required for 302 AI app');
                }
                
                console.log(`Fetching 302.ai pricing for path: ${task302.path}`);
                return await fetch302PricingAndCalculateCredits(task302.path);
        }

        try {
                // 获取 credit rule
                const rule = await doGetCreditRule(env, app, source) as CreditRule | null;

                if (!rule) {
                        console.warn(`No credit rule found for app=${app}, source=${source}`);
                        throw new Error(`No credit rule found for app=${app}, source=${source}`);
                }

                console.log('Found credit rule:', rule);

                // 计算 credits - 使用共享的动态计算服务
                if (rule.has_dynamic_rule) {
                        const dynamicCredits = doCalculateDynamicCredits(rule, task as DynamicCalculationParams);
                        console.log(`Dynamic credits calculated: ${dynamicCredits}`);
                        return dynamicCredits;
                } else {
                        console.log(`Using base credits: ${rule.base_credits}`);
                        return rule.base_credits;
                }

        } catch (error) {
                console.error(`Error calculating credits for task ${app}:`, error);
                throw error;
        }
}

/**
 * 计算多个任务的总 credits
 * @param tasks - 任务数组
 * @param env - 环境变量
 * @returns 总的 credits 需求
 */
export async function doCalculateCredits(tasks: TaskItem[], env?: any): Promise<number> {
        console.log(`Calculating credits for ${tasks.length} tasks`);

        if (!env) {
                throw new Error('Environment is required for credits calculation');
        }

        // 如果有环境变量，使用数据库查询
        const creditPromises = tasks.map(task => doCalculateSingleTaskCredits(task, env));

        try {
                const creditsResults = await Promise.all(creditPromises);
                const totalCredits = creditsResults.reduce((sum, credits) => sum + credits, 0);
                console.log(`Total credits calculated: ${totalCredits}`);
                return totalCredits;
        } catch (error) {
                console.error('Error calculating credits for tasks:', error);
                throw new Error(`Error calculating credits for tasks: ${error}`);
        }
} 