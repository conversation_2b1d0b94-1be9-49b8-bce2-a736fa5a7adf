import { CreditState, CreditRecord, PreDeductCredit, CreditOperationType, CommitCreditDeduction, AddPayAsGoCredit, ResetPlanCredit } from './schema';
import { CreditError } from './errors';
import { successOf, errorOf, Result } from './result';
import { getClient } from '../supabase';
import { APP } from '../../types';

const DEFAULT_PAY_AS_GO_CREDITS = 30;

export async function doCreateCreditAudit(
        env: Env,
        params: {
                accountId: string;
                taskId?: string;
                app?: APP;
                uid?: string;
                operationType: CreditOperationType;
                creditsBefore: CreditState;
                creditsAfter: CreditState;
                description: string;
        },
): Promise<Result<null>> {
        const { error: insertError } = await getClient(env).from('credit_audit').insert({
                account_id: params.accountId,
                uid: params.uid,
                task_id: params.taskId,
                app: params.app,
                operation_type: params.operationType,
                credits_before: params.creditsBefore as unknown as <PERSON><PERSON>,
                credits_after: params.creditsAfter as unknown as <PERSON><PERSON>,
                description: params.description,
                operation_timestamp: new Date().toISOString(),
        });

        if (insertError) {
                console.error(
                        '[createCreditAudit] Database error during credit audit insert.',
                        insertError
                );
                return errorOf(CreditError.DATABASE_ERROR);
        }

        return successOf(null);
}

export function calculateAvailableCredits(creditState: CreditState): number {
        if (!creditState || typeof creditState !== 'object') {
                throw new Error('Invalid credit state');
        }
        const values = [
                creditState.plan_credits,
                creditState.pay_as_go_credits,
                creditState.pending_credits
        ];
        if (values.some(v => typeof v !== 'number' || v < 0)) {
                throw new Error('Credit values must be non-negative numbers');
        }
        return (
                creditState.plan_credits +
                creditState.pay_as_go_credits -
                creditState.pending_credits
        );
}



export async function doPreDeductCredit(
        env: Env,
        input: PreDeductCredit,
): Promise<Result<CreditRecord>> {

        if (input.credits <= 0) {
                return errorOf(CreditError.INVALID_CREDITS_AMOUNT);
        }
        try {
                const currentCredit = await doGetOrCreateUserCreditRecord(
                        env,
                        input.accountId,
                        input.uid
                );
                if (!currentCredit.success) {
                        return currentCredit;
                }

                // 使用新函数计算可用积分
                const availableCredits = calculateAvailableCredits(currentCredit.data);
                if (availableCredits < input.credits) {
                        console.error(
                                '[preDeductCredit] Insufficient credits for user:',
                                input.accountId,
                        );
                        return errorOf(CreditError.INSUFFICIENT_CREDITS);
                }

                const creditsBefore: CreditState = {
                        pending_credits: currentCredit.data.pending_credits,
                        plan_credits: currentCredit.data.plan_credits,
                        pay_as_go_credits: currentCredit.data.pay_as_go_credits,
                };

                const creditsAfter: CreditState = {
                        ...creditsBefore,
                        pending_credits: currentCredit.data.pending_credits + input.credits,
                };

                const auditResult = await doCreateCreditAudit(env, {
                        accountId: input.accountId,
                        taskId: input.taskId,
                        uid: input.uid,
                        app: input.app,
                        operationType: CreditOperationType.PRE_DEDUCT,
                        creditsBefore,
                        creditsAfter,
                        description: `Pre-deducted ${input.credits} credits for job ${input.taskId}`,
                });

                if (!auditResult.success) {
                        return auditResult;
                }

                // 更新积分记录,增加 pending_credits
                const { data: updatedCredit, error: updateError } = await getClient(env)
                        .from('credit')
                        .update({
                                pending_credits: currentCredit.data.pending_credits + input.credits,
                                updated_at: new Date().toISOString(),
                        })
                        .eq('account_id', input.accountId)
                        .select()
                        .single();

                if (updateError) {
                        console.error(
                                '[preDeductCredit] Database error during credit update for user:',
                                input.accountId,
                        );
                        return errorOf(CreditError.DATABASE_ERROR);
                }

                return successOf(updatedCredit as CreditRecord);
        } catch (error) {
                console.error('[preDeductCredit] Failed to pre-deduct credit:', error);
                return errorOf(CreditError.UNKNOWN_ERROR);
        }
}

export async function doGetOrCreateUserCreditRecord(
        env: Env,
        accountId: string,
        uid: string
): Promise<Result<CreditRecord>> {
        try {

                // 查询用户当前积分记录
                const { data: currentCredit, error: queryError } = await getClient(env)
                        .from('credit')
                        .select()
                        .eq('account_id', accountId)
                        .maybeSingle();

                if (queryError) {
                        console.error(
                                '[getOrcCreateUserCreditRecord] Database error during credit query for user:',
                                accountId,
                        );
                        return errorOf(CreditError.DATABASE_ERROR);
                }

                if (currentCredit) {
                        return successOf(currentCredit as CreditRecord);
                }

                // 如果没有记录，创建新的积分记录
                const { data: newCredit, error: insertError } = await getClient(env)
                        .from('credit')
                        .insert({
                                account_id: accountId,
                                uid: uid,
                                pending_credits: 0,
                                plan_credits: 0,
                                pay_as_go_credits: DEFAULT_PAY_AS_GO_CREDITS,
                                created_at: new Date().toISOString(),
                                updated_at: new Date().toISOString(),
                        })
                        .select()
                        .single();

                if (insertError) {
                        console.error(
                                '[getOrcCreateUserCreditRecord] Database error during new credit record insert for user:',
                                accountId,
                        );
                        return errorOf(CreditError.DATABASE_ERROR);
                }

                // 记录积分审计
                const auditResult = await doCreateCreditAudit(env, {
                        accountId: accountId,
                        uid: uid,
                        operationType: CreditOperationType.REGISTER,
                        creditsBefore: {
                                plan_credits: 0,
                                pay_as_go_credits: 0,
                                pending_credits: 0,
                        },
                        creditsAfter: {
                                plan_credits: 0,
                                pay_as_go_credits: DEFAULT_PAY_AS_GO_CREDITS,
                                pending_credits: 0,
                        },
                        description: `Register user, give ${DEFAULT_PAY_AS_GO_CREDITS} credits`,
                });

                if (!auditResult.success) {
                        return auditResult;
                }


                return successOf(newCredit as CreditRecord);
        } catch (error) {
                console.error(
                        '[getOrcCreateUserCreditRecord] Failed to ensure user credit record:',
                        error,
                );
                return errorOf(CreditError.UNKNOWN_ERROR);
        }
}

export async function doCommitCreditDeduction(
        env: Env,
        input: CommitCreditDeduction
): Promise<Result<CreditRecord>> {

        const { accountId, taskId, app, uid } = input;

        try {
                // 确保用户有积分记录
                const userCreditResult = await doGetOrCreateUserCreditRecord(env, accountId, uid);
                if (!userCreditResult.success) {
                        return userCreditResult;
                }

                // 查询预扣记录
                const { data: auditRecord, error: auditError } = await getClient(env)
                        .from('credit_audit')
                        .select()
                        .eq('account_id', accountId)
                        .eq('task_id', taskId)
                        .eq('operation_type', CreditOperationType.PRE_DEDUCT)
                        .single();

                if (auditError || !auditRecord) {
                        console.error(
                                '[commitCreditDeduction] Audit record not found for user:',
                                accountId,
                                'task:',
                                taskId,
                        );
                        return errorOf(CreditError.AUDIT_RECORD_NOT_FOUND);
                }

                const creditsToUse =
                        (auditRecord.credits_after as { pending_credits: number })
                                .pending_credits -
                        (auditRecord.credits_before as { pending_credits: number })
                                .pending_credits;

                if (creditsToUse <= 0) {
                        return errorOf(CreditError.INVALID_CREDITS_AMOUNT);
                }

                const userCredit = userCreditResult.data;

                // 计算需要扣除的积分分配
                let planCreditsToDeduct = 0;
                let payAsGoCreditsToDeduct = 0;

                // 如果plan积分足够，全部从plan扣除
                if (userCredit.plan_credits >= creditsToUse) {
                    planCreditsToDeduct = creditsToUse;
                } else {
                    // 如果plan积分不够，先用完plan积分，剩余从pay_as_go扣除
                    planCreditsToDeduct = userCredit.plan_credits;
                    payAsGoCreditsToDeduct = creditsToUse - planCreditsToDeduct;
                }

                // 更新用户积分记录中的具体数值
                const newPlanCredits = userCredit.plan_credits - planCreditsToDeduct;
                const newPayAsGoCredits = userCredit.pay_as_go_credits - payAsGoCreditsToDeduct;

                
                // 更新积分和记录积分审计
                const { data: updatedCredit, error: updateError } = await getClient(env)
                        .from('credit')
                        .update({
                                plan_credits: newPlanCredits,
                                pay_as_go_credits: newPayAsGoCredits,
                                pending_credits: userCredit.pending_credits - creditsToUse,
                                updated_at: new Date().toISOString(),
                        })
                        .eq('account_id', accountId)
                        .select()
                        .single();

                if (updateError) {
                        console.error(
                                '[commitCreditDeduction] Database error during credit update for user:',
                                accountId,
                        );
                        return errorOf(CreditError.DATABASE_ERROR);
                }

                // 记录积分审计
                const { error: insertError } = await getClient(env).from('credit_audit').insert({
                        account_id: accountId,
                        task_id: taskId,
                        uid: uid,
                        app: app,
                        operation_type: CreditOperationType.COMMIT_DEDUCTION,
                        credits_before: {
                                pending_credits: userCredit.pending_credits,
                                plan_credits: userCredit.plan_credits,
                                pay_as_go_credits: userCredit.pay_as_go_credits,
                        },
                        credits_after: {
                                pending_credits: userCredit.pending_credits - creditsToUse,
                                plan_credits: newPlanCredits,
                                pay_as_go_credits: newPayAsGoCredits,
                        },
                        description: `Used ${creditsToUse} credits for a job`,
                        operation_timestamp: new Date().toISOString(),
                });

                if (insertError) {
                        console.error(
                                '[commitCreditDeduction] Database error during credit audit insert for user:',
                                accountId,
                                insertError,
                        );
                        return errorOf(CreditError.DATABASE_ERROR);
                }

                return successOf(updatedCredit as CreditRecord);
        } catch (error) {
                console.error(
                        '[commitCreditDeduction] Failed to commit credit deduction:',
                        error,
                );
                return errorOf(CreditError.UNKNOWN_ERROR);
        }
}


export async function doAddPayAsGoCredit(
        env: Env,
        input: AddPayAsGoCredit
): Promise<Result<null>> {
        try {
                if (input.credits <= 0) {
                        return errorOf(CreditError.INVALID_CREDITS_AMOUNT);
                }
                // 确保用户有积分记录
                const userCreditResult = await doGetOrCreateUserCreditRecord(
                        env,
                        input.accountId,
                        input.uid
                );
                if (!userCreditResult.success) {
                        return userCreditResult;
                }

                const userCredit = userCreditResult.data;

                const creditsBefore: CreditState = {
                        pending_credits: userCredit.pending_credits,
                        plan_credits: userCredit.plan_credits,
                        pay_as_go_credits: userCredit.pay_as_go_credits,
                };

                const creditsAfter: CreditState = {
                        ...creditsBefore,
                        pay_as_go_credits: userCredit.pay_as_go_credits + input.credits,
                };

                const auditResult = await doCreateCreditAudit(env, {
                        accountId: input.accountId,
                        uid: input.uid,
                        operationType: CreditOperationType.ADD_PAY_AS_GO,
                        creditsBefore,
                        creditsAfter,
                        description: `Added ${input.credits} pay-as-go credits`,
                });

                if (!auditResult.success) {
                        return auditResult;
                }

                const { error: updateError } = await getClient(env)
                        .from('credit')
                        .update({
                                pay_as_go_credits: userCredit.pay_as_go_credits + input.credits,
                                updated_at: new Date().toISOString(),
                        })
                        .eq('account_id', input.accountId);

                if (updateError) {
                        console.error(
                                '[addPayAsGoCredit] Database error during credit update for user:',
                                input.accountId,
                        );
                        return errorOf(CreditError.DATABASE_ERROR);
                }

                return successOf(null);
        } catch (error) {

                console.error('[addPayAsGoCredit] Failed to add pay-as-go credits:', error.message);
                return errorOf(CreditError.UNKNOWN_ERROR);
        }
}


export async function doGetUserCredit(env: Env, accountId?: string, uid?: string): Promise<Result<CreditRecord>> {
        let query = getClient(env)
                .from('credit')
                .select('*');

        if (uid) {
                query = query.eq('uid', uid);
        }
        if (accountId) {
                query = query.eq('account_id', accountId);
        }

        // 查询用户当前积分记录
        const { data: currentCredit, error: queryError } = await query.maybeSingle();

        if (queryError) {
                console.error(
                        '[getOrcCreateUserCreditRecord] Database error during credit query for user:',
                        uid,
                );
                return errorOf(CreditError.DATABASE_ERROR);
        }

        if (currentCredit) {
                return successOf(currentCredit as CreditRecord);
        }

        return errorOf(CreditError.USER_NOT_FOUND);
}

export async function doResetPlanCredit(
        env: Env,
        input: ResetPlanCredit
): Promise<Result<CreditRecord>> {
        try {
                // 确保用户有积分记录
                const userCreditResult = await doGetOrCreateUserCreditRecord(
                        env,
                        input.accountId,
                        input.uid
                );
                if (!userCreditResult.success) {
                        return userCreditResult;
                }

                const userCredit = userCreditResult.data;

                const creditsBefore: CreditState = {
                        pending_credits: userCredit.pending_credits,
                        plan_credits: userCredit.plan_credits,
                        pay_as_go_credits: userCredit.pay_as_go_credits,
                };

                const creditsAfter: CreditState = {
                        ...creditsBefore,
                        plan_credits: input.credits,
                };

                const auditResult = await doCreateCreditAudit(env, {
                        accountId: input.accountId,
                        uid: input.uid,
                        operationType: CreditOperationType.RESET_PLAN,
                        creditsBefore,
                        creditsAfter,
                        description: `Reset plan credits to ${input.credits}`,
                });

                if (!auditResult.success) {
                        return auditResult;
                }

                const { data: updatedCredit, error: updateError } = await getClient(env)
                        .from('credit')
                        .update({
                                plan_credits: input.credits,
                                updated_at: new Date().toISOString(),
                        })
                        .eq('account_id', input.accountId)
                        .select()
                        .single();

                if (updateError) {
                        console.error(
                                '[resetPlanCredit] Database error during credit update for user:',
                                input.accountId,
                        );
                        return errorOf(CreditError.DATABASE_ERROR);
                }

                return successOf(updatedCredit as CreditRecord);
        } catch (error) {
                console.error('[resetPlanCredit] Failed to reset plan credits:', error);
                return errorOf(CreditError.UNKNOWN_ERROR);
        }
}

interface CreditRefundRequest {
    accountId: string;
    amount: number;
    reason: string;
    taskId?: string;
    app?: APP;
    uid?: string;
}

/**
 * Process a credit refund for a failed or timed out task
 * This is the reverse operation of doCommitCreditDeduction
 */
export async function doCommitCreditRefund(
    env: Env,
    request: CreditRefundRequest
): Promise<Result<CreditRecord>> {
    const { accountId, amount, reason, taskId, app, uid } = request;

    if (amount <= 0) {
        return errorOf(CreditError.INVALID_CREDITS_AMOUNT);
    }

    try {
        // Get user's credit record
        const userCreditResult = await doGetOrCreateUserCreditRecord(env, accountId, uid);
        if (!userCreditResult.success) {
            return userCreditResult;
        }

        const userCredit = userCreditResult.data;

        // Update credit record: decrease pending credits since the task failed/timed out
        const { data: updatedCredit, error: updateError } = await getClient(env)
            .from('credit')
            .update({
                pending_credits: userCredit.pending_credits - amount,
                updated_at: new Date().toISOString(),
            })
            .eq('account_id', accountId)
            .select()
            .single();

        if (updateError) {
            console.error(
                '[commitCreditRefund] Database error during credit update for user:',
                accountId,
            );
            return errorOf(CreditError.DATABASE_ERROR);
        }

        // Record the credit audit
        const { error: insertError } = await getClient(env)
            .from('credit_audit')
            .insert({
                account_id: accountId,
                task_id: taskId,
                uid: uid,
                app: app,
                operation_type: CreditOperationType.REFUND,
                credits_before: {
                    pending_credits: userCredit.pending_credits,
                    plan_credits: userCredit.plan_credits,
                    pay_as_go_credits: userCredit.pay_as_go_credits,
                },
                credits_after: {
                    pending_credits: userCredit.pending_credits - amount,
                    plan_credits: userCredit.plan_credits,
                    pay_as_go_credits: userCredit.pay_as_go_credits,
                },
                description: reason,
                operation_timestamp: new Date().toISOString(),
            });

        if (insertError) {
            console.error(
                '[commitCreditRefund] Database error during credit audit insert for user:',
                accountId,
                insertError,
            );
            return errorOf(CreditError.DATABASE_ERROR);
        }

        return successOf(updatedCredit);
    } catch (error) {
        console.error(
            '[commitCreditRefund] Unexpected error during credit refund for user:',
            accountId,
            error,
        );
        return errorOf(CreditError.UNKNOWN_ERROR);
    }
}
