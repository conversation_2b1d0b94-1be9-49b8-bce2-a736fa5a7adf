import { CreditRule } from '../supabase/credit-rules';
import { APP } from '../../types';

export interface DynamicCalculationParams {
    app: string;
    scale?: number;
    sketchDuration?: number;
    colorFillDuration?: number;
    [key: string]: any;
}

/**
 * 根据动态规则配置计算 credits
 * @param rule - 信用规则
 * @param params - 任务参数
 * @returns 计算出的 credits
 */
export function doCalculateDynamicCredits(rule: CreditRule, params: DynamicCalculationParams): number {
    if (!rule.has_dynamic_rule || !rule.dynamic_rule_config) {
        return rule.base_credits;
    }

    const config = rule.dynamic_rule_config;
    console.log('Dynamic rule config:', config, 'params:', params);

    // 简化的动态计算：主要基于数据库配置
    let finalCredits = rule.base_credits;

    // 根据不同的app处理不同的动态计算逻辑
    switch (rule.app) {
        case APP.IMAGE_UPSCALER:
        case 'iu':
            finalCredits = calculateImageUpscalerCredits(config, params);
            break;
        case APP.SPEEDPAINTER:
        case 'sp':
            finalCredits = calculateSpeedpainterCredits(config, params);
            break;
        default:
            // 其他 app 类型：简单地使用 base_credits
            console.log(`Using base credits for app: ${rule.app}`);
            finalCredits = rule.base_credits;
            break;
    }

    console.log(`Dynamic credits calculated: ${finalCredits}`);
    return finalCredits;
}

/**
 * 计算图片放大器的 credits
 */
function calculateImageUpscalerCredits(config: any, params: DynamicCalculationParams): number {
    if (!params.scale) {
        throw new Error('Scale parameter is required for image upscaler');
    }
    
    if (config.scale_multiplier) {
        const multiplier = config.scale_multiplier[params.scale];
        if (multiplier === undefined) {
            const supportedScales = Object.keys(config.scale_multiplier)
                .filter(k => k !== 'default').join(', ');
            throw new Error(`Invalid scale value: ${params.scale}. Supported scales: ${supportedScales}`);
        }
        return multiplier;
    }
    
    throw new Error('Scale multiplier configuration not found');
}

/**
 * 计算速绘的 credits
 */
function calculateSpeedpainterCredits(config: any, params: DynamicCalculationParams): number {
    const sketchDuration = params.sketchDuration || 3;
    const colorFillDuration = params.colorFillDuration || 3;
    const totalDuration = sketchDuration + colorFillDuration;
    
    if (config.duration_multiplier) {
        return totalDuration * config.duration_multiplier;
    }
    
    throw new Error('Duration multiplier configuration not found');
} 