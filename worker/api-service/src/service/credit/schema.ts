import { z } from 'zod';
import { APP } from '../../types';

export const enum CreditOperationType {
        PRE_DEDUCT = 'pre_deduct',
        COMMIT_DEDUCTION = 'commit_deduction',
        EXECUTE_DEDUCTION = 'execute_deduction',
        ADD_PAY_AS_GO = 'add_pay_as_go',
        RESET_PLAN = 'reset_plan',
        REFUND = 'refund',
        REGISTER = 'register'
}

// Schema for preDeductCredit
export const preDeductCreditSchema = z.object({
        accountId: z.string().min(1, 'Account ID is required'),
        taskId: z.string().min(1, 'Task ID is required'),
        app: z.nativeEnum(APP),
        credits: z.number().min(1, 'Credits must be at least 1'),
        uid: z.string().min(1, 'User ID is required'),
});
export type PreDeductCredit = z.infer<typeof preDeductCreditSchema>;

// Schema for queryCredit
export const queryCreditSchema = z.object({
        userId: z.string().min(1, 'User ID is required'),
});
export type QueryCredit = z.infer<typeof queryCreditSchema>;

// Schema for commitCreditDeduction
export const commitCreditDeductionSchema = z.object({
        accountId: z.string().min(1, 'Account ID is required'),
        taskId: z.string().min(1, 'Task ID is required'),
        app: z.nativeEnum(APP),
        uid: z.string().min(1, 'User ID is required'),
});
export type CommitCreditDeduction = z.infer<typeof commitCreditDeductionSchema>;

// Schema for addPayAsGoCredit
export const addPayAsGoCreditSchema = z.object({
        accountId: z.string().min(1, 'Account ID is required'),
        credits: z.number().min(1, 'Credits must be at least 1'),
        uid: z.string().min(1, 'User ID is required'),
});
export type AddPayAsGoCredit = z.infer<typeof addPayAsGoCreditSchema>;

// Schema for resetPlanCredit
export type ResetPlanCredit = {
        accountId: string;
        credits: number;
        uid: string;
};

export const resetPlanCreditSchema = z.object({
        accountId: z.string().min(1, 'Account ID is required'),
        credits: z.number().min(0, 'Credits must be non-negative'),
        uid: z.string().min(1, 'User ID is required'),
});

// Schema for executeCreditDeduction
export const executeCreditDeductionSchema = z.object({
        accountId: z.string().min(1, 'Account ID is required'),
        credits: z.number().min(1, 'Credits must be at least 1'),
        app: z.nativeEnum(APP),
        taskId: z.string().min(1, 'Task ID is required'),
        uid: z.string().min(1, 'User ID is required'),
});
export type ExecuteCreditDeduction = z.infer<
        typeof executeCreditDeductionSchema
>;

export type CreditState = {
        pending_credits: number;
        plan_credits: number;
        pay_as_go_credits: number;
};

export type CreditRecord = CreditState & {
        id: number;
        uid: string;
        created_at: string;
        updated_at: string;
        account_id: string;
};