// 积分相关错误枚举
export enum CreditError {
        // 积分不足
        INSUFFICIENT_CREDITS = 'INSUFFICIENT_CREDITS',
        // 无效的用户ID
        INVALID_USER_ID = 'INVALID_USER_ID',
        // 数据库错误
        DATABASE_ERROR = 'DATABASE_ERROR',
        // 未知错误
        UNKNOWN_ERROR = 'UNKNOWN_ERROR',
        // 用户不存在
        USER_NOT_FOUND = 'USER_NOT_FOUND',
        // 积分记录不存在
        CREDIT_RECORD_NOT_FOUND = 'CREDIT_RECORD_NOT_FOUND',
        // 提供的积分数量无效
        INVALID_CREDITS_AMOUNT = 'INVALID_CREDITS_AMOUNT',
        // 审计记录不存在
        AUDIT_RECORD_NOT_FOUND = 'AUDIT_RECORD_NOT_FOUND',
        // 内部错误
        INTERNAL_ERROR = 'INTERNAL_ERROR',
        // 无效的请求
        INVALID_REQUEST = 'INVALID_REQUEST'
}

// 积分错误信息映射
export const CreditErrorMessages: Record<CreditError, string> = {
        // 积分不足,无法扣减
        [CreditError.INSUFFICIENT_CREDITS]:
                'Insufficient credits available for deduction.',
        // 提供的用户ID无效
        [CreditError.INVALID_USER_ID]: 'The provided user ID is invalid.',
        // 访问数据库时发生错误
        [CreditError.DATABASE_ERROR]:
                'An error occurred while accessing the database.',
        // 发生未知错误
        [CreditError.UNKNOWN_ERROR]: 'An unknown error has occurred.',
        // 用户不存在
        [CreditError.USER_NOT_FOUND]: 'User not found.',
        // 积分记录不存在
        [CreditError.CREDIT_RECORD_NOT_FOUND]: 'Credit record not found.',
        // 提供的积分数量无效
        [CreditError.INVALID_CREDITS_AMOUNT]: 'Invalid credits amount provided.',
        // 审计记录不存在
        [CreditError.AUDIT_RECORD_NOT_FOUND]: 'Audit record not found.',
};

export function getCreditErrorType(error: CreditError): {
        code: CreditError;
        message: string;
} {
        return {
                code: error,
                message: CreditErrorMessages[error],
        };
}
