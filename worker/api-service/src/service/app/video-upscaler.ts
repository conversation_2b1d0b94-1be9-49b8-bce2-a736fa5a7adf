import { TaskInfoRequest, TaskInfoResult, TaskStatus } from "../../model/task";

// Env 类型定义
export interface Env {
        VIDEO_UPSCALER_API_URL?: string;
        VIDEO_UPSCALER_TOKEN?: string;
}

// 视频放大任务响应类型（根据实际返回数据结构定义）
interface VideoUpscalerResponse {
        taskId: string;
        taskDetails: {
                id: string;
                status: string;
                request?: {
                        mimeType: string;
                        model: string;
                        video_quality: string;
                        video_url: string;
                        enable_upscale: boolean;
                };
                serverId?: string;
                result?: {
                        backend_task_id?: string;
                        data?: {
                                result_url?: string;
                                thumbnail_url?: string;
                        };
                        metadata?: {
                                server_id?: string;
                                processing_time?: number;
                                model_time?: number;
                                queue_time?: number;
                                progress?: number;
                                status?: string;
                        };
                        task_id?: string;
                };
                createdAt?: number;
                updatedAt?: number;
                callbackUrl?: string;
        };
}

/**
 * 转换状态字符串为 TaskStatus 枚举
 */
function parseTaskStatus(status: string): TaskStatus {
        switch (status) {
                case 'WAITING':
                        return TaskStatus.WAITING;
                case 'PROCESSING':
                        return TaskStatus.PROCESSING;
                case 'FINISHED':
                        return TaskStatus.FINISHED;
                case 'SUCCESS':
                        return TaskStatus.SUCCESS;
                case 'FAILED':
                        return TaskStatus.FAILED;
                case 'CANCEL':
                        return TaskStatus.CANCEL;
                default:
                        console.warn(`Unknown task status: ${status}, defaulting to UNKNOWN`);
                        return TaskStatus.UNKNOWN;
        }
}

export async function doCreateVideoUpscalerTask(task: TaskInfoRequest, env: Env): Promise<TaskInfoResult> {
        console.log('doCreateVideoUpscalerTask params:', JSON.stringify(task.params, null, 2));
        try {
                // 检查必需的环境变量
                if (!env.VIDEO_UPSCALER_API_URL) {
                        throw new Error('VIDEO_UPSCALER_API_URL environment variable is not configured');
                }
                if (!env.VIDEO_UPSCALER_TOKEN) {
                        throw new Error('VIDEO_UPSCALER_TOKEN environment variable is not configured');
                }
                
                const url = env.VIDEO_UPSCALER_API_URL;
                
                const requestBody = {                                
                        enable_upscale: task.params.enableUpscale,
                        mimeType: task.params.mimeType,
                        model: task.params.model,
                        video_quality: task.params.videoQuality,
                        video_url: task.params.videoUrl
                };
                
                console.log('Creating task with URL:', url);
                console.log('Request body:', JSON.stringify(requestBody, null, 2));

                const result = await fetch(url, {
                        method: 'POST',
                        headers: {
                                'Content-Type': 'application/json',
                                'accept': 'application/json',
                                'Authorization': `Bearer ${env.VIDEO_UPSCALER_TOKEN}`
                        },
                        body: JSON.stringify(requestBody)
                });

                console.log('Response status:', result.status);
                console.log('Response headers:', Object.fromEntries(result.headers.entries()));

                // 读取响应内容
                const responseText = await result.text();
                console.log('Raw response:', responseText);

                if (!result.ok) {
                        throw new Error(`HTTP error! status: ${result.status}, body: ${responseText}`);
                }

                try {
                        // 解析为 JSON
                        const data = JSON.parse(responseText) as VideoUpscalerResponse;
                        console.log('Parsed response data:', data);
                        
                        // 提取 taskId 和 status
                        return {
                                taskId: data.taskId,
                                status: data.taskDetails?.status ? parseTaskStatus(data.taskDetails.status) : TaskStatus.WAITING
                        } as TaskInfoResult;
                } catch (parseError) {
                        console.error('JSON parse error:', parseError);
                        throw new Error(`Failed to parse response: ${responseText}`);
                }
        } catch (error: any) {
                console.error('Create video upscaler task error:', error);
                if (error.cause) {
                        console.error('Error cause:', error.cause);
                }
                throw new Error(`Create video upscaler task failed: ${error.message}`);
        }
}

export async function doGetVideoUpscalerTask(taskId: string, env: Env): Promise<TaskInfoResult> {
        try {
                // 检查必需的环境变量
                if (!env.VIDEO_UPSCALER_API_URL) {
                        throw new Error('VIDEO_UPSCALER_API_URL environment variable is not configured');
                }
                if (!env.VIDEO_UPSCALER_TOKEN) {
                        throw new Error('VIDEO_UPSCALER_TOKEN environment variable is not configured');
                }
                
                const url = `${env.VIDEO_UPSCALER_API_URL}/${taskId}`;
                console.log('Fetching task status from:', url);
                
                const result = await fetch(url, {
                        headers: {
                                'Authorization': `Bearer ${env.VIDEO_UPSCALER_TOKEN}`
                        }
                });

                console.log('Response status:', result.status);
                console.log('Response headers:', Object.fromEntries(result.headers.entries()));

                if (!result.ok) {
                        const errorText = await result.text();
                        console.error('Error response body:', errorText);
                        throw new Error(`HTTP error! status: ${result.status}, body: ${errorText}`);
                }
                
                const data = await result.json() as VideoUpscalerResponse;
                console.log('Response data:', JSON.stringify(data, null, 2));
                
                // 验证响应数据结构
                if (typeof data !== 'object' || data === null) {
                        throw new Error('Invalid response format');
                }

                if (!data.taskDetails) {
                        throw new Error('Missing taskDetails in response');
                }

                // 根据实际数据结构获取状态和结果
                const taskStatus = data.taskDetails.status;
                const resultUrl = data.taskDetails.result?.data?.result_url;
                
                console.log('Task status:', taskStatus, 'Result URL:', resultUrl);

                return {
                        taskId: data.taskDetails.id || data.taskId,
                        status: parseTaskStatus(taskStatus),
                        videoUrl: resultUrl,
                        thumbUrl: data.taskDetails.result?.data?.thumbnail_url,
                } as TaskInfoResult;
        } catch (error: any) {
                console.error('Get task status error:', error);
                throw new Error(`Get task status failed: ${error.message}`);
        }
}