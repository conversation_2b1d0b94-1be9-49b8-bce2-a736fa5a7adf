import { TaskInfoRequest, TaskInfoResult, TaskStatus } from "../../model/task";
import { doCreateRemoveBgTask, doGetRemoveBgTask} from "../app/remove-bg";
import { FalApiClient, FAL_ENDPOINTS } from "../../utils/fal-api";
import { getSceneTemplate } from "../cms/endpoints";

// ImageRelighter API 类型定义
interface RelightifyRequest {
    image_url: string;
    prompt: string;
}

interface RelightifyResult {
    images?: Array<{
        url: string;
        width: number;
        height: number;
        content_type?: string;
    }>;
    prompt?: string;
    seed?: number;
    has_nsfw_concepts?: boolean[];
    timings?: any;
    error?: {
        message: string;
    };
}

export async function doCreateImageRelighterTask(task: TaskInfoRequest, env: Env): Promise<TaskInfoResult> {
    // 从任务参数中提取必要的信息
    const imageUrl = task.params.imageUrl as string;

    if (!imageUrl) {
        throw new Error('Image URL is required');
    }

    // Step1: 对图片处理，移除背景
    const removeBgTask: TaskInfoRequest = {
        taskId: `rmbg-${task.taskId}`, // 使用子任务 ID
        uid: task.uid,
        app: 'remove-bg',
        accountId: task.accountId,
        status: TaskStatus.WAITING,
        source: task.source, // 从父任务继承 source
        params: { imageUrl }
    };
    
    // 创建 remove-bg 任务
    const removeBgResult = await doCreateRemoveBgTask(removeBgTask);
    if (!removeBgResult.taskId) {
        throw new Error('Failed to create remove background task');
    }
    
    // 等待任务完成
    let removeBgImageUrl: string | null = null;
    let retryCount = 0;
    const maxRetries = 30; // 最多重试 30 次
    const retryDelay = 2000; // 每次重试间隔 2 秒
    
    while (retryCount < maxRetries) {
        const taskStatus = await doGetRemoveBgTask(removeBgResult.taskId);
        
        if (taskStatus.status === TaskStatus.FINISHED || taskStatus.status === TaskStatus.SUCCESS) {
            removeBgImageUrl = taskStatus.imageUrl || null;
            break;
        } else if (taskStatus.status === TaskStatus.FAILED) {
            throw new Error('Remove background task failed');
        }
        
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        retryCount++;
    }
    
    if (!removeBgImageUrl) {
        throw new Error('Failed to get removed background image URL from remove background task');
    }
    
    console.log("Removed background image URL:", removeBgImageUrl);

    // Step2: 转换IC-Light-V2 接受的提示词参数
    const scene = task.params.scene as number;
    const refImageUrl = task.params.refImageUrl as string;
    let prompt = '';

    if (scene !== undefined) {
        // 获取CMS模版里的 scene 对应的 prompt
        const template = await getSceneTemplate(env, 'relight', scene);
        
        if (!template || !template.prompt) {
            throw new Error(`No template or prompt found for relight scene: ${scene}`);
        }
        
        prompt = template.prompt;
        console.log(`Retrieved prompt for relight scene ${scene}: ${prompt}`);
        
    } else if (refImageUrl !== undefined) {
        // 反推参考图的提示词，这里用到图像理解的能力
        // TODO: Implement image understanding to extract prompt from reference image
        throw new Error('Reference image prompt extraction not implemented yet');
    } else {
        throw new Error('Either scene or refImageUrl must be provided');
    }

    console.log("Relight prompt: ", prompt);

    // Step3: 构建 Relightify API 请求
    const requestBody: RelightifyRequest = {
        image_url: removeBgImageUrl, // 使用处理后的图片
        prompt: prompt // 使用准确的提示词
    };

    return await FalApiClient.doCreateTask({
        endpoint: FalApiClient.buildEndpoint(FAL_ENDPOINTS.ICLIGHT_V2),
        apiKey: env.FAL_API_KEY,
        payload: requestBody,
        taskName: 'Image Relighter',
        initialStatus: TaskStatus.PROCESSING
    });
}

export async function doGetRelighterTask(taskId: string, env: Env): Promise<TaskInfoResult> {
    // 自定义结果处理器
    const resultProcessor = (result: RelightifyResult): TaskInfoResult => {
        console.log("Relightify status response: ", JSON.stringify(result, null, 2));

        // 检查是否有错误
        if (result.error) {
            console.error("Relightify task failed with error:", result.error);
            return {
                taskId: taskId,
                status: TaskStatus.FAILED,
                error: result.error.message
            } as TaskInfoResult;
        }

        // 检查是否有图片结果
        if (result.images && Array.isArray(result.images) && result.images.length > 0) {
            const firstImage = result.images[0];
            
            // 验证图片URL是否存在
            if (!firstImage.url) {
                console.error("Image result missing URL:", firstImage);
                return {
                    taskId: taskId,
                    status: TaskStatus.FAILED,
                    error: "Generated image missing URL"
                } as TaskInfoResult;
            }
            
            console.log("Successfully processed image:", {
                url: firstImage.url,
                width: firstImage.width,
                height: firstImage.height,
                content_type: firstImage.content_type || 'image/jpeg'
            });
            
            return {
                taskId: taskId,
                status: TaskStatus.SUCCESS,
                imageUrl: firstImage.url,
                mimeType: firstImage.content_type || 'image/jpeg'
            } as TaskInfoResult;
        }

        // 如果没有图片也没有错误，说明任务还在处理中
        console.log("Task still processing, no images yet");
        return {
            taskId: taskId,
            status: TaskStatus.PROCESSING
        } as TaskInfoResult;
    };

    return await FalApiClient.doGetTaskStatus({
        endpoint: FalApiClient.buildEndpoint(FAL_ENDPOINTS.ICLIGHT_V2),
        apiKey: env.FAL_API_KEY,
        taskId: taskId,
        statusEndpoint: FalApiClient.buildStatusEndpoint(FAL_ENDPOINTS.ICLIGHT_V2, taskId),
        taskName: 'Image Relighter',
        resultProcessor: resultProcessor
    });
} 