import { TaskInfoRequest, TaskInfoResult, TaskStatus } from "../../model/task";
import { FalApiClient, FAL_ENDPOINTS } from "../../utils/fal-api";

// Define types for fal.ai image2svg API
interface ImageVectorizationInput {
	image_url: string;
	colormode?: 'color' | 'binary';
	hierarchical?: 'stacked' | 'cutout';
	mode?: 'spline' | 'polygon';
	filter_speckle?: number;
	color_precision?: number;
	layer_difference?: number;
	corner_threshold?: number;
	length_threshold?: number;
	max_iterations?: number;
	splice_threshold?: number;
	path_precision?: number;
}

export async function doCreateImageVectorizaitonTask(task: TaskInfoRequest, env: Env): Promise<TaskInfoResult> {
	// 构建输入参数，支持高级选项
	const input: ImageVectorizationInput = {
		image_url: task.params.imageUrl as string,
	};

	// 添加可选参数（如果在 task.params 中提供）
	if (task.params.colormode && typeof task.params.colormode === 'string') {
		input.colormode = task.params.colormode as 'color' | 'binary';
	}
	if (task.params.hierarchical && typeof task.params.hierarchical === 'string') {
		input.hierarchical = task.params.hierarchical as 'stacked' | 'cutout';
	}
	if (task.params.mode && typeof task.params.mode === 'string') {
		input.mode = task.params.mode as 'spline' | 'polygon';
	}
	if (typeof task.params.filter_speckle === 'number') {
		input.filter_speckle = task.params.filter_speckle;
	}
	if (typeof task.params.color_precision === 'number') {
		input.color_precision = task.params.color_precision;
	}
	if (typeof task.params.layer_difference === 'number') {
		input.layer_difference = task.params.layer_difference;
	}
	if (typeof task.params.corner_threshold === 'number') {
		input.corner_threshold = task.params.corner_threshold;
	}
	if (typeof task.params.length_threshold === 'number') {
		input.length_threshold = task.params.length_threshold;
	}
	if (typeof task.params.max_iterations === 'number') {
		input.max_iterations = task.params.max_iterations;
	}
	if (typeof task.params.splice_threshold === 'number') {
		input.splice_threshold = task.params.splice_threshold;
	}
	if (typeof task.params.path_precision === 'number') {
		input.path_precision = task.params.path_precision;
	}
	
	return await FalApiClient.doCreateTask({
		endpoint: FalApiClient.buildEndpoint(FAL_ENDPOINTS.IMAGE2SVG),
		apiKey: env.FAL_API_KEY,
		payload: input,
		taskName: 'Image Vectorization',
		initialStatus: TaskStatus.PROCESSING
	});
}

export async function doGetImageVectorizationTask(taskId: string, env: Env): Promise<TaskInfoResult> {
	// 自定义结果处理器，处理 SVG 特殊情况
	const resultProcessor = (result: any): TaskInfoResult => {
		// 检查是否有SVG结果
		if (result.images && result.images.length > 0 && result.images[0].url) {
			const image = result.images[0];
			return {
				taskId: taskId,
				status: TaskStatus.FINISHED,
				imageUrl: image.url,
				mimeType: image.content_type || 'image/svg+xml',
				duration: result.timings?.inference
			};
		}

		// 如果没有SVG但有结果，说明任务还在处理中
		return {
			taskId: taskId,
			status: TaskStatus.PROCESSING
		};
	};

	return await FalApiClient.doGetTaskStatus({
		endpoint: FalApiClient.buildEndpoint(FAL_ENDPOINTS.IMAGE2SVG),
		apiKey: env.FAL_API_KEY,
		taskId: taskId,
		taskName: 'Image Vectorization',
		resultProcessor: resultProcessor
	});
}