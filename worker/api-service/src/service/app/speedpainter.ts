import { TaskInfoRequest, TaskInfoResult } from "../../model/task";

export async function doCreateSpeedPainterTask(task: TaskInfoRequest, _env: Env): Promise<any> {
        try {
                // 调用 rmbg 的 worker 请求
                const url = "https://app-sp.a1d.ai/api/task";

                const result = await fetch(url, {
                        method: 'POST',
                        headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer 1234`
                        },
                        body: JSON.stringify({
                                id: task.taskId,
                                imageUrl: task.params.imageUrl,
                                mimeType: task.params.mimeType,
                                sketchDuration: task.params.sketchDuration,
                                colorFillDuration: task.params.colorFillDuration,
                                needCanvas: task.params.needCanvas,
                                canvasTitle: task.params.canvasTitle,
                                needHand: task.params.needHand,
                                handTitle: task.params.handTitle,
                                needFadeout: task.params.needFadeout,
                                fps: task.params.fps
                        })
                });

                if (!result.ok) {
                        throw new Error(`HTTP error! status: ${result.status}`);
                }

                // 读取流数据
                const text = await result.text();

                try {
                        // 解析为 JSON
                        const data = JSON.parse(text);
                        return {
                                taskId: data.id,
                                status: data.status
                        } as TaskInfoResult;
                } catch (parseError) {
                        throw new Error(`Failed to parse response: ${text}`);
                }
        } catch (error: any) {
                console.error('Create speed painter task error:', error);
                throw new Error(`Create speed painter task failed: ${error.message}`);
        }
}

export async function doGetSpeedPainterTask(taskId: string, _env: Env): Promise<TaskInfoResult> {
        try {
                const url = "https://app-sp.a1d.ai/api/task/" + taskId;
                const result = await fetch(url, {
                        headers: {
                                'Authorization': `Bearer 1234`
                        }
                });

                if (!result.ok) {
                        throw new Error(`HTTP error! status: ${result.status}`);
                }
                
                const text = await result.text();
                console.log('Raw response from SpeedPainter API:', text);
                
                const data = JSON.parse(text) as {
                    id: string;
                    status: string;
                    sketchImageUrl?: string;
                    videoUrl?: string;
                    colorImageUrl?: string;
                };

                return {
                        taskId: data.id,
                        status: data.status,
                        sketchImageUrl: data.sketchImageUrl,
                        videoUrl: data.videoUrl,        
                        colorImageUrl: data.colorImageUrl
                } as TaskInfoResult;
        } catch (error: any) {
                console.error('Get task status error:', error);
                throw new Error(`Get task status failed: ${error.message}`);
        }
}