import { TaskInfoRequest, TaskInfoResult, TaskStatus } from "../../model/task";
import { ChatCompletionMessage, extractImageUrls, streamGptRequest } from "../../utils/gpt-utils";
import { nanoid } from 'nanoid';
import { R2_PREFIX_RESULT } from "../../types";
import { UrlProcessor } from "../../utils/url-processor";
import { getSystemPrompt } from "../../config/system-prompts";

export async function doCreateChat2DesignTask(task: TaskInfoRequest, env: Env): Promise<TaskInfoResult> {
    try {
        console.log('Creating chat-2-design task:', task.taskId);

        const { imageUrls = [], prompt, jwtSource } = task.params;
        const inputImageUrls = imageUrls as string[];

        console.log(`[${task.taskId}] 请求参数:`, {
            imageUrls: inputImageUrls.length > 0 ? `${inputImageUrls.length} 张图片` : '无图片',
            prompt,
            jwtSource: jwtSource || 'none'
        });

        // Get appropriate system prompt based on JWT source
        // If JWT source is 'canva', use canva channel prompt
        const channel = jwtSource === 'canva' ? 'canva' : jwtSource === 'de' ? 'de' : undefined;
        const systemPrompt = getSystemPrompt(channel as string);

        // 构建消息，只在system prompt不为空时添加
        const messages: ChatCompletionMessage[] = [];
        
        if (systemPrompt && systemPrompt.trim() !== '') {
            messages.push({
                role: 'system',
                content: systemPrompt
            });
        }

        // 添加用户消息
        if (inputImageUrls.length > 0) {
            inputImageUrls.forEach((url: string) => {
                messages.push({
                    role: 'user',
                    content: `${url} ${prompt as string}`
                });
            });
        } else {
            messages.push({
                role: 'user',
                content: prompt as string
            });
        }

        console.log(`[${task.taskId}] 开始调用 302.ai API`);
        const startTime = Date.now();

        const fullResponse = await streamGptRequest(
            messages,
            env['302_API_KEY']
        );

        if (!fullResponse) {
            throw new Error('Failed to get a response from the GPT API');
        }

        const fullContent = fullResponse.choices[0].message.content;
        const endTime = Date.now();
        console.log(`[${task.taskId}] API 调用完成，耗时: ${endTime - startTime}ms`);

        const processedImageUrls: string[] = [];
        let message = '';

        if (fullContent) {
            const extractedUrls = extractImageUrls(fullContent);

            if (extractedUrls.length > 0) {
                console.log(`[${task.taskId}] 发现 ${extractedUrls.length} 个图片URL，开始处理`);

                for (const url of extractedUrls) {
                    try {
                        const imageResponse = await fetch(url);
                        if (!imageResponse.ok) {
                            throw new Error(`下载图片失败: ${imageResponse.status} ${imageResponse.statusText}`);
                        }

                        // Detect content type from response headers
                        const contentType = imageResponse.headers.get('content-type') || 'image/png';
                        const extension = contentType.split('/')[1] || 'png';

                        const imageData = await imageResponse.arrayBuffer();
                        const fileName = `${nanoid()}_${Date.now()}.${extension}`;
                        const pathname = `${R2_PREFIX_RESULT}/chat-2-design/${fileName}`;
                        const headers = new Headers({ 'content-type': contentType });

                        console.log(`[${task.taskId}] 上传图像到 R2: ${pathname}`);
                        await env.MY_BUCKET.put(pathname, imageData, { httpMetadata: headers });

                        // 使用 UrlProcessor 安全地构建 R2 URL
                        const r2Url = UrlProcessor.buildR2Url(env.R2_PUBLIC_DOMAIN, pathname);

                        console.log(`[${task.taskId}] 图像已上传，生成URL: ${r2Url.substring(0, 50)}...`);
                        processedImageUrls.push(r2Url);
                    } catch (uploadError) {
                        console.error(`[${task.taskId}] 上传图像到 R2 失败:`, uploadError);
                        processedImageUrls.push(url);
                    }
                }
            } else {
                message = "No images were recognized. Please adjust your prompt and try again.";
                console.log(`[${task.taskId}] 未在响应中找到图片URL`);
            }
        } else {
            message = "Failed to get a complete response from the AI.";
            console.warn(`[${task.taskId}] 未从 API 获取到任何内容`);
        }

        const result: TaskInfoResult = {
            taskId: task.taskId,
            status: TaskStatus.FINISHED,
            imageUrls: processedImageUrls,
            message: processedImageUrls.length > 0 ? '' : message
        };

        console.log('Chat-2-design task completed successfully:', result);
        return result;
    } catch (error) {
        console.error('Error creating chat-2-design task:', error);

        const errorResult: TaskInfoResult = {
            taskId: task.taskId,
            status: TaskStatus.FAILED,
            error: error instanceof Error ? error.message : 'Unknown error'
        };

        return errorResult;
    }
}

export async function doGetChat2DesignTask(taskId: string, env: Env): Promise<TaskInfoResult> {
    try {
        console.log('Getting chat-2-design task:', taskId);

        // 从数据库获取任务结果
        const { doGetTask } = await import('../supabase/task');
        const taskRecord = await doGetTask(env, taskId);

        if (!taskRecord) {
            return {
                taskId: taskId,
                status: TaskStatus.UNKNOWN,
                error: 'Task not found'
            };
        }

        // 如果任务已完成且有结果，返回完整结果
        if (taskRecord.status === TaskStatus.FINISHED && taskRecord.result) {
            return {
                taskId: taskId,
                status: TaskStatus.FINISHED,
                ...taskRecord.result
            };
        }

        // 否则返回基本状态信息
        const result: TaskInfoResult = {
            taskId: taskId,
            status: taskRecord.status as TaskStatus,
            error: taskRecord.error
        };

        return result;
    } catch (error) {
        console.error('Error getting chat-2-design task:', error);

        const errorResult: TaskInfoResult = {
            taskId: taskId,
            status: TaskStatus.FAILED,
            error: error instanceof Error ? error.message : 'Unknown error'
        };

        return errorResult;
    }
}
