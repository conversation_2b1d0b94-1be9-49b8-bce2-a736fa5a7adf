import { TaskInfoRequest, TaskInfoResult, TaskStatus } from "../../model/task";
import { SOURCE } from "../../types";
import { FalApiClient, FAL_ENDPOINTS } from "../../utils/fal-api";

interface ClarityUpscaleParams {
        imageUrl: string;
        apiKey?: string;
}

interface ImageUpscaleParams extends ClarityUpscaleParams {
        scale: number;
        headToken?: string;
}



interface ImageUpscalerResponse {
        id: string;
        status: string;
        timestamp: string;
}

interface ClarityImageResult {
        image: {
                url: string;
                content_type: string;
                file_name: string;
                file_size: number | null;
                width: number;
                height: number;
        };
        seed: number;
        timings: {
                inference: number;
        };
}

interface ImageUpscalerResult {
        id: string;
        status?: string;
        mime_type?: string;
        image_url?: string;
        thumb_url?: string;
        timestamp?: string;
        duration?: number;
}


export async function doCallClarityUpscaler(params: ClarityUpscaleParams): Promise<TaskInfoResult> {
        const FAL_API_KEY = params.apiKey || process.env.FAL_API_KEY;

        if (!FAL_API_KEY) {
                throw new Error('FAL API key is required');
        }

        return await FalApiClient.doCreateTask({
                endpoint: FalApiClient.buildEndpoint(FAL_ENDPOINTS.CLARITY_UPSCALER),
                apiKey: FAL_API_KEY,
                payload: { image_url: params.imageUrl },
                taskName: 'Clarity Upscaler',
                initialStatus: TaskStatus.WAITING
        });
}



export async function doGetClarityUpscalerTask(taskId: string, env: Env): Promise<TaskInfoResult> {
        // 自定义结果处理器，处理 Clarity Upscaler 的特殊响应格式
        const resultProcessor = (result: ClarityImageResult): TaskInfoResult => {
                // 检查是否有图片结果
                if (result.image?.url) {
                        return {
                                taskId: taskId,
                                status: TaskStatus.FINISHED,
                                imageUrl: result.image.url,
                                mimeType: result.image.content_type,
                                duration: result.timings?.inference
                        };
                }

                // 如果没有图片URL但有结果，说明任务还在处理中
                return {
                        taskId: taskId,
                        status: TaskStatus.PROCESSING
                };
        };

        return await FalApiClient.doGetTaskStatus({
                endpoint: FalApiClient.buildEndpoint(FAL_ENDPOINTS.CLARITY_UPSCALER),
                apiKey: env.FAL_API_KEY,
                taskId: taskId,
                taskName: 'Clarity Upscaler',
                resultProcessor: resultProcessor
        });
}

export async function doGetImageUpscalerTask(taskId: string, _env: Env): Promise<TaskInfoResult> {
        try {
                const IU_ENDPOINT = `https://app-iu.a1d.ai/api/task/${taskId}`;
                const response = await fetch(IU_ENDPOINT, {
                        headers: {
                                'Authorization': `Bearer 1234`,
                                'Content-Type': 'application/json'
                        }
                });

                // 处理 404 错误
                if (response.status === 404) {
                        return {
                                taskId: taskId,
                                status: TaskStatus.PROCESSING
                        } as TaskInfoResult;
                }

                // 如果不是 404，但也不是成功状态，抛出错误
                if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json() as ImageUpscalerResult;
                if (result.image_url) {
                        return {
                                taskId: taskId,
                                status: TaskStatus.FINISHED,
                                imageUrl: result.image_url,
                                mimeType: result.mime_type,
                                duration: result.duration,
                                thumbUrl: result.thumb_url
                        } as TaskInfoResult;
                }

                return {
                        taskId: taskId,
                        status: TaskStatus.PROCESSING
                } as TaskInfoResult;
        } catch (error) {
                console.error('Error in doGetImageUpscalerTask:', error);
                throw new Error('Failed to get image upscaler task');
        }
}



export async function doCallImageUpscaler(params: ImageUpscaleParams): Promise<TaskInfoResult> {
        const IU_ENDPOINT = 'https://app-iu.a1d.ai/api/task';
        // const IU_HEAD_TOKEN = params.headToken ;

        // if (!IU_HEAD_TOKEN) {
        //         throw new Error('IU head token is required');
        // }
        try {
                const response = await fetch(IU_ENDPOINT, {
                        method: 'POST',
                        headers: {
                                'Authorization': `Bearer 1234`,
                                'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                                image_url: params.imageUrl,
                                scale: params.scale
                        })
                });

                if (!response.ok) {
                        throw new Error(`Upscale failed: ${response.statusText}`);
                }

                const result = await response.json() as ImageUpscalerResponse;
                return {
                        taskId: result.id,
                        status: TaskStatus.WAITING
                };
        } catch (error) {
                console.error('Error in doCallImageUpscaler:', error);
                throw new Error('Failed to upscale image');
        }

}


export async function doCreateImageUpscalerTask(task: TaskInfoRequest, env: Env, headToken?: string): Promise<TaskInfoResult> {
        const imageUrl = task.params.imageUrl;

        switch (task.source) {
                case SOURCE.FRAMER:
                        return await doCallClarityUpscaler({
                                imageUrl: imageUrl as string,
                                apiKey: env.FAL_API_KEY
                        });
                case SOURCE.API:
                case SOURCE.WEB:
                case SOURCE.CANVA:
                case SOURCE.FIGMA:
                case SOURCE.RAPIDAPI:
                case SOURCE.MCP:
                        return await doCallImageUpscaler({
                                imageUrl: imageUrl as string,
                                headToken: headToken as string,
                                // 默认放大倍数为2
                                scale: Number(task.params.scale) || 2
                        });
                default:
                        throw new Error(`Unsupported source: ${task.source}`);
        }
}