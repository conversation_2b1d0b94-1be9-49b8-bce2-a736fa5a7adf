import { TaskInfoRequest, TaskInfoResult, TaskStatus } from "../../model/task";
import { FalApiClient, FAL_ENDPOINTS } from "../../utils/fal-api";


// Ideogram V3 Reframe API 类型定义
interface IdeogramImageSize {
	width?: number;
	height?: number;
}

type IdeogramImageSizeEnum = 'square_hd' | 'square' | 'portrait_4_3' | 'portrait_16_9' | 'landscape_4_3' | 'landscape_16_9';

interface IdeogramReframeRequest {
	image_url: string;
	image_size: IdeogramImageSizeEnum | IdeogramImageSize;
	num_images?: number;
	seed?: number;
	rendering_speed?: 'TURBO' | 'BALANCED' | 'QUALITY';
}

interface IdeogramFile {
	url: string;
	content_type?: string;
	file_name?: string;
	file_size?: number;
}

// Ideogram 状态查询的实际响应格式
// 注意：当任务完成时，直接返回结果，无状态字段
// 当任务还在处理时，可能返回 404 或空响应
interface IdeogramActualResponse {
	images: IdeogramFile[];
	seed: number;
	error?: {
		message: string;
		code?: string;
	};
}

export async function doCreateImageExtendsTask(task: TaskInfoRequest, env: Env): Promise<TaskInfoResult> {
	// 从任务参数中提取必要的信息
	const imageUrl = task.params.imageUrl as string;
	const imageSize = task.params.imageSize as IdeogramImageSizeEnum || 'square_hd';
	
	if (!imageUrl) {
		throw new Error('Image URL is required');
	}

	// 构建 Ideogram API 请求
	const requestBody: IdeogramReframeRequest = {
		image_url: imageUrl,
		image_size: imageSize,
		rendering_speed: 'BALANCED'
	};

	// 如果有自定义尺寸，使用自定义尺寸
	if (task.params.width && task.params.height) {
		requestBody.image_size = {
			width: task.params.width as number,
			height: task.params.height as number
		};
	}

	return await FalApiClient.doCreateTask({
		endpoint: FalApiClient.buildEndpoint(FAL_ENDPOINTS.IDEOGRAM_V3_REFRAME),
		apiKey: env.FAL_API_KEY,
		payload: requestBody,
		taskName: 'Image Extends (Ideogram)',
		initialStatus: TaskStatus.PROCESSING
	});
}

export async function doGetImageExtendsTask(taskId: string, env: Env): Promise<TaskInfoResult> {
	// 自定义结果处理器，处理 Ideogram 实际的响应格式
	const resultProcessor = (result: IdeogramActualResponse): TaskInfoResult => {
		console.log("Ideogram status response: ", result);

		// 检查是否有错误
		if (result.error) {
			return {
				taskId: taskId,
				status: TaskStatus.FAILED,
				error: result.error.message
			} as TaskInfoResult;
		}

		// 检查是否有图片结果
		if (result.images && result.images.length > 0) {
			const firstImage = result.images[0];
			return {
				taskId: taskId,
				status: TaskStatus.SUCCESS,
				imageUrl: firstImage.url,
				mimeType: firstImage.content_type
			} as TaskInfoResult;
		}

		// 如果没有图片也没有错误，说明任务还在处理中
		return {
			taskId: taskId,
			status: TaskStatus.PROCESSING
		} as TaskInfoResult;
	};

	return await FalApiClient.doGetTaskStatus({
		endpoint: FalApiClient.buildEndpoint(FAL_ENDPOINTS.IDEOGRAM_V3_REFRAME),
		apiKey: env.FAL_API_KEY,
		taskId: taskId,
		statusEndpoint: FalApiClient.buildStatusEndpoint(FAL_ENDPOINTS.IDEOGRAM_V3_REFRAME, taskId),
		taskName: 'Image Extends (Ideogram)',
		resultProcessor: resultProcessor
	});
}