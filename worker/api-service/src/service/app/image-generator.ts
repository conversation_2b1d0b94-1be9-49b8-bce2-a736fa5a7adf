/// <reference path="../../../worker-configuration.d.ts" />
import { TaskInfoRequest, TaskInfoResult, TaskStatus } from "../../model/task";
import { FalApiClient, FAL_ENDPOINTS } from "../../utils/fal-api";

// 根据 fal.ai 官方文档定义的类型
interface RGBColor {
    r: number;
    g: number;
    b: number;
}

// 图片尺寸类型，支持预定义尺寸和自定义尺寸
type ImageSize = 
    | 'square_hd' 
    | 'square' 
    | 'portrait_4_3' 
    | 'portrait_16_9' 
    | 'landscape_4_3' 
    | 'landscape_16_9'
    | { width: number; height: number };

// 样式枚举类型
type StyleEnum = 
    | 'any'
    | 'realistic_image'
    | 'digital_illustration'
    | 'vector_illustration'
    | string; // 支持其他样式值

interface RecraftV3Params {
    prompt: string;
    image_size?: ImageSize;
    style?: StyleEnum;
    colors?: RGBColor[];
    style_id?: string;
    enable_safety_checker?: boolean;
    apiKey?: string;
}

// fal.ai 结果响应格式
interface RecraftV3ImageFile {
    url: string;
    content_type?: string;
    file_name?: string;
    file_size?: number;
    file_data?: string;
}

interface RecraftV3ImageGeneratorResult {
    images: RecraftV3ImageFile[];
}

export async function doCallRecraftV3ImageGenerator(params: RecraftV3Params): Promise<TaskInfoResult> {
    const FAL_API_KEY = params.apiKey || process.env.FAL_API_KEY;

    if (!FAL_API_KEY) {
        throw new Error('FAL API key is required');
    }

    // 构建符合 fal.ai 官方 API 规范的请求体
    const requestBody: any = {
        prompt: params.prompt,
    };

    // 添加可选参数
    if (params.image_size) {
        requestBody.image_size = params.image_size;
    } else {
        requestBody.image_size = 'square_hd'; // 默认值
    }

    if (params.style) {
        requestBody.style = params.style;
    }

    if (params.colors && params.colors.length > 0) {
        requestBody.colors = params.colors;
    }

    if (params.style_id) {
        requestBody.style_id = params.style_id;
    }

    if (params.enable_safety_checker !== undefined) {
        requestBody.enable_safety_checker = params.enable_safety_checker;
    }
    
    console.log('Sending image generation request with body:', JSON.stringify(requestBody));

    return await FalApiClient.doCreateTask({
        endpoint: FalApiClient.buildEndpoint(FAL_ENDPOINTS.RECRAFT_V3),
        apiKey: FAL_API_KEY,
        payload: requestBody,
        taskName: 'Image Generator',
        initialStatus: TaskStatus.WAITING
    });
}

export async function doGetRecraftV3ImageGeneratorTask(taskId: string, env: Env): Promise<TaskInfoResult> {
    // 自定义结果处理器，处理图片生成的响应格式和错误处理
    const resultProcessor = (result: RecraftV3ImageGeneratorResult): TaskInfoResult => {
        if (!result) {
            return {
                taskId: taskId,
                status: TaskStatus.FAILED,
                error: 'Invalid response from image generation service'
            };
        }

        if (result.images && result.images.length > 0) {
            return {
                taskId: taskId,
                status: TaskStatus.FINISHED,
                imageUrl: result.images[0].url
            };
        }

        return {
            taskId: taskId,
            status: TaskStatus.PROCESSING
        };
    };

    // 使用正确的 Recraft 状态查询端点
    const statusEndpoint = `https://queue.fal.run/fal-ai/recraft/requests/${taskId}`;

    return await FalApiClient.doGetTaskStatus({
        endpoint: FalApiClient.buildEndpoint(FAL_ENDPOINTS.RECRAFT_V3),
        apiKey: env.FAL_API_KEY,
        taskId: taskId,
        statusEndpoint: statusEndpoint,
        resultProcessor: resultProcessor
    });
}

export async function doCreateImageGeneratorTask(task: TaskInfoRequest, env: Env): Promise<TaskInfoResult> {
    // 直接从任务参数中提取所需的数据
    const {
        prompt,
        image_size,
        style,
        colors,
        style_id,
        enable_safety_checker
    } = task.params;

    // 处理颜色数组格式
    const processedColors = colors ? (Array.isArray(colors) ? colors : [colors]).map(color => {
        if (typeof color === 'string') {
            const [r, g, b] = color.split(',').map(Number);
            return { r, g, b } as RGBColor;
        }
        return color as RGBColor;
    }) : [] as RGBColor[];

    // 调用图片生成服务
    return await doCallRecraftV3ImageGenerator({
        prompt: prompt as string,
        image_size: image_size as ImageSize,
        style: style as StyleEnum,
        colors: processedColors,
        style_id: style_id as string,
        enable_safety_checker: enable_safety_checker as boolean,
        apiKey: env.FAL_API_KEY
    });
} 