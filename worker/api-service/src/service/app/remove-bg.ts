import { TaskInfoRequest, TaskInfoResult, TaskStatus } from "../../model/task";
// Bindings import is no longer needed if env is removed
// import { Bindings } from "../../types"; 

// --- Hardcoded Values --- 
// TODO: Replace placeholders with your actual token and URL
const HARDCODED_RMBG_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwicm9sZXMiOlsiYWRtaW4iLCJ1c2VyIl0sImlhdCI6MTUyNjIzOTAyMn0.PTcqwU7B_yMxZZoHXcnD1c576fTF6pDif1L_SaKHIag"; 
const HARDCODED_RMBG_API_URL = "https://app-rmbg.a1d.ai"; // e.g., "https://api.example.com"
// ------------------------

// Define interface for the nested JSON response
interface RemoveBgApiResponse {
        taskId: string;
        taskDetails: {
                id: string;
                status: TaskStatus;
                request?: {
                        url?: string;
                };
                serverId?: string;
                result?: {
                        backend_task_id?: string;
                        data?: {
                                result_url?: string;
                        };
                        metadata?: any;
                        task_id?: string;
                };
                createdAt?: number;
                updatedAt?: number;
                callbackUrl?: string;
        };
}

export async function doCreateRemoveBgTask(task: TaskInfoRequest): Promise<TaskInfoResult> {
        try {
                const url = HARDCODED_RMBG_API_URL + "/api/task"; // Use hardcoded base URL
                const token = HARDCODED_RMBG_TOKEN; // Use hardcoded token
                
                const result = await fetch(url, {
                        method: 'POST',
                        headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token}` // Use token constant
                        },
                        body: JSON.stringify({
                                url: task.params.imageUrl
                        })
                });

                if (!result.ok) {
                        const errorText = await result.text().catch(() => 'Failed to read error body');
                        throw new Error(`HTTP error! status: ${result.status}, body: ${errorText}`);
                }

                const text = await result.text();

                try {
                        const data = JSON.parse(text) as RemoveBgApiResponse;
                        if (!data.taskDetails) {
                                throw new Error('Invalid response structure: missing taskDetails');
                        }
                        return {
                                taskId: data.taskDetails.id,
                                status: data.taskDetails.status
                        } as TaskInfoResult;
                } catch (parseError) {
                        console.error('Failed to parse remove background response:', text, parseError);
                        if (parseError instanceof Error) {
                                throw new Error(`Failed to parse response: ${parseError.message}. Original text: ${text}`);
                        } else {
                                throw new Error(`Failed to parse response. Original text: ${text}`);
                        }
                }
        } catch (error) {
                console.error('Remove background error:', error);
                if (error instanceof Error) {
                        throw new Error(`Remove background failed: ${error.message}`);
                } else {
                        throw new Error(`Remove background failed: ${String(error)}`);
                }
        }
}

export async function doGetRemoveBgTask(taskId: string): Promise<TaskInfoResult> {
        try {
                console.log(`[doGetRemoveBgTask] Fetching task ${taskId} from external API`);
                const url = HARDCODED_RMBG_API_URL + `/api/task/${taskId}`; // Use hardcoded base URL
                const token = HARDCODED_RMBG_TOKEN; // Use hardcoded token
                
                const result = await fetch(url, {
                        headers: {
                                'Authorization': `Bearer ${token}` // Use token constant
                        }
                });

                if (!result.ok) {
                        const errorText = await result.text().catch(() => 'Failed to read error body');
                        console.error(`[doGetRemoveBgTask] HTTP error for task ${taskId}: ${result.status}, body: ${errorText}`);
                        throw new Error(`HTTP error! status: ${result.status}, body: ${errorText}`);
                }
                const data = await result.json() as RemoveBgApiResponse;
                console.log(`[doGetRemoveBgTask] External API response for task ${taskId}:`, data);

                if (!data.taskDetails) {
                        console.error(`[doGetRemoveBgTask] Invalid response structure for task ${taskId}: missing taskDetails`);
                        throw new Error('Invalid response structure: missing taskDetails');
                }
                if (data.taskDetails.status === TaskStatus.FINISHED && (!data.taskDetails.result?.data?.result_url)) {
                        console.warn(`[doGetRemoveBgTask] Task ${taskId} finished but result_url is missing:`, data);
                }

                const resultData = {
                        taskId: data.taskDetails.id,
                        status: data.taskDetails.status,
                        imageUrl: data.taskDetails.result?.data?.result_url ?? null
                } as TaskInfoResult;

                console.log(`[doGetRemoveBgTask] Returning result for task ${taskId}:`, resultData);
                return resultData;
        } catch (error) {
                console.error(`[doGetRemoveBgTask] Error getting task ${taskId}:`, error);
                if (error instanceof Error) {
                        throw new Error(`Get task status failed: ${error.message}`);
                } else {
                        throw new Error(`Get task status failed: ${String(error)}`);
                }
        }
}