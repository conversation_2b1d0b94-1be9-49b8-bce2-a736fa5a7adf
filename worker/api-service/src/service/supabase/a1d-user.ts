import { A1dUser } from './models';
import { getClient } from './index';
import { BaseError, createDatabaseError, createDataNotFoundError } from './errors';

const A1D_USER_TABLE = 'a1d_user';
const ACCOUNT_TABLE = 'accounts';

// 插入a1d_user
export async function doInsertA1dUser(user: A1dUser, env: Env) {
	try {
		const { data, error } = await getClient(env).from(A1D_USER_TABLE).insert(user).select().single();

		if (error) {
			throw createDatabaseError('Database insert failed', error.message, error.code);
		}
		return data;
	} catch (error) {
		if ((error as BaseError)?.name === 'DatabaseError') {
			throw error;
		}
		console.error('Error inserting user into Supabase:', error);
		throw createDatabaseError('Unexpected database error', error instanceof Error ? error.message : undefined);
	}
}

// 查询函数
export async function doGetA1dUser(env: Env, source?: string, sourceId?: string, uid?: string, apiKey?: string) {
	try {
		let query = getClient(env).from(A1D_USER_TABLE).select('*');

		if (source) {
			query = query.eq('source', source);
		}
		if (sourceId) {
			query = query.eq('source_id', sourceId);
		}
		if (uid) {
			query = query.eq('uid', uid);
		}
		if (apiKey) {
			query = query.eq('api_key', apiKey);
		}

		const { data, error } = await query.maybeSingle();

		if (error) {
			throw createDatabaseError('Database query failed', error.message, error.code);
		}
		if (!data) {
			throw createDataNotFoundError('A1d user not found');
		}
		return data;
	} catch (error) {
		if ((error as BaseError)?.name === 'DataNotFoundError' || (error as BaseError)?.name === 'DatabaseError') {
			throw error;
		}
		console.error('Error retrieving user from Supabase:', error);
		throw createDatabaseError('Unexpected database error', error instanceof Error ? error.message : undefined);
	}
}

// 根据accountId获取用户信息
export async function doGetAccountInfo(env: Env, accountId: string) {
	try {
		const { data, error } = await getClient(env)
			.from(ACCOUNT_TABLE)
			.select('primary_owner_user_id,name, email, picture_url')
			.eq('id', accountId)
			.single();

		if (error) {
			throw createDatabaseError('Database query failed', error.message, error.code);
		}

		if (!data) {
			throw createDataNotFoundError('Account not found');
		}

		return data;
	} catch (error) {
		if ((error as BaseError)?.name === 'DataNotFoundError' || (error as BaseError)?.name === 'DatabaseError') {
			throw error;
		}
		console.error('Error retrieving account info from Supabase:', error);
		throw createDatabaseError('Unexpected database error', error instanceof Error ? error.message : undefined);
	}
}
