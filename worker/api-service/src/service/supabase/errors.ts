export class BaseError extends Error {
  type: string;
  code?: string;

  constructor(name: string, message: string, type: string, code?: string) {
    super(message);
    this.name = name;
    this.type = type;
    this.code = code;
  }
}

export class DatabaseError extends BaseError {
  constructor(message: string, code?: string) {
    super('DatabaseError', message, 'DATABASE_ERROR', code);
  }
}

export class DataNotFoundError extends BaseError {
  constructor(message: string) {
    super('DataNotFoundError', message, 'NOT_FOUND');
  }
}

export function createDatabaseError(message: string, details?: string, code?: string): DatabaseError {
  return new DatabaseError(`${message}${details ? `: ${details}` : ''}`, code);
}

export function createDataNotFoundError(message: string): DataNotFoundError {
  return new DataNotFoundError(message);
}

export type SupabaseError = DatabaseError | DataNotFoundError; 