import { getClient } from './index';
import { TaskStatus } from '../../model/task';

const TASK_TABLE = 'task';

export interface Task {
    id?: number;
    uid: string;
    task_id: string;
    app: string;
    source: string;
    credit: number;
    status: string;
    error?: any;
    input_params?: any;
    result?: any;
    extra_info?: any;
    created_at?: string;
    updated_at?: string;
    deleted_at?: string;
}

export type TaskUpdateInput = {
    task_id: string;  // Required
    uid?: string;
    app?: string;
    source?: string;
    status?: TaskStatus;
    error?: string;
    result?: any;
    input_params?: Record<string, unknown>;
    extra_info?: Record<string, unknown>;
    credit?: number;
};

export async function doGetTask(env: Env, taskId: string): Promise<Task | null> {
    const supabase = getClient(env);
    const { data, error } = await supabase
        .from(TASK_TABLE)
        .select()
        .eq('task_id', taskId)
        .is('deleted_at', null)
        .maybeSingle();

    if (error) {
        console.error('Error getting task:', error);
        throw error;
    }

    return data;
}

export async function doInsertTask(task: Task, env: Env): Promise<void> {
    const supabase = getClient(env);
    const { error } = await supabase
        .from(TASK_TABLE)
        .insert(task);

    if (error) {
        console.error('Error inserting task:', error);
        throw error;
    }
}

export async function doUpdateTask(task: TaskUpdateInput, env: Env): Promise<void> {
    const supabase = getClient(env);
    
    // Build update object with only provided fields
    const updateFields: Record<string, any> = {
        updated_at: new Date().toISOString()
    };

    // Only include fields that are defined
    if (task.status !== undefined) updateFields.status = task.status;
    if (task.error !== undefined) updateFields.error = task.error;
    if (task.result !== undefined) updateFields.result = task.result;
    if (task.input_params !== undefined) updateFields.input_params = task.input_params;
    if (task.extra_info !== undefined) updateFields.extra_info = task.extra_info;
    

    const query = supabase
        .from(TASK_TABLE)
        .update(updateFields)
        .eq('task_id', task.task_id);

    // Add uid condition if provided
    if (task.uid) {
        query.eq('uid', task.uid);
    }

    const { error } = await query;

    if (error) {
        console.error('Error updating task:', error);
        throw error;
    }
}

/**
 * 查找超时的任务
 * @param env 环境变量
 * @param staleMinutes 超时时间（分钟）
 * @param limit 每次查询的任务数量限制
 */
export async function doFindStaleTasks(env: Env, staleMinutes: number, limit: number = 50): Promise<Task[]> {
    const { data: tasks, error } = await getClient(env)
        .from(TASK_TABLE)
        .select('*')
        .in('status', [TaskStatus.WAITING, TaskStatus.PROCESSING])
        .lt('created_at', new Date(Date.now() - staleMinutes * 60 * 1000).toISOString())
        .is('deleted_at', null)
        .limit(limit);

    if (error) {
        console.error('Error finding stale tasks:', error);
        throw new Error(`Failed to find stale tasks: ${error.message}`);
    }

    return tasks || [];
}

/**
 * 删除任务的返回结果类型
 */
export enum DeleteTaskResult {
    SUCCESS = 'SUCCESS',          // 删除成功
    ALREADY_DELETED = 'ALREADY_DELETED', // 任务已被删除
    NOT_FOUND = 'NOT_FOUND',      // 任务不存在
    FAIL = 'FAIL'           // 其他失败
}

/**
 * 删除任务（将任务标记为已删除）
 * @param env 环境变量
 * @param taskId 任务ID
 * @param uid 用户ID
 * @returns 操作结果
 */
export async function doDeleteTask(env: Env, taskId: string, uid: string): Promise<DeleteTaskResult> {
    const supabase = getClient(env);
    
    try {
        // 首先检查任务是否存在且未被删除
        const { data: taskData, error: checkError } = await supabase
            .from(TASK_TABLE)
            .select('deleted_at')
            .eq('task_id', taskId)
            .eq('uid', uid)
            .maybeSingle();
        
        if (checkError) {
            console.error('Error checking task existence:', checkError);
            throw checkError;
        }
        
        // 如果任务不存在
        if (!taskData) {
            console.log(`Task not found: ${taskId} for user ${uid}`);
            return DeleteTaskResult.NOT_FOUND;
        }
        
        // 如果任务已被删除
        if (taskData.deleted_at) {
            console.log(`Task already deleted: ${taskId} for user ${uid}`);
            return DeleteTaskResult.ALREADY_DELETED;
        }
        
        // 使用 .select() 来获取返回值
        const { data, error } = await supabase
            .from(TASK_TABLE)
            .update({
                deleted_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            })
            .eq('task_id', taskId)
            .eq('uid', uid)
            .select();
        
        console.log('Delete task response:', { data, error });
        
        if (error) {
            console.error('Error deleting task:', error);
            throw error;
        }
        
        // 根据返回的data判断操作是否成功
        if (Array.isArray(data) && data.length > 0) {
            return DeleteTaskResult.SUCCESS;
        } else {
            return DeleteTaskResult.FAIL;        
        }
    } catch (err) {
        console.error('Unexpected error in doDeleteTask:', err);
        throw err;
    }
}

/**
 * 用户任务分页查询参数
 */
export interface GetUserTasksParams {
    uid: string;
    page: number;
    pageSize: number;
    app?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
}

/**
 * 用户任务分页查询结果
 */
export interface GetUserTasksResult {
    tasks: Task[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
}

/**
 * 获取用户任务列表（带分页）
 * @param env 环境变量
 * @param params 查询参数
 * @returns 任务列表和分页信息
 */
export async function doGetUserTasks(env: Env, params: GetUserTasksParams): Promise<GetUserTasksResult> {
    const { uid, page = 1, pageSize = 10, app, status, startDate, endDate } = params;
    
    const supabase = getClient(env);
    
    // 构建查询条件
    let query = supabase
        .from(TASK_TABLE)
        .select('*', { count: 'exact' })
        .eq('uid', uid)
        .is('deleted_at', null);
    
    // 应用可选过滤条件
    if (app) {
        query = query.eq('app', app);
    }
    
    if (status) {
        query = query.eq('status', status);
    }
    
    if (startDate) {
        query = query.gte('created_at', `${startDate}T00:00:00Z`);
    }
    
    if (endDate) {
        query = query.lte('created_at', `${endDate}T23:59:59.999Z`);
    }
    
    // 计算分页
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;
    
    // 应用分页并排序
    query = query
        .order('created_at', { ascending: false })
        .range(from, to);
    
    const { data: tasks, error, count } = await query;
    
    if (error) {
        console.error('Error getting user tasks:', error);
        throw error;
    }
    
    const total = count || 0;
    const totalPages = Math.ceil(total / pageSize);
    
    return {
        tasks: tasks || [],
        total,
        page,
        pageSize,
        totalPages
    };
}
