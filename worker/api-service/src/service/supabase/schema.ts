import { z } from 'zod';

export const enum CreditOperationType {
        PRE_DEDUCT = 'pre_deduct',
        COMMIT_DEDUCTION = 'commit_deduction',
        EXECUTE_DEDUCTION = 'execute_deduction',
        ADD_PAY_AS_GO = 'add_pay_as_go',
        RESET_PLAN = 'reset_plan',
}

// Schema for preDeductCredit
export const preDeductCreditSchema = z.object({
        userId: z.string().min(1, 'User ID is required'),
        jobId: z.string().min(1, 'Job ID is required'),
        credits: z.number().min(1, 'Credits must be at least 1'),
});
export type PreDeductCredit = z.infer<typeof preDeductCreditSchema>;

// Schema for queryCredit
export const queryCreditSchema = z.object({
        userId: z.string().min(1, 'User ID is required'),
});
export type QueryCredit = z.infer<typeof queryCreditSchema>;

// Schema for commitCreditDeduction
export const commitCreditDeductionSchema = z.object({
        userId: z.string().min(1, 'User ID is required'),
        jobId: z.string().min(1, 'Job ID is required'),
});
export type CommitCreditDeduction = z.infer<typeof commitCreditDeductionSchema>;

// Schema for addPayAsGoCredit
export const addPayAsGoCreditSchema = z.object({
        userId: z.string().min(1, 'User ID is required'),
        credits: z.number().min(1, 'Credits must be at least 1'),
});
export type AddPayAsGoCredit = z.infer<typeof addPayAsGoCreditSchema>;

// Schema for resetPlanCredit
export const resetPlanCreditSchema = z.object({
        userId: z.string().min(1, 'User ID is required'),
        newPlanCredits: z.number().min(0, 'New plan credits must be non-negative'),
});
export type ResetPlanCredit = z.infer<typeof resetPlanCreditSchema>;

// Schema for executeCreditDeduction
export const executeCreditDeductionSchema = z.object({
        userId: z.string().min(1, 'User ID is required'),
        credits: z.number().min(1, 'Credits must be at least 1'),
});
export type ExecuteCreditDeduction = z.infer<
        typeof executeCreditDeductionSchema
>;
