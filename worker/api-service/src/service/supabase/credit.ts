// import { SupabaseClient, createClient } from '@supabase/supabase-js';
import { getClient } from './index';
//import { Credit } from './models';
const CREDIT_TABLE = 'credit';

export async function doGetUserCredit(env: Env, accountId: string) {
        try {
                if (!accountId) {
                        throw new Error('Account ID is required');
                }

                console.log('Querying credit for user:', { accountId });

                const { data, error } = await getClient(env)
                        .from(CREDIT_TABLE)
                        .select('*')
                        .eq('account_id', accountId)
                        .maybeSingle();

                if (error) {
                        console.error("Supabase query error:", {
                                message: error.message,
                                details: error.details,
                                hint: error.hint,
                                code: error.code,
                        });
                        throw error;
                }

                if (!data) {
                        console.log('No credit record found for user:', { accountId });
                        return null;
                }

                console.log('Credit record found:', { accountId });
                return data;
        } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
                console.error("Error retrieving user credit:", {
                        error: errorMessage,
                        accountId,
                });
                throw error; // 直接抛出原始错误，保留错误堆栈
        }
}

// // 插入 Credit
// export async function doInsertUserCredit(credit: Omit<Credit, 'id'>, env: Env): Promise<Credit> {
//         try {
//                 const { data, error } = await getClient(env)
//                         .from(CREDIT_TABLE)
//                         .insert(credit)
//                         .select()
//                         .single();

//                 if (error) {
//                         console.error('Insert credit error:', error);
//                         throw new Error(error.message);
//                 }

//                 return data;
//         } catch (error: any) {
//                 console.error('Error inserting credit:', {
//                         error: error.message || 'Unknown error occurred',
//                         accountId: credit.account_id
//                 });
//                 throw error;
//         }
// }

// // 添加一个测试连接的函数
// export async function testDatabaseConnection(env: Env) {
//     try {
//         // const client = getClient(env);
        
//         // 最简单的查询，检查数据库是否响应
//         const { data, error } = await client
//             .from('credit')
//             .select('*')
//             .limit(1);

//         if (error) {
//             console.error("Database connection test failed:", error);
//             throw error;
//         }

//         console.log("Database connection test successful:", data);
//         return true;
//     } catch (error) {
//         console.error("Database connection test error:", error);
//         throw error;
//     }
// }
