import { getClient } from './index';

const CREDIT_RULES_TABLE = 'credit_rules';

export interface CreditRule {
  id: number;
  app: string;
  source: string;
  base_credits: number;
  has_dynamic_rule: boolean;
  dynamic_rule_config: any;
  created_at: string;
  updated_at: string;
}

interface QueryOptions {
  app?: string;
  source?: string;
  single?: boolean;
}

// 抽象的错误处理函数
function handleSupabaseError(error: any, operation: string, context: any): never {
  console.error(`Supabase query error during ${operation}:`, {
    message: error.message,
    details: error.details,
    hint: error.hint,
    code: error.code,
    context,
  });
  throw error;
}

// 抽象的日志记录函数
function logOperation(operation: string, params: any, result?: any) {
  console.log(`${operation}:`, params);
  if (result !== undefined) {
    console.log(`${operation} result:`, result);
  }
}

// 通用的查询构建器
async function executeQuery(env: Env, options: QueryOptions): Promise<CreditRule | CreditRule[] | null> {
  const { app, source, single = false } = options;
  
  logOperation('Building query', { app, source, single });

  let query = getClient(env)
    .from(CREDIT_RULES_TABLE)
    .select('*');

  // 应用过滤条件
  if (app) {
    query = query.eq('app', app);
  }
  if (source) {
    query = query.eq('source', source);
  }

  try {
    const { data, error } = single ? await query.maybeSingle() : await query;

    if (error) {
      handleSupabaseError(error, 'executeQuery', { app, source, single });
    }

    logOperation('Query executed successfully', { app, source, single }, 
      single ? (data ? 'Found 1 record' : 'No record found') : `Found ${data?.length || 0} records`);

    return single ? (data as CreditRule | null) : (data as CreditRule[] || []);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    console.error("Error executing query:", {
      error: errorMessage,
      app,
      source,
      single,
    });
    throw error;
  }
}

// 获取所有 credit rules
export async function doGetAllCreditRules(env: Env, source?: string): Promise<CreditRule[]> {
  try {
    logOperation('Getting all credit rules', { source });
    
    const result = await executeQuery(env, { source, single: false });
    return result as CreditRule[];
  } catch (error) {
    console.error("Error retrieving all credit rules:", { source, error });
    throw error;
  }
}

// 生成缓存 key - 统一使用全量数据的缓存 key
function generateCacheKey(): string {
  return 'credit_rules:all_data';
}

// 从 KV 获取全量缓存数据
async function getCachedAllCreditRules(env: Env): Promise<CreditRule[] | null> {
  try {
    const cacheKey = generateCacheKey();
    const cached = await env.MY_KV_NAMESPACE.get(cacheKey);
    if (cached) {
      logOperation('Cache hit for all credit rules', { cacheKey });
      return JSON.parse(cached) as CreditRule[];
    }
    logOperation('Cache miss for all credit rules', { cacheKey });
    return null;
  } catch (error) {
    console.error('Error getting cached all credit rules:', error);
    return null;
  }
}

// 将全量数据存入 KV 缓存
async function setCachedAllCreditRules(env: Env, data: CreditRule[]): Promise<void> {
  try {
    if (data && data.length > 0) {
      const cacheKey = generateCacheKey();
      // 5 分钟 = 300 秒
      const expirationTtl = 300;
      await env.MY_KV_NAMESPACE.put(cacheKey, JSON.stringify(data), { expirationTtl });
      logOperation('Cached all credit rules', { cacheKey, expirationTtl, count: data.length });
    }
  } catch (error) {
    console.error('Error caching all credit rules:', error);
  }
}

// 基于全量数据进行内存筛选
function filterCreditRules(allRules: CreditRule[], app?: string, source?: string): CreditRule | CreditRule[] | null {
  if (!allRules || allRules.length === 0) {
    return null;
  }

  // 如果没有提供 app，但提供了 source，返回所有 app 中该 source 的规则（回退到 ALL）
  if (!app && source) {
    logOperation('Filtering: No app but source provided', { source });
    const results: CreditRule[] = [];
    
    // 获取所有不同的 app
    const uniqueApps = [...new Set(allRules.map(rule => rule.app))];
    
    // 为每个 app 查找对应的规则
    for (const appName of uniqueApps) {
      // 先找具体的 source 规则
      let rule = allRules.find(r => r.app === appName && r.source === source);
      // 如果没有找到且 source 不是 'ALL'，则找 ALL 规则
      if (!rule && source !== 'ALL') {
        rule = allRules.find(r => r.app === appName && r.source === 'ALL');
      }
      if (rule) {
        results.push(rule);
      }
    }
    
    logOperation('Filtered by source', { source, resultCount: results.length });
    return results;
  }

  // 如果没有提供 app，返回所有 credit rules（可能按 source 过滤）
  if (!app) {
    logOperation('Filtering: No app provided, getting all credit rules', { source });
    if (source) {
      const filtered = allRules.filter(rule => rule.source === source);
      logOperation('Filtered all rules by source', { source, resultCount: filtered.length });
      return filtered;
    }
    logOperation('Returning all rules', { totalCount: allRules.length });
    return allRules;
  }

  // 如果只有 app 没有 source，返回该 app 的所有记录
  if (app && source === undefined) {
    logOperation('Filtering: Only app provided', { app });
    const filtered = allRules.filter(rule => rule.app === app);
    logOperation('Filtered by app', { app, resultCount: filtered.length });
    return filtered;
  }

  // 如果 app 和 source 都有，查找特定记录（带回退逻辑）
  if (app && source !== undefined) {
    logOperation('Filtering: Both app and source provided', { app, source });
    
    // 首先尝试查找具体的规则
    let rule = allRules.find(r => r.app === app && r.source === source);
    
    if (rule) {
      logOperation('Found specific rule', { app, source });
      return rule;
    }

    // 如果没找到且 source 不是 'ALL'，尝试查找默认规则
    if (source !== 'ALL') {
      rule = allRules.find(r => r.app === app && r.source === 'ALL');
      
      if (rule) {
        logOperation('Found fallback rule', { app, fallbackTo: 'ALL' });
        return rule;
      }
    }

    logOperation('No rule found', { app, source });
    return null;
  }

  // 这种情况不应该发生，但为了类型安全
  logOperation('Unexpected case in filterCreditRules', { app, source });
  return null;
}

// 主要的获取 credit rule 函数
export async function doGetCreditRule(env: Env, app?: string, source?: string): Promise<CreditRule | CreditRule[] | null> {
  try {
    // 先尝试从缓存获取全量数据
    let allRules = await getCachedAllCreditRules(env);
    
    if (!allRules) {
      // 缓存未命中，从数据库获取全量数据
      logOperation('Cache miss, fetching all rules from database', {});
      allRules = await doGetAllCreditRules(env) as CreditRule[];
      
      // 将全量数据存入缓存
      if (allRules && allRules.length > 0) {
        await setCachedAllCreditRules(env, allRules);
      }
    }

    // 基于全量数据进行筛选
    const result = filterCreditRules(allRules || [], app, source);
    
    return result;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    console.error("Error in doGetCreditRule:", {
      error: errorMessage,
      app,
      source,
    });
    throw error;
  }
} 
