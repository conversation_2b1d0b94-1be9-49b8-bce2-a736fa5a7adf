export interface A1dUser {
        uid?: string;
        source?: string;
        source_id?: string;
        api_key?: string;
}

export interface Credit {
    id?: number;
    account_id: string;
    pending_credits: number;
    plan_credits: number;
    pay_as_go_credits: number;
    created_at: Date;
    updated_at: Date;
}


export interface ApiKey {
    id:string;
    uid: string;
    api_key: string;
    scope: string,
    status: string,
    name?: string,
    account_id: string,
    deleted_at?: Date,
    created_at?: Date;
    updated_at?: Date;
}