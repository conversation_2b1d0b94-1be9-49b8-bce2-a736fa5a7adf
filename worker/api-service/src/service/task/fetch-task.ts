import { TaskInfoResult, TaskStatus } from '../../model/task';
import { TaskService } from './task-service';
import { doGetTask } from '../supabase/task';
import { FetchTaskParams } from './task-service';
import { doConvertImageFormat } from '../../utils/image-utils';
import { UrlProcessor } from '../../utils/url-processor';
import { TaskPoller, TaskDataFetcher } from './task-poller';
import { SSEResponseHandler } from './sse-response-handler';
import { TaskMapper } from './task-mapper';
import { isTerminalState } from '../../utils/task-utils';

/**
 * 处理任务结果的URL转换
 * @param result 任务结果
 * @param taskId 任务ID
 * @param env 环境变量
 * @param app 应用名称
 * @returns 处理后的任务结果
 */
async function processTaskResultUrls(result: TaskInfoResult, taskId: string, env: Env, app?: string): Promise<TaskInfoResult> {

    // 检查是否有任何URL需要处理
    // fetch-task 跳过特殊字段(sketchImageUrl, colorImageUrl, videoUrl)的转换
    const needsUrlProcessing = (
        (result.imageUrl && !UrlProcessor.isA1dUrl(result.imageUrl)) ||
        (result.thumbUrl && !UrlProcessor.isA1dUrl(result.thumbUrl))
    );

    if (needsUrlProcessing) {
        console.log(`[processTaskResultUrls] Processing URLs for task ${taskId}, app: ${app}`);
        try {
            const processedResult = await UrlProcessor.processTaskResultWithUrlConversion(
                result,
                env,
                app,
                { skipSpecialFields: true }
            );
            console.log(`[processTaskResultUrls] All URLs processed for task ${taskId}`);
            return processedResult;
        } catch (error) {
            console.error(`[processTaskResultUrls] Error processing URLs for task ${taskId}:`, error);
            // 继续使用原始URL
            return result;
        }
    } else {
        console.log(`[processTaskResultUrls] All URLs are already A1D domains for task ${taskId}`);
        return result;
    }
}

export class TaskDataFetcherImpl implements TaskDataFetcher {
    constructor(
        private env: Env,
        private targetImageType?: string
    ) { }

    async fetchTaskData(taskId: string): Promise<TaskInfoResult> {
        return doFetchTaskData(taskId, this.env, this.targetImageType);
    }
}

export async function doFetchTaskData(taskId: string, env: Env, targetImageType?: string): Promise<TaskInfoResult> {
    try {
        console.log(`[doFetchTaskData] Fetching data for task ${taskId}`);

        // 首先尝试从 Supabase 获取使用记录
        const taskResult = await doGetTask(env, taskId);

        console.log('taskResult', taskResult);

        // 如果找不到任务，返回等待状态
        if (!taskResult) {
            console.log(`[doFetchTaskData] Task ${taskId} not found in database, returning WAITING`);
            return TaskMapper.createWaitingResult(taskId);
        }

        // 如果任务已完成且有结果
        if (taskResult.status === TaskStatus.FINISHED) {
            console.log(`[doFetchTaskData] Task ${taskId} is FINISHED, returning result from database`);
            let result = TaskMapper.mapTaskToResult(taskResult, taskId);

            // 处理图片格式转换
            if (result.imageUrl && targetImageType) {
                result.imageUrl = await doConvertImageFormat(env, result.imageUrl, targetImageType);
            }

            // 检查是否需要URL转换（如果URL还不是A1D域名）
            result = await processTaskResultUrls(result, taskId, env, taskResult.app);

            return result;
        }

        // 如果任务处于其他终止状态（取消、失败、未知）
        if ([TaskStatus.CANCEL, TaskStatus.FAILED, TaskStatus.UNKNOWN].includes(taskResult.status as TaskStatus)) {
            console.log(`[doFetchTaskData] Task ${taskId} is in terminal state ${taskResult.status}, returning result from database`);
            return TaskMapper.mapTaskToResult(taskResult, taskId);
        }

        // 如果没有使用记录或结果，再从原始 API 获取
        console.log(`[doFetchTaskData] Task ${taskId} status is ${taskResult.status}, fetching from external API`);
        let result = await TaskService.fetchExternalTaskStatus({
            app: taskResult.app,
            taskId: taskId,
            source: taskResult.source
        } as FetchTaskParams, env);

        console.log(`[doFetchTaskData] External API returned for task ${taskId}:`, result);

        // 标准化结果
        const normalizedResult = TaskMapper.normalizeTaskResult(taskId, result);
        console.log(`[doFetchTaskData] Normalized result for task ${taskId}:`, normalizedResult);

        // 处理图片格式转换
        if (normalizedResult.imageUrl && targetImageType) {
            normalizedResult.imageUrl = await doConvertImageFormat(env, normalizedResult.imageUrl, targetImageType);
        }

        // 如果任务已完成，处理URL转换
        if (isTerminalState(normalizedResult.status as TaskStatus)) {
            console.log(`[doFetchTaskData] External API returned terminal task ${taskId}, processing URLs`);
            return await processTaskResultUrls(normalizedResult, taskId, env, taskResult.app);
        }

        return normalizedResult;
    } catch (error) {
        console.error(`[doFetchTaskData] Error fetching data for task ${taskId}:`, error);
        return TaskMapper.createFailedResult(taskId, error instanceof Error ? error : 'Unknown error');
    }
}

/**
 * @deprecated 使用 TaskMapper.normalizeTaskResult 替代
 */
export function parseTaskResult(taskId: string, result: TaskInfoResult | any): TaskInfoResult {
    return TaskMapper.normalizeTaskResult(taskId, result);
}

export async function doFetchTask(
    writer: WritableStreamDefaultWriter,
    enc: TextEncoder,
    taskId: string,
    env: Env,
    targetImageType?: string
) {
    console.log(`[doFetchTask] Starting SSE for task ${taskId}, targetImageType: ${targetImageType}`);

    const sseHandler = new SSEResponseHandler(writer, enc);
    const taskPoller = new TaskPoller();
    const taskFetcher = new TaskDataFetcherImpl(env, targetImageType);

    try {
        console.log(`[doFetchTask] Starting polling for task ${taskId}`);
        await taskPoller.poll(
            taskId,
            taskFetcher,
            // onProgress callback
            async (result: TaskInfoResult) => {
                console.log(`[doFetchTask] Progress callback for task ${taskId}, status: ${result.status}`);
                await sseHandler.sendTaskUpdate(result);
            },
            // onComplete callback
            async (result: TaskInfoResult) => {
                console.log(`[doFetchTask] Complete callback for task ${taskId}, status: ${result.status}`);
                try {
                    // 发送到消息队列
                    console.log(`[doFetchTask] Sending task to queue for task ${taskId}`);
                    await env.TASK_QUEUE.send(result);
                    console.log('Task message sent to queue:', result);
                } catch (error) {
                    console.error('Error processing completed task:', error);
                    await sseHandler.sendError(taskId, error instanceof Error ? error : 'Error processing completed task');
                }
            }
        );
        console.log(`[doFetchTask] Polling completed for task ${taskId}`);
    } catch (error) {
        console.error(`[doFetchTask] Error fetching task ${taskId}:`, error);
        await sseHandler.sendError(taskId, error instanceof Error ? error : 'Unknown error');
    } finally {
        console.log(`[doFetchTask] Closing SSE connection for task ${taskId}`);
        await sseHandler.close();
        console.log(`[doFetchTask] SSE connection closed for task ${taskId}`);
    }
}