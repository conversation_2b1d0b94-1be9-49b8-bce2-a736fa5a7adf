import { TaskInfoResult, TaskStatus } from '../../model/task';
import { TaskMapper } from './task-mapper';

export class SSEResponseHandler {
    private writer: WritableStreamDefaultWriter;
    private encoder: TextEncoder;

    constructor(writer: WritableStreamDefaultWriter, encoder: TextEncoder) {
        this.writer = writer;
        this.encoder = encoder;
    }

    async sendTaskUpdate(taskResult: TaskInfoResult): Promise<void> {
        try {
            const clientResult = TaskMapper.filterClientFields(taskResult);
            console.log(`[SSEHandler] Sending task update for ${taskResult.taskId}:`, clientResult);
            const data = `data: ${JSON.stringify(clientResult)}\n\n`;
            await this.writer.write(this.encoder.encode(data));
            console.log(`[SSEHandler] Task update sent successfully for ${taskResult.taskId}`);
        } catch (error) {
            console.error(`[SSEHandler] Error sending SSE update for ${taskResult.taskId}:`, error);
            throw error;
        }
    }

    async sendError(taskId: string, error: Error | string): Promise<void> {
        console.log(`[SSEHandler] Sending error for task ${taskId}:`, error);
        const errorResult: TaskInfoResult = {
            taskId,
            status: TaskStatus.FAILED,
            error: error instanceof Error ? error.message : error
        };
        await this.sendTaskUpdate(errorResult);
    }

    async close(): Promise<void> {
        try {
            console.log(`[SSEHandler] Closing SSE stream`);
            await this.writer.close();
            console.log(`[SSEHandler] SSE stream closed successfully`);
        } catch (error) {
            console.error(`[SSEHandler] Error closing SSE stream:`, error);
            // Don't throw here as stream might already be closed
        }
    }
} 