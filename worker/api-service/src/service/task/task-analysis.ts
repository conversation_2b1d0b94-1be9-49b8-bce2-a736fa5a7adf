import { getClient } from '../supabase';
import { TaskStatus } from '../../model/task';

interface TaskDailyStats {
    stats_date: string;
    uid: string;
    app: string;
    source: string;
    success_task_count: number;
    failed_task_count: number;
    canceled_task_count: number;
    success_credit_cost: number;
    failed_credit_cost: number;
    canceled_credit_cost: number;
    total_task_count: number;
    total_credit_cost: number;
}

interface Task {
    created_at: string;
    uid: string;
    app: string;
    source: string;
    status: TaskStatus;
    credit: number | string;
}

/**
 * Analyze tasks within a date range and update daily statistics
 * @param env Env object
 * @param startDate Optional start date in YYYY-MM-DD format. If not provided, defaults to yesterday
 * @param endDate Optional end date in YYYY-MM-DD format. If not provided, defaults to startDate
 */
export async function doAnalyzeTasksByDateRange(env: Env, startDate?: string, endDate?: string) {
    // If no startDate provided, default to yesterday
    if (!startDate) {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        startDate = yesterday.toISOString().split('T')[0];
    }
    
    // If no endDate provided, use startDate
    endDate = endDate || startDate;

    // Validate date range
    if (endDate < startDate) {
        throw new Error(`Invalid date range: endDate (${endDate}) cannot be earlier than startDate (${startDate})`);
    }

    try {
        const PAGE_SIZE = 1000;
        let hasMore = true;
        let currentPage = 0;
        let totalProcessedTasks = 0;
        
        // Global stats map to accumulate all statistics
        const statsMap = new Map<string, TaskDailyStats>();

        // Process tasks page by page
        while (hasMore) {
            const from = currentPage * PAGE_SIZE;
            const to = from + PAGE_SIZE - 1;

            const { data: tasks, error: queryError } = await getClient(env)
                .from('task')
                .select('*')
                .gte('created_at', `${startDate}T00:00:00Z`)
                .lt('created_at', `${endDate}T23:59:59.999Z`)
                .range(from, to)
                .order('created_at', { ascending: true })
                .returns<Task[]>();

            if (queryError) {
                throw queryError;
            }

            if (!tasks || tasks.length === 0) {
                hasMore = false;
                break;
            }

            // Process current batch of tasks
            tasks.forEach(task => {
                const taskDate = new Date(task.created_at).toISOString().split('T')[0];
                const key = `${taskDate}|${task.uid}|${task.app}|${task.source}`;
                let stats = statsMap.get(key);

                if (!stats) {
                    stats = {
                        stats_date: taskDate,
                        uid: task.uid,
                        app: task.app,
                        source: task.source,
                        success_task_count: 0,
                        failed_task_count: 0,
                        canceled_task_count: 0,
                        success_credit_cost: 0,
                        failed_credit_cost: 0,
                        canceled_credit_cost: 0,
                        total_task_count: 0,
                        total_credit_cost: 0
                    };
                    statsMap.set(key, stats);
                }

                // Update statistics based on task status
                const credit = Number(task.credit) || 0;
                stats.total_task_count += 1;
                stats.total_credit_cost += credit;

                if ([TaskStatus.SUCCESS, TaskStatus.FINISHED].includes(task.status)) {
                    stats.success_task_count += 1;
                    stats.success_credit_cost += credit;
                } else if (task.status === TaskStatus.FAILED) {
                    stats.failed_task_count += 1;
                    stats.failed_credit_cost += credit;
                } else if (task.status === TaskStatus.CANCEL) {
                    stats.canceled_task_count += 1;
                    stats.canceled_credit_cost += credit;
                }
            });

            totalProcessedTasks += tasks.length;
            console.log(`Processed ${tasks.length} tasks in current batch. Total processed: ${totalProcessedTasks}`);
            
            if (tasks.length < PAGE_SIZE) {
                hasMore = false;
            } else {
                currentPage++;
            }
        }

        if (totalProcessedTasks === 0) {
            console.log(`No tasks found for date range: ${startDate} to ${endDate}`);
            return;
        }

        // Delete existing records for the date range
        const { error: deleteError } = await getClient(env)
            .from('task_daily_stats')
            .delete()
            .gte('stats_date', startDate)
            .lte('stats_date', endDate);

        if (deleteError) {
            throw deleteError;
        }

        // Convert stats map to array
        const statsArray = Array.from(statsMap.values());
        
        // Insert stats in batches
        const BATCH_SIZE = 500;
        for (let i = 0; i < statsArray.length; i += BATCH_SIZE) {
            const batch = statsArray.slice(i, i + BATCH_SIZE);
            const { error: insertError } = await getClient(env)
                .from('task_daily_stats')
                .insert(batch);

            if (insertError) {
                throw insertError;
            }
            console.log(`Inserted batch ${Math.floor(i / BATCH_SIZE) + 1} of stats (${batch.length} records)`);
        }

        console.log(`Successfully analyzed ${totalProcessedTasks} tasks for date range: ${startDate} to ${endDate}`);
        return statsArray;
    } catch (error) {
        console.error('Error analyzing tasks:', error);
        throw error;
    }
}
