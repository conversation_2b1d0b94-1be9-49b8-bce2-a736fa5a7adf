import { TaskInfoResult, TaskStatus } from '../../model/task';
import { isTerminalState } from '../../utils/task-utils';

export interface PollingConfig {
    initialInterval: number;
    maxInterval: number;
    increment: number;
    maxAttempts?: number;
}

export interface TaskDataFetcher {
    fetchTaskData(taskId: string): Promise<TaskInfoResult>;
}

export class TaskPoller {
    private config: PollingConfig;

    constructor(config: Partial<PollingConfig> = {}) {
        this.config = {
            initialInterval: 1000,
            maxInterval: 3000,
            increment: 500,
            maxAttempts: 100,
            ...config
        };
    }

    async poll(
        taskId: string,
        fetcher: TaskDataFetcher,
        onProgress?: (result: TaskInfoResult) => void,
        onComplete?: (result: TaskInfoResult) => void
    ): Promise<TaskInfoResult> {
        let interval = this.config.initialInterval;
        let attempts = 0;

        console.log(`[TaskPoller] Starting polling for task ${taskId}, config:`, this.config);

        while (true) {
            if (this.config.maxAttempts && attempts >= this.config.maxAttempts) {
                const errorMsg = `Task polling timeout after ${attempts} attempts`;
                console.error(`[TaskPoller] ${errorMsg}`);
                throw new Error(errorMsg);
            }

            console.log(`[TaskPoller] Attempt ${attempts + 1} for task ${taskId}`);
            const result = await fetcher.fetchTaskData(taskId);
            attempts++;

            console.log(`[TaskPoller] Task ${taskId} status: ${result.status}, isTerminal: ${isTerminalState(result.status as TaskStatus)}`);

            // 如果任务已完成，结束轮询
            if (isTerminalState(result.status as TaskStatus)) {
                console.log(`[TaskPoller] Task ${taskId} reached terminal state, sending final progress update`);
                
                // 先发送进度更新，让客户端知道任务状态
                try {
                    onProgress?.(result);
                    console.log(`[TaskPoller] Final progress update sent for task ${taskId}`);
                } catch (error) {
                    console.error(`[TaskPoller] Error sending final progress update for task ${taskId}:`, error);
                }
                
                // 然后调用完成回调
                console.log(`[TaskPoller] Task ${taskId} calling onComplete`);
                onComplete?.(result);
                console.log(`[TaskPoller] Polling completed for task ${taskId}`);
                return result;
            }

            // 发送当前状态（非终态任务）
            console.log(`[TaskPoller] Task ${taskId} sending progress update`);
            try {
                onProgress?.(result);
                console.log(`[TaskPoller] Progress update sent for task ${taskId}`);
            } catch (error) {
                console.error(`[TaskPoller] Error sending progress update for task ${taskId}:`, error);
                throw error;
            }

            // 等待一段时间后继续轮询
            console.log(`[TaskPoller] Waiting ${interval}ms before next poll for task ${taskId}`);
            await new Promise((resolve) => setTimeout(resolve, interval));
            interval = Math.min(interval + this.config.increment, this.config.maxInterval);
        }
    }
} 