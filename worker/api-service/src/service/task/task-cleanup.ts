import { TaskService } from './task-service';
import { TaskInfoResult, TaskStatus } from '../../model/task';
import { doCommitCreditDeduction, doCommitCreditRefund } from '../credit/service';
import { doGetA1dUser } from '../supabase/a1d-user';

const DEFAULT_STALE_MINUTES = 10; // 默认任务超时时间，10分钟
const BATCH_PROCESS_LIMIT = 50; // 每批处理的任务数量限制

/**
 * 处理超时任务的清理
 * 1. 循环查找所有超时的任务（状态为 WAITING 或 PROCESSING，且创建时间超过指定时间）
 * 2. 对每个任务：
 *    - 检查外部任务状态
 *    - 如果外部任务已完成，更新状态
 *    - 如果外部任务失败或无法获取状态，标记为失败
 * @param env Env 环境变量
 * @param staleMinutes 可选的超时时间（分钟），默认为 10 分钟
 */
export async function handleStaleTasksCleanup(env: Env, staleMinutes: number = DEFAULT_STALE_MINUTES): Promise<void> {
    try {
        console.log(`Starting stale tasks cleanup with timeout of ${staleMinutes} minutes...`);
        let processedCount = 0;
        let totalProcessed = 0;

        while (true) {
            // 获取一批过期任务
            const staleTasks = await TaskService.findStaleTasks(env, staleMinutes, BATCH_PROCESS_LIMIT);
            processedCount = staleTasks.length;

            if (processedCount === 0) {
                console.log(`No more stale tasks found. Total processed: ${totalProcessed}`);
                break;
            }

            console.log(`Processing batch of ${processedCount} stale tasks`);

            for (const task of staleTasks) {
                try {
                    console.log(`Processing stale task: ${task.task_id}`);

                    const taskResult = await TaskService.fetchExternalTaskStatus({
                        app: task.app as any,
                        taskId: task.task_id,
                        source: task.source as any
                    }, env) as TaskInfoResult;

                    if (!taskResult.status) {
                        console.log(`Task ${task.task_id} status unavailable, marking as unknown`);
                        taskResult.status = TaskStatus.UNKNOWN;
                        taskResult.taskId = task.task_id;
                    }

                    let userInfo;
                    if (task.uid !== '0') {
                        userInfo = await doGetA1dUser(env, undefined, undefined, task.uid, undefined);
                    }

                    if (taskResult) {
                        console.log(`Updating task ${task.task_id} with status: ${taskResult.status}`);

                        if ([TaskStatus.PROCESSING, TaskStatus.WAITING, TaskStatus.FAILED, TaskStatus.UNKNOWN, TaskStatus.ERROR, TaskStatus.INIT].includes(taskResult.status)) {
                            console.log(`Marking task ${task.task_id} as failed due to status: ${taskResult.status}`);
                            await TaskService.markTaskAsFailed(env, task.task_id, task.error);

                            if ((task.credit || 0) >= 1 && userInfo) {
                                console.log(`Refunding credits for failed task ${task.task_id}`);
                                await doCommitCreditRefund(env, {
                                    accountId: userInfo.account_id,
                                    taskId: task.task_id,
                                    uid: task.uid,
                                    amount: task.credit || 0,
                                    reason: `Credit refund for task ${task.task_id} (status: ${taskResult.status})`
                                });
                            }
                        } else if ([TaskStatus.SUCCESS, TaskStatus.FINISHED].includes(taskResult.status)) {
                            console.log(`Marking task ${task.task_id} as finished due to status: ${taskResult.status}`);

                            // TaskService.updateTaskStatus 现在会自动处理URL转换
                            await TaskService.updateTaskStatus(env, task.task_id, TaskStatus.FINISHED, taskResult, task.app);

                            if ((task.credit || 0) >= 1 && userInfo) {
                                console.log(`Deducting credits for successful task ${task.task_id}`);
                                await doCommitCreditDeduction(env, {
                                    accountId: userInfo.account_id,
                                    taskId: task.task_id,
                                    app: task.app as any,
                                    uid: task.uid,
                                });
                            }
                        }
                    } else {
                        console.log(`Marking task ${task.task_id} as failed due to no result`);
                        await TaskService.markTaskAsFailed(env, task.task_id, task.error);

                        if (userInfo) {
                            console.log(`Task ${task.task_id} status unavailable, refunding credits`);
                            await doCommitCreditRefund(env, {
                                accountId: userInfo.account_id,
                                taskId: task.task_id,
                                uid: task.uid,
                                amount: task.credit || 0,
                                reason: `Credit refund for task ${task.task_id} (status unavailable)`
                            });
                        }
                    }
                } catch (error) {
                    console.error(`Error processing stale task ${task.task_id}:`, error);
                }
            }

            totalProcessed += processedCount;
            console.log(`Completed processing batch. Total processed so far: ${totalProcessed}`);
        }

        console.log('Stale tasks cleanup completed successfully');
    } catch (error) {
        console.error('Error in handleStaleTasksCleanup:', error);
        throw error;
    }
}
