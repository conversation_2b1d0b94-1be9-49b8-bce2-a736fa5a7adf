import { APP, SOURCE } from '../../types';
import { TaskInfoRequest, TaskInfoResult, TaskStatus } from '../../model/task';
import { doGetClarityUpscalerTask, doGetImageUpscalerTask } from '../app/image-upscaler';
import { doGetRemoveBgTask } from '../app/remove-bg';
import { doGetSpeedPainterTask } from '../app/speedpainter';
import { doGetVideoUpscalerTask } from '../app/video-upscaler';
import { doUpdateTask, doFindStaleTasks, Task } from '../supabase/task';
import { isTerminalState } from '../../utils/task-utils';
import { getClient } from '../supabase';
import { doCommitCreditRefund } from '../credit/service';
import { Result, successOf, errorOf } from '../credit/result';
import { doGetImageVectorizationTask } from '../app/image-vectorization';
import { doGetChat2DesignTask } from '../app/chat-2-design';
import { doGetImageExtendsTask } from '../app/image-extends';
import { doGetRecraftV3ImageGeneratorTask } from '../app/image-generator';
import { doGetRelighterTask } from '../app/image-relighter';
import { UrlProcessor } from '../../utils/url-processor';


export interface FetchTaskParams {
    app: APP;
    taskId: string;
    source?: SOURCE;
}

export interface CancelTaskParams {
    taskId: string;
    accountId: string;
}

/**
 * 统一的任务服务，处理所有任务相关的操作
 */
export class TaskService {
    /**
     * 从外部worker获取任务状态
     */
    static async fetchExternalTaskStatus(params: FetchTaskParams, env: Env): Promise<TaskInfoResult | {}> {

        try {
            switch (params.app) {
                case APP.IMAGE_UPSCALER:
                    switch (params.source) {
                        case SOURCE.FRAMER:
                            return await doGetClarityUpscalerTask(params.taskId, env);
                        case SOURCE.API:
                        case SOURCE.WEB:
                        case SOURCE.CANVA:
                        case SOURCE.FIGMA:
                        case SOURCE.RAPIDAPI:
                        case SOURCE.MCP:
                            return await doGetImageUpscalerTask(params.taskId, env);
                        default:
                            throw new Error(`Unknown source ${params.source} for app ${params.app}`);
                    }
                case APP.REMOVE_BG:
                    return await doGetRemoveBgTask(params.taskId);
                case APP.SPEEDPAINTER:
                    return await doGetSpeedPainterTask(params.taskId, env);
                case APP.VIDEO_UPSCALER:
                    return await doGetVideoUpscalerTask(params.taskId, env);
                case APP.IMAGE_VECTORIZATION:
                    return await doGetImageVectorizationTask(params.taskId, env);
                case APP.IMAGE_EXTENDS:
                    return await doGetImageExtendsTask(params.taskId, env);
                case APP.IMAGE_GENERATOR:
                    return await doGetRecraftV3ImageGeneratorTask(params.taskId, env);
                case APP.IMAGE_RELIGHTER:
                    return await doGetRelighterTask(params.taskId, env);
                case APP.CHAT_2_DESIGN:
                    return await doGetChat2DesignTask(params.taskId, env);
                default:
                    throw new Error(`Unknown app ${params.app}`);
            }
        } catch (error) {
            console.error(`Error fetching external task status for task ${params.taskId}:`, error);
            if (error instanceof SyntaxError && error.message.includes('JSON')) {
                console.error('JSON parsing error details:', {
                    app: params.app,
                    source: params.source,
                    message: error.message,
                    stack: error.stack
                });
            }
            return {};
        }
    }

    /**
     * 取消任务
     * 如果任务已经是终态，则返回任务信息
     * 如果任务还在进行中，则取消任务并退还积分
     */
    static async cancelTask(env: Env, params: CancelTaskParams): Promise<Result<any>> {
        try {
            // 查询任务状态
            const { data: task, error: queryError } = await getClient(env)
                .from('task')
                .select('*')
                .eq('task_id', params.taskId)
                .single();

            if (queryError || !task) {
                return errorOf('TASK_NOT_FOUND');
            }

            // 检查任务是否已经是终态
            if (isTerminalState(task.status)) {
                return successOf({
                    message: 'Task already finished',
                    data: task
                });
            }

            // 如果任务还在进行中，更新状态为取消
            const { error: updateError } = await getClient(env)
                .from('task')
                .update({
                    status: TaskStatus.CANCEL,
                    updated_at: new Date().toISOString(),
                })
                .eq('task_id', params.taskId);

            if (updateError) {
                return errorOf('UPDATE_TASK_FAILED');
            }

            // 退还积分
            const refundResult = await doCommitCreditRefund(env, {
                accountId: params.accountId,
                amount: Number(task.credit),
                reason: `Task ${params.taskId} cancelled by user`,
                taskId: params.taskId,
                app: task.app,
                uid: task.uid
            });

            if (!refundResult.success) {
                console.error('Failed to refund credits:', refundResult.code);
                // 即使退款失败，我们仍然返回取消成功，但记录错误
                return successOf({
                    message: 'Task cancelled but credit refund failed',
                    data: task
                });
            }

            // 重新获取更新后的任务数据
            const { data: updatedTask } = await getClient(env)
                .from('task')
                .select('*')
                .eq('task_id', params.taskId)
                .single();

            return successOf({
                message: 'Task cancelled successfully',
                data: updatedTask
            });

        } catch (error) {
            console.error('Error cancelling task:', error);
            return errorOf('INTERNAL_ERROR');
        }
    }

    /**
     * 更新任务状态和结果
     * 自动处理URL转换以确保一致性
     */
    static async updateTaskStatus(
        env: Env,
        taskId: string,
        status: TaskStatus,
        taskResult: TaskInfoResult,
        app?: string
    ): Promise<void> {
        // 对于终态任务，确保URL已经被正确转换
        let processedTaskResult = taskResult;
        if (isTerminalState(status)) {
            console.log(`[TaskService] Processing URLs for terminal task ${taskId}, status: ${status}`);
            processedTaskResult = await UrlProcessor.processTaskResultWithUrlConversion(
                taskResult,
                env,
                app
            );
        }

        await doUpdateTask({
            task_id: taskId,
            status,
            result: processedTaskResult
        }, env);
    }

    /**
     * 将任务标记为失败
     */
    static async markTaskAsFailed(
        env: Env,
        taskId: string,
        errorMessage?: string
    ): Promise<void> {
        await doUpdateTask({
            task_id: taskId,
            status: TaskStatus.FAILED,
            error: errorMessage
        }, env);
    }

    /**
     * 检查任务是否需要更新
     * 如果任务不在终态，且已经超过指定时间，则需要更新
     */
    static isTaskNeedsUpdate(
        task: TaskInfoRequest,
        staleMinutes: number
    ): boolean {
        if (isTerminalState(task.status)) {
            return false;
        }

        const staleTime = new Date();
        staleTime.setMinutes(staleTime.getMinutes() - staleMinutes);
        const taskCreatedAt = new Date(task.created_at);

        return taskCreatedAt < staleTime;
    }

    /**
     * 查找所有超时的任务
     * @param env 环境变量
     * @param staleMinutes 超时时间（分钟）
     * @param limit 每次查询的任务数量限制，默认50
     */
    static async findStaleTasks(env: Env, staleMinutes: number, limit?: number): Promise<Task[]> {
        return await doFindStaleTasks(env, staleMinutes, limit);
    }
}
