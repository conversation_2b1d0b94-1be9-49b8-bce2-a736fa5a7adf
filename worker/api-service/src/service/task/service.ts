import { getClient } from '../supabase';
import { doCommitCreditRefund } from '../credit/service';
import { Result, successOf, errorOf } from '../credit/result';
import { TaskStatus } from '../../model/task';

export async function doCancelTask(
    env: Env,
    params: {
        taskId: string;
        accountId: string;
    }
): Promise<Result<any>> {
    try {
        // 查询任务状态
        const { data: task, error: queryError } = await getClient(env)
            .from('task')
            .select('*')
            .eq('task_id', params.taskId)
            .single();

        if (queryError || !task) {
            return errorOf('Task not found');
        }

        // 检查任务是否已经是终态
        const finalStates = [TaskStatus.SUCCESS, TaskStatus.FAILED, TaskStatus.CANCEL, TaskStatus.FINISHED];
        if (finalStates.includes(task.status)) {
            return successOf({
                message: 'Task already finished',
                data: task
            });
        }

        // 如果任务还在进行中，更新状态为取消
        const { error: updateError } = await getClient(env)
            .from('task')
            .update({
                status: TaskStatus.CANCEL,
                updated_at: new Date().toISOString(),
            })
            .eq('task_id', params.taskId);

        if (updateError) {
            return errorOf('Failed to cancel task');
        }

        // 退还积分
        const refundResult = await doCommitCreditRefund(env, {
            accountId: params.accountId,
            amount: Number(task.credit),
            reason: `Task ${params.taskId} cancelled by user`,
            taskId: params.taskId,
            app: task.app,
            uid: task.uid
        });

        if (!refundResult.success) {
            console.error('Failed to refund credits:', refundResult.code);
            // 即使退款失败，我们仍然返回取消成功，但记录错误
            return successOf({
                message: 'Task cancelled but credit refund failed',
                data: task
            });
        }

        // 重新获取更新后的任务数据
        const { data: updatedTask } = await getClient(env)
            .from('task')
            .select('*')
            .eq('task_id', params.taskId)
            .single();

        return successOf({
            message: 'Task cancelled successfully',
            data: updatedTask
        });

    } catch (error) {
        console.error('Error cancelling task:', error);
        return errorOf('Internal server error');
    }
}