import { TaskInfoRequest, TaskStatus, TaskQueueMessage, TaskInfoResult } from '../../model/task';
import { doCreateImageUpscalerTask } from '../app/image-upscaler';
import { doCreateRemoveBgTask } from '../app/remove-bg';
import { doCreateSpeedPainterTask } from '../app/speedpainter';
import { doCreateVideoUpscalerTask } from '../app/video-upscaler';
import { doCreateImageGeneratorTask } from '../app/image-generator';
import { doCreateImageVectorizaitonTask } from '../app/image-vectorization';
import { doGetNeededCredits } from '../../middlewares/credit';
import { doGetTask, doUpdateTask } from '../supabase/task';
import { doCommitCreditDeduction } from '../credit/service';
import { APP } from '../../types';
import { doGetA1dUser } from '../supabase/a1d-user';
import { isUpdatableState, isCreditDeductionState, isTerminalState } from '../../utils/task-utils';
import { doCreateImageExtendsTask } from '../app/image-extends';
import { doCreateImageRelighterTask } from '../app/image-relighter';
import { doCreateChat2DesignTask } from '../app/chat-2-design';

export async function doCreateTask(task: TaskInfoRequest, env: Env) {
    let result;
    const neededCredits = await doGetNeededCredits(env, task.app, task.source, task.params);
    console.log('Needed credits:', neededCredits);
    try {
        console.log('Creating task:', {
            app: task.app,
            source: task.source,
            input_params: task.params
        });

        switch (task.app) {
            case 'iu':
                result = await doCreateImageUpscalerTask(task, env);
                break;
            case 'sp':
                result = await doCreateSpeedPainterTask(task, env);
                break;
            case 'vu':
                result = await doCreateVideoUpscalerTask(task, env);
                break;
            case 'remove-bg':
                result = await doCreateRemoveBgTask(task);
                break;
            case "image-generator":
                result = await doCreateImageGeneratorTask(task, env);
                break;
            case 'image-vectorization':
                result = await doCreateImageVectorizaitonTask(task, env);
                break;
            case 'image-extends':
                result = await doCreateImageExtendsTask(task, env);
                break;
            case 'image-relighter':
                result = await doCreateImageRelighterTask(task, env);
                break;

            case 'chat-2-design':
                result = await doCreateChat2DesignTask(task, env);
                break;
            // 这个只是做了一个记录，做扣费逻辑，实际上没有后端任务，所以不需要实现
            case 'text-behind-image':
                result = {
                    taskId: task.taskId,
                    status: TaskStatus.FINISHED
                } as TaskInfoResult;
                break;
            case '302':
                // 302 API 是实时代理，创建任务只是为了积分处理
                result = {
                    taskId: task.taskId,
                    status: TaskStatus.FINISHED,
                    result: task.params.result // 从参数中获取 API 调用结果
                } as TaskInfoResult;
                break;
        }

        // 无论任务是否成功，都发送消息到队列
        const queueMessage: TaskQueueMessage = {
            taskId: result?.taskId || task.taskId,
            uid: task.uid,
            app: task.app,
            source: task.source || 'API',
            credit: neededCredits,
            accountId: task.accountId,
            inputParams: task.params,
            status: result?.status,
            result: result, // 添加 result 字段
            error: result?.error
        };
        await env.TASK_QUEUE.send(queueMessage);
        console.log('Task message sent to queue:', queueMessage);

        return result;
    } catch (error) {
        console.error('Error in createTask:', error);
        let errorMessage = 'Unknown error';
        if (error instanceof Error) {
            errorMessage = error.message;
            if (error instanceof SyntaxError && error.message.includes('JSON')) {
                errorMessage = `JSON parsing error: ${error.message}`;
                console.error('JSON parsing error details:', {
                    message: error.message,
                    stack: error.stack
                });
            }
        }
        // 发生异常时也发送消息到队列
        const queueMessage: TaskQueueMessage = {
            taskId: result?.taskId || task.taskId,
            uid: task.uid,
            app: task.app,
            source: task.source || 'API',
            credit: neededCredits,
            accountId: task.accountId,
            status: TaskStatus.FAILED,
            inputParams: task.params,
            error: errorMessage
        };
        await env.TASK_QUEUE.send(queueMessage);
        console.log('Error task message sent to queue:', queueMessage);
        throw error;
    }
}

// 更新任务状态并处理积分
export async function doUpdateTaskStatus(taskResult: TaskInfoResult, env: Env): Promise<void> {
    try {
        // 获取现有任务记录
        const existingTask = await doGetTask(env, taskResult.taskId);
        if (!existingTask) {
            console.log('Missing task with ID:', taskResult.taskId);
            return;
        }

        // 检查是否处于终态
        if (isTerminalState(existingTask.status as TaskStatus)) {
            console.log(`Task ${taskResult.taskId} is already in terminal state: ${existingTask.status}`);
            return;
        }

        // 检查当前状态是否为 WAITING 或 PROCESSING
        if (!isUpdatableState(existingTask.status as TaskStatus)) {
            console.log(`Task ${taskResult.taskId} is in unexpected state: ${existingTask.status}`);
            return;
        }

        // 检查新状态是否为终态
        if (isTerminalState(taskResult.status as TaskStatus)) {
            console.log(`Updating task ${taskResult.taskId} to terminal state: ${taskResult.status}`);

            // 更新任务状态
            await doUpdateTask({
                ...existingTask,
                status: taskResult.status,
                result: taskResult,
                error: taskResult.error
            }, env);

            // 只有在成功完成时才扣除积分
            if (existingTask.credit > 0 && isCreditDeductionState(taskResult.status)) {
                console.log(`Processing credit deduction for task ${taskResult.taskId}`);
                const userInfo = await doGetA1dUser(env, undefined, undefined, existingTask.uid, undefined);
                console.log('userInfo', userInfo);
                if (!userInfo) {
                    console.error('User not found');
                    throw new Error('User not found');
                }
                const creditResult = await doCommitCreditDeduction(env, {
                    accountId: userInfo.account_id,
                    app: existingTask.app as APP,
                    taskId: existingTask.task_id,
                    uid: existingTask.uid
                });

                if (!creditResult.success) {
                    console.error('Failed to commit credit deduction:', creditResult.message);
                    throw new Error(`Failed to commit credit deduction: ${creditResult.message}`);
                }
                console.log(`Successfully processed credits for task ${taskResult.taskId}`);
            }
        } else {
            // 如果不是终态，只更新状态
            console.log(`Updating task ${taskResult.taskId} to state: ${taskResult.status}`);
            await doUpdateTask({
                ...existingTask,
                status: taskResult.status,
                result: taskResult,
                error: taskResult.error
            }, env);
        }
    } catch (error) {
        console.error('Error in updateTaskStatus:', error);
        throw error;
    }
}