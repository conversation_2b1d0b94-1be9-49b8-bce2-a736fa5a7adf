# Task Service Architecture

## 概览

任务服务经过重构，提取了几个核心抽象类来提高代码的可维护性和复用性：

## 核心抽象类

### 1. TaskPoller - 任务轮询器

负责处理任务的轮询逻辑，支持自定义配置：

```typescript
const poller = new TaskPoller({
    initialInterval: 1000,  // 初始轮询间隔
    maxInterval: 3000,      // 最大轮询间隔
    increment: 500,         // 每次增加的间隔
    maxAttempts: 100        // 最大尝试次数
});
```

### 2. SSEResponseHandler - SSE响应处理器

统一处理服务器推送事件(SSE)的响应：

```typescript
const sseHandler = new SSEResponseHandler(writer, encoder);
await sseHandler.sendTaskUpdate(taskResult);
await sseHandler.sendError(taskId, error);
await sseHandler.close();
```

**注意**: SSE响应处理器会自动过滤掉内部字段，确保客户端只接收到公开数据。

### 3. TaskMapper - 任务映射器

处理不同数据格式之间的转换：

```typescript
// 数据库Task -> TaskInfoResult
const result = TaskMapper.mapTaskToResult(dbTask, taskId);

// 标准化任务结果
const normalized = TaskMapper.normalizeTaskResult(taskId, rawResult);

// 过滤内部字段，只保留客户端需要的字段
const clientResult = TaskMapper.filterClientFields(rawResult);

// 创建特定状态的结果
const waiting = TaskMapper.createWaitingResult(taskId);
const failed = TaskMapper.createFailedResult(taskId, error);
```

#### 内部字段过滤

TaskMapper 会自动过滤掉以下内部字段，确保客户端不会接收到仅供内部使用的数据：

- `originalImageUrl` - 原始图片链接
- `originalVideoUrl` - 原始视频链接  
- `originalThumbUrl` - 原始缩略图链接
- `rawResult` - 原始结果数据
- `internalData` - 内部数据

这些字段主要用于：
- 内部记录和追踪
- 队列处理
- 数据分析
- 系统监控

## 使用示例

### 基本任务获取

```typescript
// 获取单次任务数据
const result = await doFetchTaskData(taskId, env, 'webp');
```

### SSE轮询

```typescript
// 创建SSE流进行任务轮询
await doFetchTask(writer, encoder, taskId, env, 'webp');
```

#### SSE 连接行为

SSE (Server-Sent Events) 接口的工作流程：

1. **连接建立**: 客户端发起 SSE 连接，服务器立即返回流并保持连接开放
2. **后台轮询**: 服务器在后台开始轮询任务状态
3. **实时推送**: 每次轮询都会通过 SSE 推送当前状态给客户端
4. **自动断开**: 当任务达到终态（FINISHED/FAILED/CANCEL）时，服务器自动关闭连接

```
客户端                    服务器
   |                        |
   |--- GET /sse/task123 -->|
   |<-- 200 + SSE stream ---|
   |                        |--- 开始后台轮询
   |<-- data: WAITING ------|
   |<-- data: PROCESSING ---|
   |<-- data: PROCESSING ---|
   |<-- data: FINISHED -----|--- 任务完成
   |<-- 连接关闭 ----------|--- 自动断开
```

#### 轮询配置

可以通过 `TaskPoller` 配置轮询行为：

```typescript
const poller = new TaskPoller({
    initialInterval: 1000,  // 初始间隔 1 秒
    maxInterval: 3000,      // 最大间隔 3 秒  
    increment: 500,         // 每次增加 0.5 秒
    maxAttempts: 100        // 最大尝试 100 次
});
```

#### 客户端处理

```javascript
const eventSource = new EventSource('/fetch-task-sse/task123');

eventSource.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('任务状态:', data.status);
    
    // 检查是否为终态
    if (['FINISHED', 'FAILED', 'CANCEL'].includes(data.status)) {
        console.log('任务完成，连接即将关闭');
        eventSource.close();
    }
};
```

### 自定义轮询

```typescript
class CustomTaskFetcher implements TaskDataFetcher {
    async fetchTaskData(taskId: string): Promise<TaskInfoResult> {
        // 自定义获取逻辑
        return customFetchLogic(taskId);
    }
}

const poller = new TaskPoller({ maxInterval: 5000 });
const fetcher = new CustomTaskFetcher();

await poller.poll(taskId, fetcher, onProgress, onComplete);
```

## 改进点

1. **关注点分离**: 轮询、SSE处理、数据映射各自独立
2. **错误处理统一**: 通过抽象类统一错误处理逻辑
3. **类型安全**: 移除了 `Promise<TaskInfoResult | {}>` 这样的不安全类型
4. **可测试性**: 每个抽象类都可以独立测试
5. **可复用性**: 其他需要轮询或SSE的功能可以复用这些类

## 迁移指南

原有的 `parseTaskResult` 函数已标记为 deprecated，建议使用 `TaskMapper.normalizeTaskResult`：

```typescript
// 旧方式 (deprecated)
const result = parseTaskResult(taskId, rawResult);

// 新方式
const result = TaskMapper.normalizeTaskResult(taskId, rawResult);
```

#### 故障排除

**问题**: SSE连接立即关闭，客户端无法接收到任务数据

**原因**: 对于已完成（终态）的任务，TaskPoller直接调用`onComplete`回调而没有先调用`onProgress`，导致客户端没有收到任务状态就直接收到连接关闭信号。

**解决方案**: 修改TaskPoller逻辑，对于终态任务：
1. 先调用`onProgress`发送任务状态给客户端
2. 再调用`onComplete`发送最终结果并关闭连接

```typescript
// 修复后的逻辑
if (isTerminalState(result.status as TaskStatus)) {
    // 先发送进度更新，让客户端知道任务状态
    onProgress?.(result);
    // 然后调用完成回调
    onComplete?.(result);
    return result;
}
```

**权限验证**: 确保SSE和普通任务获取接口都应用了`authMiddleware`认证中间件，保持接口安全性一致。

## 使用示例 