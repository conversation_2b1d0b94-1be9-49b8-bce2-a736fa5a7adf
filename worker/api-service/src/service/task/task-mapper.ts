import { TaskInfoResult, TaskStatus } from '../../model/task';
import { Task } from '../supabase/task';

export class TaskMapper {
    /**
     * 内部字段列表 - 这些字段不应该返回给客户端
     */
    private static readonly INTERNAL_FIELDS = [
        'originalImageUrl',
        'originalVideoUrl',
        'originalThumbUrl',
        'rawResult',
        'internalData'
    ];

    /**
     * 过滤掉内部字段，只保留客户端需要的字段
     */
    static filterClientFields(result: any): TaskInfoResult {
        if (!result || typeof result !== 'object') {
            return result;
        }

        const filtered = { ...result };

        // 移除内部字段
        this.INTERNAL_FIELDS.forEach(field => {
            delete filtered[field];
        });

        return filtered;
    }

    /**
     * 将数据库Task对象转换为TaskInfoResult
     */
    static mapTaskToResult(task: Task, taskId: string): TaskInfoResult {
        // 如果任务状态为FINISHED且有结果，直接返回结果
        if (task.status === TaskStatus.FINISHED && task.result) {
            const result = {
                taskId,
                ...task.result,
                status: TaskStatus.FINISHED
            };

            // 过滤掉内部字段
            return this.filterClientFields(result);
        }

        // 构建基础结果对象
        const result: TaskInfoResult = {
            taskId,
            status: task.status as TaskStatus || TaskStatus.UNKNOWN
        };

        // 添加错误信息
        if (task.error) {
            result.error = task.error;
        }

        // 如果有结果数据，合并到返回对象中
        if (task.result && typeof task.result === 'object') {
            Object.assign(result, task.result);
        }

        // 过滤掉内部字段
        return this.filterClientFields(result);
    }

    /**
     * 标准化任务结果，确保所有必要字段存在
     */
    static normalizeTaskResult(taskId: string, result: TaskInfoResult | any): TaskInfoResult {
        if (!result || result.status === TaskStatus.UNKNOWN) {
            return {
                taskId,
                status: TaskStatus.UNKNOWN,
                error: 'Task not found'
            };
        }

        // 状态转换：SUCCESS -> FINISHED
        if (result.status === TaskStatus.SUCCESS) {
            result.status = TaskStatus.FINISHED;
        }

        const normalized = {
            taskId,
            status: result.status || TaskStatus.UNKNOWN,
            error: result.error,
            mimeType: result.mimeType,
            imageUrl: result.imageUrl,
            thumbUrl: result.thumbUrl,
            duration: result.duration,
            videoUrl: result.videoUrl,
            sketchImageUrl: result.sketchImageUrl,
            colorImageUrl: result.colorImageUrl,
            imageUrls: result.imageUrls,
            message: result.message,
            nsfw: result.nsfw
        };

        // 过滤掉内部字段
        return this.filterClientFields(normalized);
    }

    /**
     * 创建默认的等待状态任务结果
     */
    static createWaitingResult(taskId: string): TaskInfoResult {
        return {
            taskId,
            status: TaskStatus.WAITING
        };
    }

    /**
     * 创建失败状态的任务结果
     */
    static createFailedResult(taskId: string, error: string | Error): TaskInfoResult {
        return {
            taskId,
            status: TaskStatus.FAILED,
            error: error instanceof Error ? error.message : error
        };
    }
} 