import { Context } from 'hono';
import { Bindings, CacheHandler } from '../../types';
import { JwksClient } from './JwksClient';
import { decode, verify } from 'hono/jwt';

const CACHE_EXPIRY_MS = 60 * 60 * 1_000; // 60 minutes

async function getActivePublicKey({ appId, token, cacheHandler }: { appId: string; token: string; cacheHandler: CacheHandler }) {
	const decoded = decode(token);

	if (!decoded) {
		throw new Error('Invalid token');
	}

	const { kid } = decoded.header as any;

	const jwks = new JwksClient({
		jwksUri: `https://api.canva.com/rest/v1/apps/${appId}/jwks`,
		cacheHandler,
	});

	const key = await jwks.getSigningKey(kid);
	return key.getPublicKey();
}

export async function verifyToken(token: string, appId: string, c: Context<{ Bindings: Bindings }>) {
	function cacheFactory(c: Context<{ Bindings: Bindings }>) {
		const prefix = 'PUB_KEY_' + appId + '_';
		return {
			get(key: string) {
				return c.env.MY_KV_NAMESPACE.get(prefix + key);
			},
			set(key: string, value: string, ttl = CACHE_EXPIRY_MS) {
				return c.env.MY_KV_NAMESPACE.put(prefix + key, value, { expirationTtl: ttl });
			},
			del(key: string) {
				return c.env.MY_KV_NAMESPACE.delete(prefix + key);
			},
		};
	}
	const publicKey = await getActivePublicKey({
		appId,
		token,
		cacheHandler: cacheFactory(c),
	});
	const verified = verify(token, publicKey, 'RS256');

	return verified;
}
