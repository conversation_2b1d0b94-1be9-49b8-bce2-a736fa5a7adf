import { DurableObject } from 'cloudflare:workers'
import { TaskInfoResult } from './types'

export class TaskInfoDurableObject extends DurableObject {
  async getInfo() {
    let value = await this.ctx.storage.get<TaskInfoResult>('value')
    return value
  }

  async setInfo(obj: TaskInfoResult) {
    const value = await this.ctx.storage.get<TaskInfoResult>('value')
    const newValue = Object.assign(value || {}, obj) as TaskInfoResult
    await this.ctx.storage.put('value', newValue)
    return newValue
  }
}