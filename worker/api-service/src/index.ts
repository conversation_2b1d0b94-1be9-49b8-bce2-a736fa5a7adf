/**
 * Run `npm run dev` in your terminal to start a development server
 * Open a browser tab at http://localhost:8787/ to see your worker in action
 * Run `npm run deploy` to publish your worker
 *
 * Bind resources to your worker in `wrangler.toml`.
 *
 * Learn more at https://developers.cloudflare.com/workers/
 */

import { fromHono } from 'chanfana';
import { Context, Hono, Next } from 'hono';
import { cors } from 'hono/cors';
// import { upgradeWebSocket } from 'hono/cloudflare-workers';
import { ExchangeToken } from './endpoints/exchange-token';
import { GenApiKey, GetApiKey, UpdateApiKey } from './endpoints/api-key';
import { ImageUpscaler } from './endpoints/image-upscaler';
// import { TaskInfoDurableObject } from './durableObjects';
import { FetchTask, FetchTaskSSE } from './endpoints/fetch-task-sse';
import { authMiddleware } from './middlewares/auth';
import { creditEnoughMiddleware } from './middlewares/credit';
import { FileUpload } from './endpoints/file-upload';
import { RemoveBg } from './endpoints/remove-bg';
import {
	GetCredit,
	GetAppCredit,
} from './endpoints/credit';
import { APP, Bindings } from './types';
import { SpeedPainter } from './endpoints/speedpainter';
import { VideoUpscaler } from './endpoints/video-upscaler';
import { TextBehindImage } from './endpoints/text-behind-image';
import { StripeAddPayAsGoCredit, StripeResetPlanCredit } from './endpoints/stripe-credit';
import { FileUploadBase64 } from './endpoints/file-upload-base64';
import { handleTaskQueue } from './queue';
import { handleScheduled } from './scheduled';
import { GetUserInfo } from './endpoints/get-user-info';
import { CancelTask } from './endpoints/cancel-task';
import { ImageVectorization } from './endpoints/image-vectorization';
import { ImageExtends } from './endpoints/image-extends';
// import { TaskDataAnalysis } from './endpoints/task-data-analysis';
import { ImageProxy } from './endpoints/image-proxy';
import { GeminiImageGeneration } from './endpoints/gemini-image-generation';
import { ImageGenerator } from './endpoints/image-generator';
import { Chat2Design } from './endpoints/chat-2-design';
import { GetAppTemplate } from './endpoints/get-app-template';
import { DeleteTask } from './endpoints/delete-task';
import { GetUserTasks } from './endpoints/get-user-tasks';
import { Proxy302 } from './endpoints/proxy-302';
import { CalculateCredits } from './endpoints/calculate-credits';
import { ImageRelighter } from './endpoints/image-relighter';
import { PosthogProxy } from './endpoints/posthog-proxy';


// Star a Hono app
const app = new Hono();

app.use(
	'/api/*',
	cors({
		credentials: true,
		origin: '*',
	}),
);

// Register WebSocket events
//app.get('/api/task/:id/ws', durableObjectMiddleware, upgradeWebSocket(createEvents));

// Setup OpenAPI registry
const options = {
	docs_url: '/docs',
	schema: {
		info: {
			title: 'A1d Auth Worker API',
			version: '1.0',
		},
		servers: [
			{
				url: '/',
				description: 'Development server',
			},
			{
				url: 'https://auth-cf-testk-xshar.workers.dev/',
				description: 'Production server',
			},
		],
		security: [
			{
				BearerAuth: [],
			},
			{
				KeyAuth: [],
			},
		],
	},
} as const;
const openapi = fromHono<{ Bindings: Bindings }>(app, options);

openapi.registry.registerComponent('securitySchemes', 'BearerAuth', {
	type: 'http',
	scheme: 'bearer',
	bearerFormat: 'JWT',
	description: 'JWT token with Bearer prefix',
});

openapi.registry.registerComponent('securitySchemes', 'KeyAuth', {
	type: 'apiKey',
	in: 'header',
	name: 'Authorization',
	description: 'API Key with KEY prefix (e.g., KEY your-api-key)',
});

// 修改中间件工厂函数
const createAppNameMiddleware = (app: string) => {
	return async (c: Context<{ Bindings: Bindings }>, next: Next) => {
		// 获取现有的 payload
		const existingPayload = c.get('jwtPayload') || {};

		// 从请求体中获取 source，智能检测格式
		let body = {};
		const contentType = c.req.header('content-type') || '';
		console.log(`[createAppNameMiddleware] Content-Type: "${contentType}"`);
		console.log(`[createAppNameMiddleware] Method: ${c.req.method}`);
		console.log(`[createAppNameMiddleware] Request URL: ${c.req.url}`);
		console.log(`[createAppNameMiddleware] Request timestamp: ${new Date().toISOString()}`);

		try {
			// 优先尝试 form-data 解析，因为很多客户端使用 --form 但可能没有正确设置 Content-Type
			if (contentType.includes('multipart/form-data') || contentType.includes('application/x-www-form-urlencoded')) {
				// 明确的 form 格式
				const formData = await c.req.parseBody();
				body = formData; // 保留原始的 FormData，包括文件
				console.log(`[createAppNameMiddleware] Successfully parsed form data with keys:`, Object.keys(formData));
				// 为日志显示创建一个安全的版本
				const logSafeBody = Object.fromEntries(
					Object.entries(formData).map(([key, value]) => [
						key,
						value instanceof File ? `[File: ${value.name}, size: ${value.size}]` : value
					])
				);
				console.log(`[createAppNameMiddleware] Form data content:`, logSafeBody);
			} else if (contentType.includes('application/json')) {
				// JSON 格式
				body = await c.req.json();
				console.log(`[createAppNameMiddleware] Successfully parsed JSON body:`, body);
			} else {
				// 未知或缺失 Content-Type，智能检测
				console.log(`[createAppNameMiddleware] Unknown/missing Content-Type, attempting smart detection...`);

				// 先尝试 form-data（因为 curl --form 经常不设置正确的 Content-Type）
				try {
					const formData = await c.req.parseBody();
					if (formData && Object.keys(formData).length > 0) {
						body = formData; // 保留原始数据
						console.log(`[createAppNameMiddleware] Smart detection: parsed as form data with keys:`, Object.keys(formData));
						const logSafeBody = Object.fromEntries(
							Object.entries(formData).map(([key, value]) => [
								key,
								value instanceof File ? `[File: ${value.name}, size: ${value.size}]` : value
							])
						);
						console.log(`[createAppNameMiddleware] Smart detection form content:`, logSafeBody);
					} else {
						throw new Error('Empty form data, try JSON');
					}
				} catch (formError) {
					console.log(`[createAppNameMiddleware] Form parsing failed, trying JSON...`);
					// 如果 form-data 解析失败，尝试 JSON
					body = await c.req.json();
					console.log(`[createAppNameMiddleware] Smart detection: parsed as JSON:`, body);
				}
			}
		} catch (error) {
			console.error(`[createAppNameMiddleware] All parsing attempts failed:`, error instanceof Error ? error.message : 'Unknown error');
			// 获取原始请求体用于调试
			try {
				const rawBody = await c.req.text();
				console.error(`[createAppNameMiddleware] Raw request body: "${rawBody}"`);
				console.error(`[createAppNameMiddleware] Raw body length: ${rawBody.length}`);
				console.error(`[createAppNameMiddleware] Raw body first 10 chars:`, rawBody.substring(0, 10));
				if (rawBody.length > 0 && rawBody.length < 1000) {
					console.error(`[createAppNameMiddleware] Raw body bytes:`, Array.from(rawBody).map(c => c.charCodeAt(0)));
				}
				// 特别检查是否包含减号
				if (rawBody.includes('-')) {
					console.error(`[createAppNameMiddleware] Body contains minus sign at positions:`,
						[...rawBody].map((char, index) => char === '-' ? index : null).filter(pos => pos !== null));
				}
			} catch (textError) {
				console.error(`[createAppNameMiddleware] Could not read raw body:`, textError);
			}
			body = {};
		}
		// 合并新的 app 信息和 source 到现有的 payload
		c.set('jwtPayload', {
			...existingPayload,
			app,
		});

		// 使用类型断言来避免类型错误
		(c as any).set('requestBody', body);

		await next();
	};
};

openapi.post('/api/exchange-token', ExchangeToken);
openapi.post('/api/gen-api-key', authMiddleware, GenApiKey);
openapi.get('/api/get-api-key', authMiddleware, GetApiKey);
openapi.put('/api/update-api-key', authMiddleware, UpdateApiKey);

// Stripe webhook endpoints (no auth middleware)
openapi.post('/api/stripe/add-pay-as-you-go', StripeAddPayAsGoCredit);
openapi.post('/api/stripe/reset-plan-credits', StripeResetPlanCredit);

// get user info
openapi.get('/api/get-user-info', authMiddleware, GetUserInfo);

// iu

openapi.post('/api/image-upscaler', authMiddleware, createAppNameMiddleware(APP.IMAGE_UPSCALER), creditEnoughMiddleware, ImageUpscaler);

// task
openapi.get('/api/task/:taskId/sse', FetchTaskSSE);
openapi.get('/api/task/:taskId', FetchTask);
openapi.post('/api/uploads/:file_name', authMiddleware, FileUpload);
openapi.post('/api/file-upload-base64', authMiddleware, FileUploadBase64);
// export { TaskInfoDurableObject };

// remove-bg

openapi.post('/api/remove-bg', authMiddleware, createAppNameMiddleware(APP.REMOVE_BG), creditEnoughMiddleware, RemoveBg);

// speed painter
openapi.post('/api/speedpainter', authMiddleware, createAppNameMiddleware(APP.SPEEDPAINTER), creditEnoughMiddleware, SpeedPainter);

// video upscaler
openapi.post('/api/video-upscaler', authMiddleware, createAppNameMiddleware(APP.VIDEO_UPSCALER), creditEnoughMiddleware, VideoUpscaler);

// image vectorization
openapi.post('/api/image-vectorization', authMiddleware, createAppNameMiddleware(APP.IMAGE_VECTORIZATION), creditEnoughMiddleware, ImageVectorization);

// image extends 
openapi.post('/api/image-extends', authMiddleware, createAppNameMiddleware(APP.IMAGE_EXTENDS), creditEnoughMiddleware, ImageExtends);

// image relighter
openapi.post('/api/image-relighter', authMiddleware, createAppNameMiddleware(APP.IMAGE_RELIGHTER), creditEnoughMiddleware, ImageRelighter);

// credit
openapi.get('/api/get-credit', authMiddleware, GetCredit);

// Add this line with other route registrations
openapi.post('/api/task/:taskId/cancel', authMiddleware, CancelTask);

// text behind image
openapi.post('/api/text-behind-image', authMiddleware, createAppNameMiddleware(APP.TEXT_BEHIND_IMAGE), creditEnoughMiddleware, TextBehindImage);

// openapi.post('/api/data/task-data-analysis', authMiddleware, TaskDataAnalysis);
// image generator
openapi.post('/api/image-generator', authMiddleware, createAppNameMiddleware(APP.IMAGE_GENERATOR), creditEnoughMiddleware, ImageGenerator);
// image proxy - publicly accessible without authentication
openapi.get('/api/image-proxy', ImageProxy);

// Gemini image generation
openapi.post('/api/gemini-image-generation', authMiddleware, GeminiImageGeneration);

// Chat 2 Design
openapi.post('/api/chat-2-design', authMiddleware, createAppNameMiddleware(APP.CHAT_2_DESIGN), creditEnoughMiddleware, Chat2Design);


// App templates endpoint - get templates based on app parameter
openapi.get('/api/templates', GetAppTemplate);

// Add new task endpoints
openapi.delete('/api/task/:taskId', authMiddleware, DeleteTask);
openapi.get('/api/tasks', authMiddleware, GetUserTasks);
openapi.get('/api/app-credit', GetAppCredit);

// Calculate credits endpoint - no auth required for calculation
openapi.post('/api/calculate-credits', CalculateCredits);

// 302 API proxy
openapi.post('/api/302', authMiddleware, createAppNameMiddleware(APP.AI_302), creditEnoughMiddleware, Proxy302);
openapi.get('/api/302', authMiddleware, Proxy302);

// PostHog Analytics proxy - no auth required
openapi.get('/ph/*', PosthogProxy);
openapi.post('/ph/*', PosthogProxy);
openapi.options('/ph/*', PosthogProxy);

// Export the Hono app
export default {
	fetch: app.fetch,
	queue: handleTaskQueue,
	scheduled: handleScheduled
};
