import { OpenAPIRoute, contentJson } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings, APP } from '../types';
import { nanoid } from 'nanoid';
import { TaskInfoRequest, TaskStatus } from '../model/task';
import { doCreateTask } from '../service/task/create-task';


export class Chat2Design extends OpenAPIRoute {
    schema = {
        summary: 'Chat 2 Design',
        description: '使用 302.ai API 进行图像分析和对话',
        tags: ['api'],
        request: {
            body: contentJson(
                z.object({
                    imageUrls: z.array(z.string()).optional().describe('图片 URL 数组（可选）'),
                    prompt: z.string().describe('分析提示')
                })
            )
        },
        response: {
            200: contentJson({
                success: z.boolean().describe('是否成功生成图片'),
                taskId: z.string().describe('任务ID'),
                imageUrls: z.array(z.string()).describe('生成的图片URL数组'),
                message: z.string().describe('提示信息')
            }),
            400: contentJson({
                error: z.string().describe('Request error message')
            }),
            401: contentJson({
                error: z.string().describe('Authentication error message')
            }),
            500: contentJson({
                error: z.string().describe('Internal server error message')
            })
        }
    };

    async handle(c: Context<{ Bindings: Bindings }>) {
        console.log('=== Chat 2 Design API 请求开始 ===');
        const requestId = crypto.randomUUID();
        const payload = c.get('jwtPayload');
        console.log(`[${requestId}] 请求开始处理`);

        try {
            const body = await c.req.json();
            let { imageUrls = [], prompt, source } = body;

            // 优先使用 JWT 中的 source，如果没有则使用请求体中的 source
            // 如果都没有，默认为 'canva'
            const finalSource = payload.source || source || 'canva';

            console.log(`[${requestId}] 请求参数:`, {
                imageUrls: imageUrls.length > 0 ? `${imageUrls.length} 张图片` : '无图片',
                prompt,
                jwtSource: payload.source || 'none',
                requestSource: source || 'none',
                finalSource
            });

            // 创建任务记录用于积分扣除和跟踪
            const task: TaskInfoRequest = {
                taskId: nanoid(),
                source: finalSource,  // 使用最终确定的 source
                uid: payload.uid as string,
                app: APP.CHAT_2_DESIGN,
                status: TaskStatus.WAITING,
                params: {
                    imageUrls,
                    prompt,
                    jwtSource: payload.source // 传递JWT中的source信息，用于选择system prompt
                },
                accountId: payload.accountId as string
            };

            try {
                // 创建任务并执行处理逻辑
                const taskResult = await doCreateTask(task, c.env);

                // 返回处理结果（保持原有的响应格式，并添加 taskId）
                return c.json({
                    success: true,
                    taskId: taskResult?.taskId || task.taskId,
                    imageUrls: taskResult?.imageUrls || [],
                    message: taskResult?.message || ''
                });
            } catch (error: any) {
                console.error(`[${requestId}] Create chat-2-design task failed:`, error);
                console.error('Error details:', {
                    message: error.message,
                    stack: error.stack,
                    name: error.name
                });

                if (error instanceof z.ZodError) {
                    return c.json({ error: error.errors[0].message }, 400);
                }
                return c.json({ error: 'Create chat-2-design task failed' }, 500);
            }
        } catch (error: any) {
            console.error(`[${requestId}] Chat 2 Design request failed:`, error);
            return c.json({ error: 'Invalid request' }, 400);
        } finally {
            console.log(`[${requestId}] === Chat 2 Design API 请求结束 ===`);
        }
    }
}