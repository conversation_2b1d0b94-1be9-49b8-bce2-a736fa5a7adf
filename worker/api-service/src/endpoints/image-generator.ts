import { <PERSON><PERSON><PERSON>oute, contentJson } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings, SOURCE, APP } from '../types';
import { doCreateTask } from '../service/task/create-task';
import { TaskInfoRequest, TaskStatus } from '../model/task';
import { nanoid } from 'nanoid';

// 定义与 fal.ai 官方 API 完全匹配的 Zod schemas
const ImageSizeSchema = z.union([
    z.enum(['square_hd', 'square', 'portrait_4_3', 'portrait_16_9', 'landscape_4_3', 'landscape_16_9']),
    z.object({
        width: z.number().min(256).max(2048),
        height: z.number().min(256).max(2048)
    })
]);

const StyleEnumSchema = z.enum([
    'any',
    'realistic_image',
    'digital_illustration',
    'vector_illustration',
    // realistic_image 子样式
    'realistic_image/b_and_w',
    'realistic_image/hard_flash',
    'realistic_image/hdr',
    'realistic_image/natural_light',
    'realistic_image/studio_portrait',
    'realistic_image/enterprise',
    'realistic_image/motion_blur',
    'realistic_image/evening_light',
    'realistic_image/faded_nostalgia',
    'realistic_image/forest_life',
    'realistic_image/mystic_naturalism',
    'realistic_image/natural_tones',
    'realistic_image/organic_calm',
    'realistic_image/real_life_glow',
    'realistic_image/retro_realism',
    'realistic_image/retro_snapshot',
    'realistic_image/urban_drama',
    'realistic_image/village_realism',
    'realistic_image/warm_folk',
    // digital_illustration 子样式
    'digital_illustration/pixel_art',
    'digital_illustration/hand_drawn',
    'digital_illustration/grain',
    'digital_illustration/infantile_sketch',
    'digital_illustration/2d_art_poster',
    'digital_illustration/handmade_3d',
    'digital_illustration/hand_drawn_outline',
    'digital_illustration/engraving_color',
    'digital_illustration/2d_art_poster_2',
    'digital_illustration/antiquarian',
    'digital_illustration/bold_fantasy',
    'digital_illustration/child_book',
    'digital_illustration/child_books',
    'digital_illustration/cover',
    'digital_illustration/crosshatch',
    'digital_illustration/digital_engraving',
    'digital_illustration/expressionism',
    'digital_illustration/freehand_details',
    'digital_illustration/grain_20',
    'digital_illustration/graphic_intensity',
    'digital_illustration/hard_comics',
    'digital_illustration/long_shadow',
    'digital_illustration/modern_folk',
    'digital_illustration/multicolor',
    'digital_illustration/neon_calm',
    'digital_illustration/noir',
    'digital_illustration/nostalgic_pastel',
    'digital_illustration/outline_details',
    'digital_illustration/pastel_gradient',
    'digital_illustration/pastel_sketch',
    'digital_illustration/pop_art',
    'digital_illustration/pop_renaissance',
    'digital_illustration/street_art',
    'digital_illustration/tablet_sketch',
    'digital_illustration/urban_glow',
    'digital_illustration/urban_sketching',
    'digital_illustration/vanilla_dreams',
    'digital_illustration/young_adult_book',
    'digital_illustration/young_adult_book_2',
    // vector_illustration 子样式
    'vector_illustration/bold_stroke',
    'vector_illustration/chemistry',
    'vector_illustration/colored_stencil',
    'vector_illustration/contour_pop_art',
    'vector_illustration/cosmics',
    'vector_illustration/cutout',
    'vector_illustration/depressive',
    'vector_illustration/editorial',
    'vector_illustration/emotional_flat',
    'vector_illustration/infographical',
    'vector_illustration/marker_outline',
    'vector_illustration/mosaic',
    'vector_illustration/naivector',
    'vector_illustration/roundish_flat',
    'vector_illustration/segmented_colors',
    'vector_illustration/sharp_contrast',
    'vector_illustration/thin',
    'vector_illustration/vector_photo',
    'vector_illustration/vivid_shapes',
    'vector_illustration/engraving',
    'vector_illustration/line_art',
    'vector_illustration/line_circuit',
    'vector_illustration/linocut'
]).or(z.string());

const RGBColorSchema = z.object({
    r: z.number().min(0).max(255).int(),
    g: z.number().min(0).max(255).int(),
    b: z.number().min(0).max(255).int()
});

export class ImageGenerator extends OpenAPIRoute {
    schema = {
        summary: 'Image Generator - Text-to-Image',
        description: 'High-quality text-to-image generation service with multiple styles and customization options.',
        tags: ['api'],
        request: {
            body: contentJson(
                z.object({
                    // fal.ai 官方 API 参数 - 必需
                    prompt: z.string().min(1).describe('Text prompt for image generation'),

                    // fal.ai 官方 API 参数 - 可选
                    image_size: ImageSizeSchema.optional().default('square_hd').describe('Image size: predefined enum or custom {width, height} object. Default: square_hd'),
                    style: StyleEnumSchema.optional().default('realistic_image').describe('Style of the generated image. Vector images cost 2X as much. Default: realistic_image'),
                    colors: z.array(RGBColorSchema).optional().default([]).describe('Array of preferable RGB colors'),
                    style_id: z.string().optional().describe('The ID of the custom style reference (optional)'),
                    enable_safety_checker: z.boolean().optional().describe('Enable safety checker. If set to true, the safety checker will be enabled'),

                    // 系统参数
                    source: z.nativeEnum(SOURCE).describe('Channel source for analytics and tracking')
                })
            ),
        },
        response: {
            200: contentJson({
                taskId: z.string().describe('Task ID for tracking the generation progress')
            }),
            400: contentJson({
                error: z.string().describe('Request validation error')
            }),
            401: contentJson({
                error: z.string().describe('Authentication error')
            }),
            500: contentJson({
                error: z.string().describe('Internal server error')
            })
        }
    };

    async handle(c: Context<{ Bindings: Bindings }>) {
        try {
            const payload = c.get('jwtPayload');
            const data = await c.req.json();

            console.log('Image generator request:', data);
            console.log('User payload:', payload);

            const {
                prompt,
                image_size,
                style,
                colors,
                style_id,
                enable_safety_checker,
                source
            } = data;

            // 创建任务请求 - 直接使用 fal.ai API 参数
            const task: TaskInfoRequest = {
                taskId: nanoid(),
                source,
                uid: payload.uid as string,
                app: APP.IMAGE_GENERATOR,
                accountId: payload.accountId as string,
                status: TaskStatus.WAITING,
                params: {
                    prompt,
                    image_size: image_size || 'square_hd',
                    style: style || 'realistic_image',
                    colors: colors || [],
                    style_id,
                    enable_safety_checker,
                    checkpoint: 'recraft-v3' // 固定使用 recraft-v3
                }
            };

            const taskResult = await doCreateTask(task, c.env);

            return c.json({
                taskId: taskResult?.taskId
            }, 200);

        } catch (error: any) {
            console.error('Image generator task creation failed:', error);
            console.error('Error details:', {
                message: error.message,
                stack: error.stack,
                name: error.name
            });

            if (error instanceof z.ZodError) {
                return c.json({
                    error: `Validation error: ${error.errors.map(e => e.message).join(', ')}`
                }, 400);
            }

            return c.json({
                error: 'Failed to create image generation task'
            }, 500);
        }
    }
} 