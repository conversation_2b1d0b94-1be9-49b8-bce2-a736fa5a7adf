import { OpenAPIRoute } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings, SOURCE, APP } from '../types';
import { doCreateTask } from '../service/task/create-task';
import { TaskInfoRequest, TaskStatus } from '../model/task';
import { nanoid } from 'nanoid';
import { isFree302Path } from '../config/free-paths';

// 目标API的基础URL
const TARGET_API_BASE_URL = 'https://api.302.ai';

export class Proxy302 extends OpenAPIRoute {
        schema = {
                summary: 'Proxy requests to 302.ai API',
                description: 'Forwards requests to 302.ai API with credit billing, returns API response directly. Supports additional query parameters for debugging.',
                tags: ['proxy'],
                request: {
                        query: z.object({
                                path: z.string().describe('Path to forward to 302.ai API')
                        }).passthrough().describe('Additional query parameters will be forwarded to 302.ai API'),
                        body: {
                                content: {
                                        'application/json': {
                                                schema: z.object({}).passthrough().describe('Request body to forward to 302.ai API')
                                        }
                                }
                        }
                },
                responses: {
                        200: {
                                description: 'Response from 302.ai API',
                                content: {
                                        'application/json': {
                                                schema: z.any()
                                        }
                                }
                        },
                        400: {
                                description: 'Bad request',
                                content: {
                                        'application/json': {
                                                schema: z.object({
                                                        success: z.boolean(),
                                                        error: z.string()
                                                })
                                        }
                                }
                        },
                        500: {
                                description: 'Server error',
                                content: {
                                        'application/json': {
                                                schema: z.object({
                                                        success: z.boolean(),
                                                        error: z.string()
                                                })
                                        }
                                }
                        }
                }
        };

        async handle(c: Context<{ Bindings: Bindings }>) {
                const payload = c.get('jwtPayload');
                console.log(`[Proxy302] Starting request handling - Method: ${c.req.method}, URL: ${c.req.url}`);

                try {
                        // Get the raw request body only for methods that typically have a body
                        const method = c.req.method;
                        let requestBody = {};

                        if (method !== 'GET' && method !== 'HEAD') {
                                try {
                                        // 先获取原始文本，然后尝试解析
                                        const rawBody = await c.req.text();
                                        console.log(`[Proxy302] Raw request body: "${rawBody}"`);
                                        console.log(`[Proxy302] Raw body length: ${rawBody.length}`);

                                        if (rawBody.trim()) {
                                                requestBody = JSON.parse(rawBody);
                                                console.log(`[Proxy302] Parsed request body:`, requestBody);
                                        } else {
                                                console.log(`[Proxy302] Empty request body, using empty object`);
                                                requestBody = {};
                                        }
                                } catch (error) {
                                        console.error(`[Proxy302] Failed to parse request body:`, error);
                                        console.error(`[Proxy302] Error details:`, error instanceof Error ? error.message : 'Unknown error');
                                        // If JSON parsing fails, use empty object
                                        requestBody = {};
                                }
                        }

                        const urlSearchParams = new URLSearchParams(c.req.query());
                        const path = urlSearchParams.get('path');
                        urlSearchParams.delete('path');
                        console.log(`[Proxy302] Query params:`, Object.fromEntries(urlSearchParams.entries()));
                        console.log(`[Proxy302] Target path: "${path}"`);

                        if (!path) {
                                console.error(`[Proxy302] Missing path in query`);
                                return c.json({ success: false, error: 'Missing path in query' }, 400);
                        }

                        // 先调用 302.ai API
                        const queryString = urlSearchParams.toString();
                        // 确保path正确拼接，避免双斜杠
                        const cleanPath = path.startsWith('/') ? path.slice(1) : path;
                        const targetUrl = `${TARGET_API_BASE_URL}/${cleanPath}${queryString ? '?' + queryString : ''}`;
                        console.log(`[Proxy302] Proxying request to: ${targetUrl}`);

                        if (!c.env['302_API_KEY']) {
                                console.error('[Proxy302] Missing 302_API_KEY environment variable');
                                return c.json({
                                        success: false,
                                        error: {
                                                message: '302_API_KEY environment variable is missing',
                                                code: 'MISSING_API_KEY'
                                        }
                                }, 500);
                        }

                        const originalHeaders = c.req.header();
                        const headers = new Headers();
                        console.log(`[Proxy302] Original headers:`, originalHeaders);

                        for (const [key, value] of Object.entries(originalHeaders)) {
                                if (key.toLowerCase() !== 'host' && key.toLowerCase() !== 'connection') {
                                        headers.set(key, value);
                                }
                        }
                        headers.set('Authorization', `Bearer ${c.env['302_API_KEY']}`);

                        const requestInit: any = {
                                method,
                                headers
                        };

                        if (method !== 'GET' && method !== 'HEAD') {
                                const bodyString = JSON.stringify(requestBody);
                                console.log(`[Proxy302] Request body string: "${bodyString}"`);
                                requestInit.body = bodyString;
                                headers.set('content-type', 'application/json');
                        }

                        console.log(`[Proxy302] Final request init:`, {
                                method: requestInit.method,
                                headers: Object.fromEntries(headers.entries()),
                                body: requestInit.body
                        });

                        // 调用 302.ai API
                        console.log(`[Proxy302] Making fetch request...`);
                        const response = await fetch(targetUrl, requestInit);
                        console.log(`[Proxy302] Response status: ${response.status} ${response.statusText}`);

                        // 如果 API 调用成功，根据请求方法决定是否扣费
                        if (response.ok) {
                                // 先获取响应文本，然后尝试解析（所有成功响应都需要解析为JSON）
                                console.log(`[Proxy302] Getting response text...`);
                                const responseText = await response.text();
                                console.log(`[Proxy302] Response text length: ${responseText.length}`);
                                console.log(`[Proxy302] Raw response text: "${responseText}"`);

                                if (responseText.length === 0) {
                                        console.error(`[Proxy302] Empty response from 302.ai API`);
                                        return c.json({
                                                success: false,
                                                error: 'Empty response from 302.ai API'
                                        }, 500);
                                }

                                let responseData;
                                try {
                                        console.log(`[Proxy302] Attempting to parse JSON response...`);
                                        responseData = JSON.parse(responseText);
                                        console.log(`[Proxy302] Successfully parsed response data:`, responseData);
                                } catch (parseError) {
                                        console.error(`[Proxy302] Failed to parse JSON response:`, parseError);
                                        console.error(`[Proxy302] Parse error details:`, parseError instanceof Error ? parseError.message : 'Unknown parsing error');
                                        console.error(`[Proxy302] Raw response text:`, responseText);
                                        console.error(`[Proxy302] Response text bytes:`, Array.from(responseText).map(c => c.charCodeAt(0)));
                                        return c.json({
                                                success: false,
                                                error: `Invalid JSON response from 302.ai API: ${parseError instanceof Error ? parseError.message : 'Unknown parsing error'}`
                                        }, 500);
                                }

                                let taskId: string | undefined;

                                // 只有 POST 请求且不是免费路径才需要扣费
                                if (method === 'POST' && !isFree302Path(path)) {
                                        // 创建任务以处理积分（异步处理，不影响 API 响应速度）
                                        const task: TaskInfoRequest = {
                                                taskId: nanoid(),
                                                source: (requestBody as any).source || SOURCE.WEB,
                                                uid: payload.uid as string,
                                                app: APP.AI_302,
                                                accountId: payload.accountId as string,
                                                status: TaskStatus.WAITING,
                                                params: {
                                                        path,
                                                        method,
                                                        queryParams: Object.fromEntries(urlSearchParams.entries()),
                                                        requestBody: requestBody,
                                                        targetUrl,
                                                        result: responseData // 存储 API 结果用于任务记录
                                                }
                                        };

                                        try {
                                                await doCreateTask(task, c.env);
                                                taskId = task.taskId;
                                        } catch (error) {
                                                console.error('Failed to create task for 302 API call:', error);
                                                // 不影响 API 响应，只记录错误
                                        }
                                }

                                // 所有成功的响应都返回解析后的JSON格式，并包含taskId（如果有的话）
                                const finalResponse = { ...responseData };
                                if (taskId) {
                                        finalResponse.taskId = taskId;
                                }
                                return c.json(finalResponse);
                        } else {
                                // API 调用失败，直接返回错误响应
                                console.error(`[Proxy302] 302.ai API call failed: ${response.status} ${response.statusText}`);
                                const errorData = await response.text();
                                console.error(`[Proxy302] Error response data:`, errorData);
                                return new Response(errorData, {
                                        status: response.status,
                                        headers: response.headers
                                });
                        }
                } catch (error: any) {
                        console.error('[Proxy302] Proxy request failed:', error);
                        console.error('[Proxy302] Error stack:', error.stack);
                        return c.json({
                                success: false,
                                error: error.message || 'Failed to proxy request'
                        }, 500);
                }
        }
} 
