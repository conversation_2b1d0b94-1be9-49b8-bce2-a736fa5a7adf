import { OpenAPIRoute } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings, SOURCE, APP } from '../types';
import { doCreateTask } from '../service/task/create-task';
import { TaskInfoRequest, TaskStatus } from '../model/task';
import { nanoid } from 'nanoid';
import { isFree302Path } from '../config/free-paths';

// 目标API的基础URL
const TARGET_API_BASE_URL = 'https://api.302.ai';

export class Proxy302 extends OpenAPIRoute {
        schema = {
                summary: 'Proxy requests to 302.ai API',
                description: 'Forwards requests to 302.ai API with credit billing, returns API response directly. Supports additional query parameters for debugging.',
                tags: ['proxy'],
                request: {
                        query: z.object({
                                path: z.string().describe('Path to forward to 302.ai API')
                        }).passthrough().describe('Additional query parameters will be forwarded to 302.ai API'),
                        body: {
                                content: {
                                        'application/json': {
                                                schema: z.object({}).passthrough().describe('Request body to forward to 302.ai API')
                                        }
                                }
                        }
                },
                responses: {
                        200: {
                                description: 'Response from 302.ai API',
                                content: {
                                        'application/json': {
                                                schema: z.any()
                                        }
                                }
                        },
                        400: {
                                description: 'Bad request',
                                content: {
                                        'application/json': {
                                                schema: z.object({
                                                        success: z.boolean(),
                                                        error: z.string()
                                                })
                                        }
                                }
                        },
                        500: {
                                description: 'Server error',
                                content: {
                                        'application/json': {
                                                schema: z.object({
                                                        success: z.boolean(),
                                                        error: z.string()
                                                })
                                        }
                                }
                        }
                }
        };

        async handle(c: Context<{ Bindings: Bindings }>) {
                const payload = c.get('jwtPayload');
                console.log(`[Proxy302] Starting request handling - Method: ${c.req.method}, URL: ${c.req.url}`);

                try {
                        // 直接在 Proxy302 中处理多种请求格式
                        const method = c.req.method;
                        let requestBody = {};
                        let isFormData = false;
                        const contentType = c.req.header('content-type') || '';

                        console.log(`[Proxy302] Request timestamp: ${new Date().toISOString()}`);
                        console.log(`[Proxy302] Request URL: ${c.req.url}`);
                        console.log(`[Proxy302] Content-Type: "${contentType}"`);
                        console.log(`[Proxy302] Method: ${method}`);

                        if (method !== 'GET' && method !== 'HEAD') {
                                try {
                                        if (contentType.includes('multipart/form-data') || contentType.includes('application/x-www-form-urlencoded')) {
                                                // 处理 form-data 格式
                                                console.log(`[Proxy302] Parsing as form-data`);
                                                const formData = await c.req.parseBody();
                                                requestBody = formData;
                                                isFormData = true;

                                                console.log(`[Proxy302] Form data keys:`, Object.keys(formData));
                                                // 详细记录每个字段
                                                for (const [key, value] of Object.entries(formData)) {
                                                        if (value instanceof File) {
                                                                console.log(`[Proxy302] Field ${key}: File(name="${value.name}", size=${value.size}, type="${value.type}")`);
                                                        } else {
                                                                console.log(`[Proxy302] Field ${key}: "${value}"`);
                                                        }
                                                }
                                        } else {
                                                // 处理 JSON 格式
                                                console.log(`[Proxy302] Parsing as JSON`);
                                                const rawBody = await c.req.text();
                                                console.log(`[Proxy302] Raw request body length: ${rawBody.length}`);
                                                console.log(`[Proxy302] Raw request body: "${rawBody.substring(0, 200)}${rawBody.length > 200 ? '...' : ''}"`);

                                                if (rawBody.trim()) {
                                                        requestBody = JSON.parse(rawBody);
                                                        console.log(`[Proxy302] Parsed JSON body:`, requestBody);
                                                } else {
                                                        console.log(`[Proxy302] Empty request body, using empty object`);
                                                        requestBody = {};
                                                }
                                        }
                                } catch (error) {
                                        console.error(`[Proxy302] Failed to parse request body:`, error instanceof Error ? error.message : 'Unknown error');
                                        console.error(`[Proxy302] Error stack:`, error instanceof Error ? error.stack : 'No stack');
                                        requestBody = {};
                                }
                        }

                        const urlSearchParams = new URLSearchParams(c.req.query());
                        const path = urlSearchParams.get('path');
                        urlSearchParams.delete('path');
                        console.log(`[Proxy302] Query params:`, Object.fromEntries(urlSearchParams.entries()));
                        console.log(`[Proxy302] Target path: "${path}"`);

                        if (!path) {
                                console.error(`[Proxy302] Missing path in query`);
                                return c.json({ success: false, error: 'Missing path in query' }, 400);
                        }

                        // 先调用 302.ai API
                        const queryString = urlSearchParams.toString();
                        // 确保path正确拼接，避免双斜杠
                        const cleanPath = path.startsWith('/') ? path.slice(1) : path;
                        const targetUrl = `${TARGET_API_BASE_URL}/${cleanPath}${queryString ? '?' + queryString : ''}`;
                        console.log(`[Proxy302] Proxying request to: ${targetUrl}`);

                        if (!c.env['302_API_KEY']) {
                                console.error('[Proxy302] Missing 302_API_KEY environment variable');
                                return c.json({
                                        success: false,
                                        error: {
                                                message: '302_API_KEY environment variable is missing',
                                                code: 'MISSING_API_KEY'
                                        }
                                }, 500);
                        }

                        const originalHeaders = c.req.header();
                        const headers = new Headers();
                        console.log(`[Proxy302] Original headers:`, originalHeaders);

                        // 过滤掉可能导致问题的 headers
                        const skipHeaders = [
                                'host',
                                'connection',
                                'content-length',
                                'cache-control',
                                'postman-token',
                                'user-agent',
                                'accept-encoding'
                        ];

                        for (const [key, value] of Object.entries(originalHeaders)) {
                                if (!skipHeaders.includes(key.toLowerCase())) {
                                        headers.set(key, value);
                                        console.log(`[Proxy302] Keeping header: ${key}: ${value}`);
                                } else {
                                        console.log(`[Proxy302] Skipping header: ${key}: ${value}`);
                                }
                        }
                        headers.set('Authorization', `Bearer ${c.env['302_API_KEY']}`);

                        const requestInit: any = {
                                method,
                                headers
                        };

                        if (method !== 'GET' && method !== 'HEAD') {
                                if (isFormData) {
                                        // 对于 form-data，重新构建 FormData
                                        console.log(`[Proxy302] Rebuilding FormData for forwarding`);
                                        const formData = new FormData();

                                        for (const [key, value] of Object.entries(requestBody)) {
                                                if (value instanceof File) {
                                                        // 直接添加文件
                                                        formData.append(key, value);
                                                        console.log(`[Proxy302] Added file: ${key} = ${value.name} (${value.size} bytes)`);
                                                } else if (value !== null && value !== undefined) {
                                                        // 添加普通字段，确保转换为字符串
                                                        const stringValue = String(value);
                                                        formData.append(key, stringValue);
                                                        console.log(`[Proxy302] Added field: ${key} = "${stringValue}"`);
                                                }
                                        }

                                        requestInit.body = formData;
                                        // 不设置 content-type，让 fetch 自动设置正确的 multipart/form-data 边界
                                        headers.delete('content-type');
                                        console.log(`[Proxy302] Using FormData body with ${Object.keys(requestBody).length} fields`);
                                } else {
                                        // JSON 格式
                                        const bodyString = JSON.stringify(requestBody);
                                        console.log(`[Proxy302] JSON body string length: ${bodyString.length}`);
                                        console.log(`[Proxy302] JSON body: "${bodyString.substring(0, 200)}${bodyString.length > 200 ? '...' : ''}"`);
                                        requestInit.body = bodyString;
                                        headers.set('content-type', 'application/json');
                                }
                        }

                        console.log(`[Proxy302] Final request init:`, {
                                method: requestInit.method,
                                headers: Object.fromEntries(headers.entries()),
                                body: requestInit.body
                        });

                        // 详细记录请求信息用于对比
                        console.log(`[Proxy302] ===== REQUEST COMPARISON =====`);
                        console.log(`[Proxy302] Target URL: ${targetUrl}`);
                        console.log(`[Proxy302] Method: ${requestInit.method}`);
                        console.log(`[Proxy302] Headers:`);
                        for (const [key, value] of headers.entries()) {
                                console.log(`[Proxy302]   ${key}: ${value}`);
                        }

                        if (requestInit.body instanceof FormData) {
                                console.log(`[Proxy302] FormData contents:`);
                                for (const [key, value] of requestInit.body.entries()) {
                                        if (value instanceof File) {
                                                console.log(`[Proxy302]   ${key}: File(name="${value.name}", size=${value.size}, type="${value.type}")`);
                                        } else {
                                                console.log(`[Proxy302]   ${key}: "${value}"`);
                                        }
                                }
                        }
                        console.log(`[Proxy302] ================================`);

                        // 调用 302.ai API
                        console.log(`[Proxy302] Making fetch request...`);
                        const response = await fetch(targetUrl, requestInit);
                        console.log(`[Proxy302] Response status: ${response.status} ${response.statusText}`);

                        // 如果 API 调用成功，根据请求方法决定是否扣费
                        if (response.ok) {
                                // 先获取响应文本，然后尝试解析（所有成功响应都需要解析为JSON）
                                console.log(`[Proxy302] Getting response text...`);
                                const responseText = await response.text();
                                console.log(`[Proxy302] Response text length: ${responseText.length}`);
                                console.log(`[Proxy302] Raw response text: "${responseText}"`);

                                if (responseText.length === 0) {
                                        console.error(`[Proxy302] Empty response from 302.ai API`);
                                        return c.json({
                                                success: false,
                                                error: 'Empty response from 302.ai API'
                                        }, 500);
                                }

                                let responseData;
                                try {
                                        console.log(`[Proxy302] Attempting to parse JSON response...`);
                                        responseData = JSON.parse(responseText);
                                        console.log(`[Proxy302] Successfully parsed response data:`, responseData);
                                } catch (parseError) {
                                        console.error(`[Proxy302] Failed to parse JSON response:`, parseError);
                                        console.error(`[Proxy302] Parse error details:`, parseError instanceof Error ? parseError.message : 'Unknown parsing error');
                                        console.error(`[Proxy302] Raw response text:`, responseText);
                                        console.error(`[Proxy302] Response text bytes:`, Array.from(responseText).map(c => c.charCodeAt(0)));
                                        return c.json({
                                                success: false,
                                                error: `Invalid JSON response from 302.ai API: ${parseError instanceof Error ? parseError.message : 'Unknown parsing error'}`
                                        }, 500);
                                }

                                let taskId: string | undefined;

                                // 只有 POST 请求且不是免费路径才需要扣费
                                if (method === 'POST' && !isFree302Path(path)) {
                                        // 创建任务以处理积分（异步处理，不影响 API 响应速度）
                                        const task: TaskInfoRequest = {
                                                taskId: nanoid(),
                                                source: (requestBody as any).source || SOURCE.WEB,
                                                uid: payload.uid as string,
                                                app: APP.AI_302,
                                                accountId: payload.accountId as string,
                                                status: TaskStatus.WAITING,
                                                params: {
                                                        path,
                                                        method,
                                                        queryParams: Object.fromEntries(urlSearchParams.entries()),
                                                        requestBody: requestBody,
                                                        targetUrl,
                                                        result: responseData // 存储 API 结果用于任务记录
                                                }
                                        };

                                        try {
                                                await doCreateTask(task, c.env);
                                                taskId = task.taskId;
                                        } catch (error) {
                                                console.error('Failed to create task for 302 API call:', error);
                                                // 不影响 API 响应，只记录错误
                                        }
                                }

                                // 所有成功的响应都返回解析后的JSON格式，并包含taskId（如果有的话）
                                const finalResponse = { ...responseData };
                                if (taskId) {
                                        finalResponse.taskId = taskId;
                                }
                                return c.json(finalResponse);
                        } else {
                                // API 调用失败，直接返回错误响应
                                console.error(`[Proxy302] 302.ai API call failed: ${response.status} ${response.statusText}`);
                                const errorData = await response.text();
                                console.error(`[Proxy302] Error response data:`, errorData);

                                // 提供调试建议
                                console.error(`[Proxy302] ===== DEBUGGING INFO =====`);
                                console.error(`[Proxy302] Target URL: ${targetUrl}`);
                                console.error(`[Proxy302] Request method: ${method}`);
                                console.error(`[Proxy302] Content-Type: ${contentType}`);
                                console.error(`[Proxy302] Is FormData: ${isFormData}`);

                                if (isFormData && requestInit.body instanceof FormData) {
                                        console.error(`[Proxy302] Equivalent curl command:`);
                                        console.error(`[Proxy302]   curl -X POST "${targetUrl}" \\`);
                                        console.error(`[Proxy302]     -H "Authorization: Bearer ${c.env['302_API_KEY']}" \\`);
                                        for (const [key, value] of requestInit.body.entries()) {
                                                if (value instanceof File) {
                                                        console.error(`[Proxy302]     --form '${key}=@"path/to/${value.name}"' \\`);
                                                } else {
                                                        console.error(`[Proxy302]     --form '${key}="${value}"' \\`);
                                                }
                                        }
                                } else {
                                        console.error(`[Proxy302] Request body:`, requestInit.body);
                                }
                                console.error(`[Proxy302] ========================`);

                                return new Response(errorData, {
                                        status: response.status,
                                        headers: response.headers
                                });
                        }
                } catch (error: any) {
                        console.error('[Proxy302] Proxy request failed:', error);
                        console.error('[Proxy302] Error stack:', error.stack);
                        return c.json({
                                success: false,
                                error: error.message || 'Failed to proxy request'
                        }, 500);
                }
        }
} 
