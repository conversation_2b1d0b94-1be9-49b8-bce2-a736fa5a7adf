import { OpenAPIRoute } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings } from '../types';
import { doFetchTask, doFetchTaskData } from '../service/task/fetch-task';
import { TaskStatus } from '../model/task';
import { isTerminalState } from '../utils/task-utils';
import { TaskMapper } from '../service/task/task-mapper';
import { UrlProcessor } from '../utils/url-processor';
import { doGetTask } from '../service/supabase/task';


export class FetchTaskSSE extends OpenAPIRoute {
    schema = {
        summary: '获取任务状态 SSE',
        description: `获取任务状态，如果成功同时返回图片地址.`,
        tags: ['fetch-task'] as string[],
        request: {
            params: z.object({
                taskId: z.string(),
            }),
            query: z.object({
                targetImageType: z.enum(['png', 'jpeg', 'jpg', 'webp']).optional()
            })
        },
        responses: {
            200: {
                description: 'SSE stream with task status updates',
                content: {
                    'text/event-stream': {
                        schema: z.string().describe('Server-sent events stream')
                    }
                }
            }
        }
    };

    async handle(c: Context<{ Bindings: Bindings }>) {
        const data = await this.getValidatedData<typeof this.schema>();

        const taskId = data.params.taskId;
        const targetImageType = data.query?.targetImageType;

        const headers: Record<string, string> = {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            Connection: 'keep-alive',
        };
        Object.keys(headers).forEach((key) => {
            c.header(key, headers[key]);
        });

        const { readable, writable } = new TransformStream();

        const enc = new TextEncoder();

        const writer = writable.getWriter();

        // 启动异步轮询任务，但不等待它完成
        // 这样可以立即返回 readable stream，而轮询会在后台继续进行
        doFetchTask(writer, enc, taskId, c.env, targetImageType).catch(error => {
            console.error('Error in background task polling:', error);
            // 如果轮询出错，尝试发送错误信息并关闭连接
            writer.write(enc.encode(`data: ${JSON.stringify({
                taskId,
                status: 'FAILED',
                error: 'Internal server error during task polling'
            })}\n\n`)).catch(() => { }).finally(() => {
                writer.close().catch(() => { });
            });
        });

        return c.body(readable);
    }
}

export class FetchTask extends OpenAPIRoute {
    schema = {
        summary: '获取任务状态',
        description: `获取任务状态，如果成功同时返回图片地址.`,
        tags: ['fetch-task'] as string[],
        request: {
            params: z.object({
                taskId: z.string(),
            }),
            query: z.object({
                targetImageType: z.enum(['png', 'jpeg', 'jpg', 'webp']).optional()
            })
        },
        responses: {
            200: {
                description: 'Success',
                content: {
                    'application/json': {
                        schema: z.object({
                            taskId: z.string(),
                            status: z.string(),
                            error: z.string().optional(),
                            mimeType: z.string().optional(),
                            imageUrl: z.string().optional(),
                            thumbUrl: z.string().optional(),
                            duration: z.number().optional(),
                            videoUrl: z.string().optional(),
                            sketchImageUrl: z.string().optional(),
                            colorImageUrl: z.string().optional(),
                            imageUrls: z.array(z.string()).optional(),
                            message: z.string().optional(),
                            nsfw: z.boolean().optional()
                        })
                    }
                }
            }
        }
    };

    async handle(c: Context<{ Bindings: Bindings }>) {
        const data = await this.getValidatedData<typeof this.schema>();

        const taskId = data.params.taskId;
        const targetImageType = data.query?.targetImageType;

        const result = await doFetchTaskData(taskId, c.env, targetImageType);

        // 使用新的TaskMapper替代deprecated的parseTaskResult
        let taskResult = TaskMapper.normalizeTaskResult(taskId, result);

        // 如果是终态，先处理URL转换再返回
        if (isTerminalState(taskResult.status as TaskStatus)) {
            try {
                // 获取任务信息来获得 app 参数
                const taskInfo = await doGetTask(c.env, taskId);
                const app = taskInfo?.app;
                // 先处理 URL 转换 - SSE接口跳过特殊字段(sketchImageUrl, colorImageUrl, videoUrl)转换
                const processedTaskResult = await UrlProcessor.processTaskResultWithUrlConversion(
                    taskResult,
                    c.env,
                    app,
                    { skipSpecialFields: true }
                );

                // 过滤掉内部字段
                const clientResult = TaskMapper.filterClientFields(processedTaskResult);

                // 发送到消息队列（用于后续处理，如统计、通知等）
                await c.env.TASK_QUEUE.send(processedTaskResult);

                // 返回处理后的结果
                return c.json(clientResult);
            } catch (error) {
                console.error('Error processing task URLs:', error);
                // 如果URL处理失败，返回原始结果（也要过滤内部字段）
                const clientResult = TaskMapper.filterClientFields(taskResult);
                return c.json(clientResult);
            }
        }

        // 非终态也要过滤内部字段
        const clientResult = TaskMapper.filterClientFields(taskResult);
        return c.json(clientResult);
    }
}
