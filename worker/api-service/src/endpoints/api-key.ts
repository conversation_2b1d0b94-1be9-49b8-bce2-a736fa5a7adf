import { OpenAPIRoute, contentJson } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings } from '../types';
import { CreditError } from '../service/credit/errors';
import { doGenApiKey, doGetApiKeys,doUpdateApiKey } from '../../../auth-service/src/service/apiKey';
import { ApiKey } from '../service/supabase/models';

export class Gen<PERSON><PERSON><PERSON><PERSON> extends OpenAPIRoute {
        schema = {
                summary: 'Generate API Key',
                description: 'If user already has an API Key, return it directly, otherwise generate a new API Key',
                tags: ['api-key'],
                request: {
                        body: {
                                content: {
                                        'application/json': {
                                                schema: z.object({
                                                        scope: z.string().optional().describe('API Key scope'),
                                                        name: z.string().optional().describe('API Key name')
                                                })
                                        }
                                }
                        }
                },
                response: {
                        200: contentJson({
                                success: z.boolean().describe('Whether successful'),
                                code: z.nativeEnum(CreditError).optional(),
                                message: z.string().optional(),
                                data: z.object({
                                        id: z.string().describe('API Key ID'),

                                        apiKey: z.string().describe('API Key'),
                                        name: z.string().describe('API Key name'),
                                        scope: z.string().describe('API Key scope'),
                                        status: z.string().describe('API Key status'),
                                        uid: z.string().describe('API Key user ID'),
                                        account_id: z.string().describe('API Key account ID'),
                                        created_at: z.string().describe('API Key creation time'),
                                        updated_at: z.string().describe('API Key update time')
                                })
                        }),
                        400: contentJson({
                                error: z.string().describe('Request error message')
                        }),
                        401: contentJson({
                                error: z.string().describe('Authentication error message')
                        }),
                        500: contentJson({
                                error: z.string().describe('Internal server error message')
                        })
                }
        };

        async handle(c: Context<{ Bindings: Bindings }>) {
                try {
                        const payload = c.get('jwtPayload');
                        const data = await this.getValidatedData<typeof this.schema>();
                        const { scope, name } = data.body;

                        const insertApiKey = {
                                scope,
                                name,
                                uid: payload.uid,
                                account_id: payload.accountId
                        } as ApiKey;

                        let apiKey = null;
                        if (c.env.ENV === 'dev') {
                                apiKey = await doGenApiKey(c.env, insertApiKey);
                        } else {
                                apiKey = await c.env.AUTH_SERVICE.doGenApiKey(insertApiKey);
                        }
                        return c.json({
                                success: true,
                                data: apiKey
                        });
                } catch (error: any) {
                        console.error('Token exchange failed:', error);
                        if (error instanceof z.ZodError) {
                                return c.json({ success: false, code: CreditError.INVALID_REQUEST, message: error.errors[0].message }, 400);
                        }
                        return c.json({ success: false, code: CreditError.INTERNAL_ERROR, message: 'Token exchange failed' }, 500);
                }
        }
}


export class GetApiKey extends OpenAPIRoute {
        schema = {
                summary: 'Get user API Keys',
                description: 'Get user API Keys',
                tags: ['api-key'],
                request: {
                },
                response: {
                        200: contentJson({
                                success: z.boolean().describe('Whether successful'),
                                code: z.nativeEnum(CreditError).optional(),
                                message: z.string().optional(),
                                data: z.array(z.object({
                                        id: z.string().describe('API Key ID'),
                                        apiKey: z.string().describe('Generated API Key'),
                                        name: z.string().describe('API Key name'),
                                        scope: z.string().describe('API Key scope'),
                                        created_at: z.string().describe('API Key creation time')
                                }))
                        }),
                        400: contentJson({
                                error: z.string().describe('Request error message')
                        }),
                        401: contentJson({
                                error: z.string().describe('Authentication error message')
                        }),
                        500: contentJson({
                                error: z.string().describe('Internal server error message')
                        })
                }
        };

        async handle(c: Context<{ Bindings: Bindings }>) {
                try {
                        const payload = c.get('jwtPayload');
                        const uid = payload.uid as string;

                        let apiKeys = [] as ApiKey[];
                        
                        if (c.env.ENV === 'dev') {
                                apiKeys = await doGetApiKeys(c.env, uid);
                        } else {
                                apiKeys = await c.env.AUTH_SERVICE.doGetApiKeys(uid);
                        }
                        return c.json({
                                success: true,
                                data: apiKeys
                        });
                } catch (error: any) {
                        console.error('Token exchange failed:', error);
                        if (error instanceof z.ZodError) {
                                return c.json({ success: false, code: CreditError.INVALID_REQUEST, message: error.errors[0].message }, 400);
                        }
                        return c.json({ success: false, code: CreditError.INTERNAL_ERROR, message: 'Token exchange failed' }, 500);
                }
        }
}

export class UpdateApiKey extends OpenAPIRoute {
        schema = {
                summary: 'Update user API Key',
                description: 'Update user API Key',
                tags: ['api-key'],
                request: {
                        body: {
                                content: {
                                        'application/json': {
                                                schema: z.object({
                                                        isDelete: z.boolean().default(false).describe('Whether to delete'),
                                                        id: z.string().describe('API Key ID'),
                                                        name: z.string().optional().describe('API Key name'),
                                                })
                                        }
                                }
                        }
                },
                response: {
                        200: contentJson({
                                success: z.boolean().describe('Whether successful'),
                                code: z.nativeEnum(CreditError).optional(),
                                message: z.string().optional(),
                                data: z.object({
                                                id: z.string().describe('API Key ID'),
                                                apiKey: z.string().describe('Generated API Key'),
                                                name: z.string().describe('API Key name'),
                                                scope: z.string().describe('API Key scope'),
                                                status: z.string().describe('API Key status'),
                                                created_at: z.string().describe('API Key creation time'),
                                                updated_at: z.string().describe('API Key update time')
                                })
                        }),
                        400: contentJson({
                                error: z.string().describe('Request error message')
                        }),
                        401: contentJson({
                                error: z.string().describe('Authentication error message')
                        }),
                        500: contentJson({
                                error: z.string().describe('Internal server error message')
                        })
                }
        };

        async handle(c: Context<{ Bindings: Bindings }>) {
                try {
                        const payload = c.get('jwtPayload');
                        const uid = payload.uid as string;

                        const data = await this.getValidatedData<typeof this.schema>();
                        if (!data) {
                                throw new Error('Validated data is undefined');
                        }
                        const { isDelete, id } = data.body;

                        let updateApiKey = {
                                uid,
                                id
                        } as ApiKey;    

                        if(isDelete) {
                                updateApiKey.deleted_at = new Date();
                                updateApiKey.status = 'DELETED';
                        }

                        if (c.env.ENV === 'dev') {
                                await doUpdateApiKey(c.env, updateApiKey);
                        } else {
                                await c.env.AUTH_SERVICE.doUpdateApiKey(updateApiKey);
                        }
                        return c.json({
                                success: true,
                                data: {}
                        });
                } catch (error: any) {
                        console.error('Token exchange failed:', error);
                        if (error instanceof z.ZodError) {
                                return c.json({ success: false, code: CreditError.INVALID_REQUEST, message: error.errors[0].message }, 400);
                        }
                        return c.json({ success: false, code: CreditError.INTERNAL_ERROR, message: 'Token exchange failed' }, 500);
                }
        }
}