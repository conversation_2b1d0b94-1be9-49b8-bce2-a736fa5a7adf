import { OpenAPIRoute, contentJson } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings, R2_PREFIX_RESULT } from '../types';
import { nanoid } from 'nanoid';
import { UrlProcessor } from '../utils/url-processor';

export class GeminiImageGeneration extends OpenAPIRoute {
    schema = {
        summary: 'Gemini Image Generation',
        description: '使用 Google Gemini API 进行图像生成和编辑',
        tags: ['api'],
        request: {
            body: contentJson(
                z.object({
                    imageUrls: z.array(z.string()).optional().describe('用于编辑的图片URL数组'),
                    prompt: z.string().describe('图像生成或编辑的提示文本')
                })
            )
        },
        response: {
            200: contentJson({
                success: z.boolean().describe('请求是否成功'),
                data: z.object({
                    images: z.array(z.object({
                        url: z.string().describe('图像数据，使用 Data URL 格式'),
                        mimeType: z.string().describe('图像的MIME类型')
                    })).optional(),
                    text: z.object({
                        content: z.string().describe('生成的文本内容'),
                        mimeType: z.string().describe('文本的MIME类型')
                    }).optional()
                })
            }),
            400: contentJson({
                success: z.boolean().describe('请求是否成功'),
                error: z.string().describe('错误信息')
            }),
            401: contentJson({
                success: z.boolean().describe('请求是否成功'),
                error: z.string().describe('Authentication error message')
            }),
            500: contentJson({
                success: z.boolean().describe('请求是否成功'),
                error: z.string().describe('Internal server error message')
            })
        }
    };

    async handle(c: Context<{ Bindings: Bindings }>) {
        console.log('=== Gemini Image Generation API 请求开始 ===');
        const requestId = crypto.randomUUID();
        console.log(`[${requestId}] 请求开始处理`);

        try {
            // 从请求体中获取数据
            const body = await c.req.json();
            const { imageUrls = [], prompt } = body;

            console.log(`[${requestId}] 请求参数:`, {
                imageUrls: imageUrls.length > 0 ? `${imageUrls.length} 张图片` : '无图片',
                prompt
            });

            // Lambda URL for Gemini API
            const LAMBDA_URL = 'https://ekpm6dfbhtd7uwnp4ithtshmce0iviau.lambda-url.us-east-1.on.aws/v1/gemini/image-generation';
            console.log(`[${requestId}] 准备调用 Lambda 服务: ${LAMBDA_URL}`);

            // 设置 API 调用超时
            const API_TIMEOUT = 60000; // 60 秒超时

            // 创建超时控制器
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT);

            console.log(`[${requestId}] 开始调用 Lambda 服务`);
            const startTime = Date.now();

            try {
                // 调用 Lambda URL
                const response = await fetch(LAMBDA_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'a1d_api_key'
                    },
                    body: JSON.stringify({
                        imageUrls,
                        prompt
                    }),
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                const endTime = Date.now();
                console.log(`[${requestId}] Lambda 调用完成，耗时: ${endTime - startTime}ms, 状态码: ${response.status}`);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error(`[${requestId}] Lambda 服务返回错误: ${response.status} ${response.statusText}`);
                    console.error(`[${requestId}] 错误详情: ${errorText}`);
                    return c.json({
                        success: false,
                        error: `Lambda service error: ${response.statusText}`
                    }, response.status as any);
                }

                // 处理响应
                const result = await response.json() as Record<string, any>;
                console.log(`[${requestId}] Lambda 服务返回数据处理完成`);

                // 处理图像响应
                if (result.success && result.data && result.data.images) {
                    console.log(`[${requestId}] 处理图像响应，上传到R2`);

                    const processedImages = await Promise.all(result.data.images.map(async (image: any) => {
                        try {
                            // 从 data:image/png;base64,xxx 格式中提取 base64 数据
                            const base64Data = image.url;
                            const base64Parts = base64Data.split(',');
                            const mimeType = (base64Parts[0].match(/data:(.*?);/) || [])[1] || 'image/png';
                            const base64Content = base64Parts[1];

                            // 解码 base64 数据
                            const binaryData = Buffer.from(base64Content, 'base64');

                            // 创建文件名
                            const fileName = `c2d/${nanoid()}_${Date.now()}.${mimeType.split('/')[1]}`;
                            const pathname = `${R2_PREFIX_RESULT}/${fileName}`;

                            // 设置文件类型
                            const headers = new Headers();
                            headers.set('content-type', mimeType);

                            // 上传到 R2
                            console.log(`[${requestId}] 上传图像到 R2: ${pathname}`);
                            await c.env.MY_BUCKET.put(pathname, binaryData, { httpMetadata: headers });

                            const r2Url = UrlProcessor.buildR2Url(c.env.R2_PUBLIC_DOMAIN, pathname);

                            console.log(`[${requestId}] 图像已上传，生成URL: ${r2Url.substring(0, 50)}...`);

                            return {
                                url: r2Url,
                                mimeType
                            };
                        } catch (uploadError) {
                            console.error(`[${requestId}] 上传图像到 R2 失败:`, uploadError);
                            // 如果上传失败，回退到直接返回base64
                            return {
                                url: image.url,
                                mimeType: image.mimeType
                            };
                        }
                    }));

                    // 返回处理后的结果
                    return c.json({
                        success: true,
                        data: {
                            images: processedImages,
                            text: result.data.text || {}
                        }
                    });
                }

                // 直接返回原始响应
                console.log(`[${requestId}] 请求成功完成，直接返回原始响应`);
                return c.json(result);

            } catch (apiError) {
                clearTimeout(timeoutId);

                console.error(`[${requestId}] Lambda 调用过程中发生错误:`, apiError);
                if (apiError instanceof Error) {
                    console.error(`[${requestId}] 错误详情: ${apiError.name} - ${apiError.message}`);
                    console.error(`[${requestId}] 错误堆栈: ${apiError.stack}`);

                    // 检查是否是超时错误
                    if (apiError.name === 'AbortError') {
                        return c.json({
                            success: false,
                            error: 'Lambda service request timed out. Please try again later.'
                        }, 504 as any);
                    }
                }
                throw apiError; // 重新抛出错误以便外层 catch 块处理
            }

        } catch (error) {
            console.error(`[${requestId}] 服务错误:`, error);
            if (error instanceof Error) {
                console.error(`[${requestId}] 错误详情: ${error.name} - ${error.message}`);
                console.error(`[${requestId}] 错误堆栈: ${error.stack}`);
            }
            return c.json({
                success: false,
                error: `Service error: ${error instanceof Error ? error.message : String(error)}`
            }, 500 as any);
        } finally {
            console.log(`[${requestId}] === Gemini Image Generation API 请求结束 ===`);
        }
    }
} 