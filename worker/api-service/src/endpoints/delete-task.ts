import { OpenAPIRoute } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings } from '../types';
import { doDeleteTask, DeleteTaskResult } from '../service/supabase/task';

export class DeleteTask extends OpenAPIRoute {
    schema = {
        summary: 'delete task',
        description: 'delete task',
        tags: ['task'],
        request: {
            params: z.object({
                taskId: z.string(),
            }),
        },
        responses: {
            200: {
                description: 'Success',
                content: {
                    'application/json': {
                        schema: z.object({
                            success: z.boolean(),
                            message: z.string()
                        })
                    }
                }
            },
            404: {
                description: 'Task not found',
                content: {
                    'application/json': {
                        schema: z.object({
                            success: z.boolean(),
                            error: z.string()
                        })
                    }
                }
            }
        }
    };

    async handle(c: Context<{ Bindings: Bindings }>) {
        const data = await this.getValidatedData<typeof this.schema>();
        const taskId = data.params.taskId;
        const jwtPayload = c.get('jwtPayload');
        const uid = jwtPayload?.uid;

        if (!uid) {
            return c.json({
                success: false,
                error: 'Unauthorized, user ID is missing'
            }, 401);
        }

        try {
            const result = await doDeleteTask(c.env, taskId, uid);
            
            switch (result) {
                case DeleteTaskResult.SUCCESS:
                    return c.json({
                        success: true,
                        message: 'Task successfully deleted'
                    });
                
                case DeleteTaskResult.ALREADY_DELETED:
                    return c.json({
                        success: true,
                        message: 'Task was already deleted'
                    });
                
                case DeleteTaskResult.NOT_FOUND:
                    return c.json({
                        success: false,
                        error: 'Task not found or you do not have permission to delete this task'
                    }, 404);
                
                case DeleteTaskResult.FAIL:
                default:
                    return c.json({
                        success: false,
                        error: 'Failed to delete task'
                    }, 500);
            }
        } catch (error) {
            console.error('Error deleting task:', error);
            return c.json({
                success: false,
                error: 'Failed to delete task'
            }, 500);
        }
    }
} 