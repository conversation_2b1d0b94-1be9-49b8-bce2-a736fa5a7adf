import { <PERSON><PERSON><PERSON>oute, content<PERSON>son } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings, SOURCE, APP } from '../types';
import { doCreateTask } from '../service/task/create-task';
import { TaskInfoRequest, TaskStatus } from '../model/task';
import { nanoid } from 'nanoid';

export class TextBehindImage extends OpenAPIRoute {
    schema = {
        summary: 'Text behind image, submit Task',
        description: 'Image composition service that places text behind foreground and in front of background',
        tags: ['api'],
        request: {
            body: contentJson(
                z.object({
                    source: z.nativeEnum(SOURCE).describe('Channel source, possible values: "api", "web", "framer"'),
                })
            ),
        },
        response: {
            200: contentJson({
                taskId: z.string().describe('Task ID')
            }),
            400: contentJson({
                error: z.string().describe('Request error message')
            }),
            401: contentJson({
                error: z.string().describe('Authentication error message')
            }),
            500: contentJson({
                error: z.string().describe('Internal server error message')
            })
        }
    };

    async handle(c: Context<{ Bindings: Bindings }>) {
        const payload = c.get('jwtPayload');

        const data = await c.req.json();
        console.log('data', data);
        console.log('payload', payload);

        const { source } = data;

        const task: TaskInfoRequest = {
            taskId: nanoid(),
            source,
            uid: payload.uid as string,
            app: APP.TEXT_BEHIND_IMAGE,
            accountId: payload.accountId as string,
            status: TaskStatus.WAITING,
            params: {}
        }

        try {
            // 创建任务并返回任务ID
            const taskResult = await doCreateTask(task, c.env);
            return c.json({ taskId: taskResult?.taskId }, 200);
        } catch (error: any) {
            console.error('Create task failed:', error);
            console.error('Error details:', {
                message: error.message,
                stack: error.stack,
                name: error.name
            });

            if (error instanceof z.ZodError) {
                return c.json({ error: error.errors[0].message }, 400);
            }
            return c.json({ error: 'Create task failed' }, 500);
        }
    }
}
