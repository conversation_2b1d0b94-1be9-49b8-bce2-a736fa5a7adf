import { OpenAPIRoute, contentJson } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings } from '../types';

export class ImageProxy extends OpenAPIRoute {
    schema = {
        summary: 'Image Proxy',
        description: 'Proxy an image from a remote URL with security checks',
        tags: ['api'],
        request: {
            query: z.object({
                url: z.string().describe('The URL of the image to proxy')
            })
        },
        response: {
            200: {
                description: 'The proxied image',
                content: {
                    'image/*': {}
                }
            },
            400: contentJson({
                error: z.string().describe('Request error information')
            }),
            500: contentJson({
                error: z.string().describe('Server internal error information')
            })
        }
    };

    async handle(c: Context<{ Bindings: Bindings }>) {
        // 处理 OPTIONS 预检请求
        if (c.req.method === 'OPTIONS') {
            return new Response(null, {
                headers: {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
                    'Access-Control-Allow-Headers': '*',
                    'Access-Control-Expose-Headers': '*',
                    'Access-Control-Max-Age': '86400', // 24小时
                }
            });
        }

        const url = c.req.query('url');
        console.log('Received URL:', url);

        if (!url) {
            return c.json({ error: 'Image URL is required' }, 400 as any);
        }

        // 处理嵌套的 URL（如果存在）
        let targetUrl = url;
        try {
            // 检查是否是 Figma CORS 代理的 URL
            if (url.includes('cors-image-proxy.figma.com')) {
                const decodedUrl = decodeURIComponent(url);
                console.log('Decoded Figma proxy URL:', decodedUrl);
                // 提取实际的图片 URL
                const urlMatch = decodedUrl.match(/url=([^&]+)/);
                if (urlMatch) {
                    targetUrl = decodeURIComponent(urlMatch[1]);
                    console.log('Extracted target URL:', targetUrl);
                }
            }
        } catch (error) {
            console.error('Error processing URL:', error);
            return c.json({ error: 'Invalid URL format' }, 400 as any);
        }

        const timeout = 10000; // 10 seconds
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        try {
            console.log('Fetching from URL:', targetUrl);
            const response = await fetch(targetUrl, {
                signal: controller.signal,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.9',
                    Referer: 'https://www.google.com/',
                    'Cache-Control': 'no-cache',
                    Pragma: 'no-cache',
                },
            });

            if (!response.ok) {
                console.error('Fetch failed:', response.status, response.statusText);
                return c.json({ error: `HTTP error! status: ${response.status}` }, response.status as any);
            }

            // check image type
            const contentType = response.headers.get('content-type');
            if (!contentType?.startsWith('image/')) {
                console.error('Invalid content type:', contentType);
                return c.json({ error: 'Invalid image type' }, 400 as any);
            }

            // Create a new response with the same body and headers
            const newResponse = new Response(response.body, {
                headers: {
                    ...Object.fromEntries(response.headers),
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
                    'Access-Control-Allow-Headers': '*',
                    'Access-Control-Expose-Headers': '*'
                },
                status: response.status,
                statusText: response.statusText
            });

            return newResponse;
        } catch (error) {
            if (error instanceof Error) {
                console.error('Image proxy error:', error.message);
                if (error.name === 'AbortError') {
                    return c.json({ error: 'Request timed out' }, 408 as any);
                }
            }
            return c.json({ error: 'Failed to fetch image' }, 500 as any);
        } finally {
            clearTimeout(timeoutId);
        }
    }
} 