import { <PERSON><PERSON><PERSON>oute, contentJson } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings, SOURCE, APP } from '../types';
import { doCreateTask } from '../service/task/create-task';
import { TaskInfoRequest, TaskStatus } from '../model/task';
import { nanoid } from 'nanoid';

export class ImageRelighter extends OpenAPIRoute {
        schema = {
                summary: 'Image relighter, submit Task',
                description: 'Submit image relighter task',
                tags: ['api'],
                request: {
                        body: contentJson(
                                z.object({
                                        imageUrl: z.string().url(),
                                        source: z.nativeEnum(SOURCE).describe('Channel source, possible values: "api", "web", "framer", "figma", "canva", "rapidapi", "mcp"'),
                                        scene: z.number()
                                        .int()
                                        .optional()
                                        .describe('The number mapping to the scene in the app template'),
                                        refImageUrl: z.string()
                                        .url()
                                        .optional()
                                        .describe('Reference image URL for relighting')
                                })
                        )
                },
                response: {
                    200: contentJson({
                            taskId: z.string().describe('Task ID')
                    }),
                    400: contentJson({
                            error: z.string().describe('Request error message')
                    }),
                    401: contentJson({
                            error: z.string().describe('Authentication error message')
                    }),
                    500: contentJson({
                            error: z.string().describe('Internal server error message')
                    })
                }
        };

        async handle(c: Context<{ Bindings: Bindings }>) {
                const payload = c.get('jwtPayload');

                const data = await c.req.json();
                const { imageUrl, source, scene, refImageUrl } = data;

                // check the imageUrl is valid
                if (!imageUrl) {
                        return c.json({ error: 'imageUrl is required' }, 400);
                }

                // Check that either scene or refImageUrl is provided, but not both
                if (scene !== undefined && refImageUrl !== undefined) {
                        return c.json({ error: 'Cannot provide both scene and refImageUrl parameters' }, 400);
                }

                if (scene === undefined && refImageUrl === undefined) {
                        return c.json({ error: 'Either scene or refImageUrl must be provided' }, 400);
                }

                // double-check the request data
                console.log('imageUrl:', imageUrl);
                console.log('scene:', scene);
                console.log('refImageUrl:', refImageUrl);
                console.log('source:', source);

                const task: TaskInfoRequest = {
                        taskId: nanoid(),
                        app: APP.IMAGE_RELIGHTER,
                        uid: payload.uid as string,
                        accountId: payload.accountId as string,
                        status: TaskStatus.WAITING,
                        source: source,
                        params: { imageUrl, scene, refImageUrl }
                };

                try {
                        const taskResult = await doCreateTask(task, c.env);
                        return c.json({ taskId: taskResult?.taskId }, 200);
                } catch (error: any) {
                        console.error('Create image-relighter task failed:', error);
                        console.error('The detail error is:', {
                                message: error.message,
                                stack: error.stack,
                                name: error.name
                        });

                        if (error instanceof z.ZodError) {
                                return c.json({ error: error.errors[0].message }, 400);
                        }
                        return c.json({ error: 'Create image-relighter task failed.' }, 500);
                }
        }
}
