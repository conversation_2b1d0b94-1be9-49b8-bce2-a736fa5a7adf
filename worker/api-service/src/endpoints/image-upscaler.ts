import { <PERSON><PERSON><PERSON>oute, contentJson } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings, SOURCE, APP } from '../types';
import { doCreateTask } from '../service/task/create-task';
import { TaskInfoRequest, TaskStatus } from '../model/task';
import { nanoid } from 'nanoid';

export class ImageUpscaler extends OpenAPIRoute {
        schema = {
                summary: 'Image upscaler, submit Task',
                description: 'Submit image upscaler task',
                tags: ['api'],
                request: {
                        body: contentJson(
                                z.object({
                                        imageUrl: z.string().describe('Image URL'),
                                        source: z.nativeEnum(SOURCE).describe('Channel source, possible values: "api", "web", "framer", "figma", "canva", "rapidapi", "mcp"'),
                                        scale: z.number()
                                            .default(2)
                                            .refine(val => [2,3,4,8,16].includes(val), {
                                                message: "Scale factor must be 2,3,4,8,16"
                                            })
                                            .describe('Upscale factor, possible values: 2,3,4,8,16')
                                })
                        ),
                },
                response: {
                        200: contentJson({
                                taskId: z.string().describe('Task ID')
                        }),
                        400: contentJson({
                                error: z.string().describe('Request error message')
                        }),
                        401: contentJson({
                                error: z.string().describe('Authentication error message')
                        }),
                        500: contentJson({
                                error: z.string().describe('Internal server error message')
                        })
                }
        };

        async handle(c: Context<{ Bindings: Bindings }>) {
                const payload = c.get('jwtPayload');

                // 从请求中获取数据
                const data = await c.req.json();
                console.log('data', data);
                console.log('payload', payload);

                const { imageUrl, source, scale } = data;
                const task: TaskInfoRequest = {
                        taskId: nanoid(),
                        source,
                        uid: payload.uid as string,
                        app: APP.IMAGE_UPSCALER,
                        accountId: payload.accountId as string,
                        status: TaskStatus.WAITING,
                        params: { imageUrl, scale }
                }

                try {
                        const taskResult = await doCreateTask(task, c.env);
                        return c.json({ taskId: taskResult?.taskId }, 200);
                } catch (error: any) {
                        console.error('Create task failed:', error);
                        console.error('Error details:', {
                                message: error.message,
                                stack: error.stack,
                                name: error.name
                        });

                        if (error instanceof z.ZodError) {
                                return c.json({ error: error.errors[0].message }, 400);
                        }
                        return c.json({ error: 'Create task failed' }, 500);
                }
        }
}
