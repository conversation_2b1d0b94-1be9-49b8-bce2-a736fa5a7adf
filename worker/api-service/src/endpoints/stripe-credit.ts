import { Context } from 'hono';
import { OpenAPIRoute, contentJson } from 'chanfana';
import { z } from 'zod';
import { Bindings } from '../types';
import { doAddPayAsGoCredit, doResetPlanCredit } from '../service/credit/service';
import { CreditError } from '../service/credit/errors';

export class StripeAddPayAsGoCredit extends OpenAPIRoute {
	schema = {
		summary: 'Stripe webhook: Add user pay as go credit',
		description: 'Stripe webhook endpoint for adding pay as go credits',
		tags: ['stripe-credit'],
		request: {
			body: contentJson(
				z.object({
					accountId: z.string(),
					uid: z.string(),
					credits: z.number().min(1),
					stripeSignature: z.string(),
				}),
			),
		},
		response: {
			200: contentJson(
				z
					.object({
						success: z.boolean(),
						data: z.object({
							id: z.number().optional(),
							account_id: z.string(),
							pending_credits: z.number(),
							plan_credits: z.number(),
							pay_as_go_credits: z.number(),
							created_at: z.date(),
							updated_at: z.date(),
						}),
						code: z.nativeEnum(CreditError).optional(),
						message: z.string().optional(),
					})
					.describe('Successfully added credits'),
			),
			400: contentJson({
				error: z.string().describe('Request error message'),
			}),
			500: contentJson({
				error: z.string().describe('Internal server error message'),
			}),
		},
	};

	async handle(c: Context<{ Bindings: Bindings }>) {
		const { accountId, uid, credits, stripeSignature: _stripeSignature } = await c.req.json();
		console.log('StripeAddPayAsGoCredit', {
			accountId,
			uid,
			credits,
			stripeSignature: _stripeSignature,
		});

		// TODO: Add Stripe signature verification here
		// const isValidSignature = verifyStripeSignature(stripeSignature, rawBody);
		// if (!isValidSignature) {
		//     return c.json({ success: false, message: 'Invalid Stripe signature' }, 400);
		// }

		const result = await doAddPayAsGoCredit(c.env, {
			accountId,
			uid,
			credits,
		});

		if (!result.success) {
			return c.json(
				{
					success: false,
					code: result.code,
					message: result.message,
				},
				400,
			);
		}

		return c.json({
			success: true,
			data: result.data,
		});
	}
}

export class StripeResetPlanCredit extends OpenAPIRoute {
	schema = {
		summary: 'Stripe webhook: Reset user plan credit',
		description: 'Stripe webhook endpoint for resetting plan credits',
		tags: ['stripe-credit'],
		request: {
			body: contentJson(
				z.object({
					accountId: z.string(),
					uid: z.string(),
					credits: z.number().min(0),
					stripeSignature: z.string(),
				}),
			),
		},
		response: {
			200: contentJson(
				z
					.object({
						success: z.boolean(),
						data: z.object({
							id: z.number().optional(),
							account_id: z.string(),
							pending_credits: z.number(),
							plan_credits: z.number(),
							pay_as_go_credits: z.number(),
							created_at: z.date(),
							updated_at: z.date(),
						}),
						code: z.nativeEnum(CreditError).optional(),
						message: z.string().optional(),
					})
					.describe('Successfully reset credits'),
			),
			400: contentJson({
				error: z.string().describe('Request error message'),
			}),
			500: contentJson({
				error: z.string().describe('Internal server error message'),
			}),
		},
	};

	async handle(c: Context<{ Bindings: Bindings }>) {
		const { accountId, uid, credits, stripeSignature: _stripeSignature } = await c.req.json();

		// TODO: Add Stripe signature verification here
		// const isValidSignature = verifyStripeSignature(_stripeSignature, rawBody);
		// if (!isValidSignature) {
		//     return c.json({ success: false, message: 'Invalid Stripe signature' }, 400);
		// }
		console.log('StripeResetPlanCredit', {
			accountId,
			uid,
			credits,
		});

		const result = await doResetPlanCredit(c.env, {
			accountId,
			uid,
			credits,
		});

		if (!result.success) {
			return c.json(
				{
					success: false,
					code: result.code,
					message: result.message,
				},
				400,
			);
		}

		return c.json({
			success: true,
			data: result.data,
		});
	}
}
