import { OpenAPIRoute, contentJson } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings } from '../types';
import { getSceneEndpoints } from '../service/cms/endpoints';

// Default empty response when scene is not found
const EMPTY_RESPONSE = { data: [] };

export class GetAppTemplate extends OpenAPIRoute {
  schema = {
    summary: 'Get templates for a specific scene',
    description: 'Get template data based on scene parameter. All query parameters (except "scene") will be directly forwarded to the CMS endpoint, allowing you to use any filters, sorting, or pagination parameters supported by the CMS.',
    tags: ['templates'],
    request: {
      query: z.object({
        scene: z.string().describe('Scene identifier. Available scenes will be fetched dynamically.'),
      }).catchall(z.string().optional().describe('Any additional query parameters (e.g., limit, page, filter, sort) will be forwarded directly to the CMS endpoint'))
    },
    response: {
      200: contentJson(z.any().describe('Template data')),
      404: contentJson({
        success: z.boolean(),
        error: z.string().describe('Error message when scene is not found')
      }),
      500: contentJson({
        success: z.boolean(),
        error: z.object({
          message: z.string().describe('Error message'),
          code: z.string().describe('Error code')
        })
      })
    }
  };

  async handle(c: Context<{ Bindings: Bindings }>) {
    try {
      // Get the validated request data
      const data = await this.getValidatedData<typeof this.schema>();
      const { scene } = data.query;
      
      // Get the CMS endpoint for the requested scene
      const cmsEndpoint = scene ? await getSceneEndpoints(c.env, scene) : null;
      
      // If scene is not found, return empty response
      if (!cmsEndpoint) {
        console.log(`Scene not found: ${scene}`);
        return c.json(EMPTY_RESPONSE);
      }
      
      // Get all query parameters from the original request
      const url = new URL(c.req.url);
      const queryParams = url.searchParams;
      
      // Remove 'scene' from the parameters since it's used to select the endpoint
      queryParams.delete('scene');
      
      // Build the final CMS endpoint URL with query parameters
      let finalEndpoint = cmsEndpoint;
      const queryString = queryParams.toString();
      if (queryString) {
        finalEndpoint = `${cmsEndpoint}?${queryString}`;
      }
      
      console.log(`Fetching templates from: ${finalEndpoint}`);
      
      // Fetch template data from CMS
      const response = await fetch(finalEndpoint);
      
      if (!response.ok) {
        // If CMS request fails, return empty object instead of error
        console.error(`Failed to fetch from CMS: ${response.status} ${response.statusText}`);
        return c.json(EMPTY_RESPONSE);
      }

      // Get template data from CMS response
      const templateData = await response.json() as Record<string, unknown>;
      
      // Return data
      return c.json(templateData);
    } catch (error: any) {
      console.error('Get app template failed:', error);

      // Return empty object even if an error occurs
      return c.json(EMPTY_RESPONSE);
    }
  }
}