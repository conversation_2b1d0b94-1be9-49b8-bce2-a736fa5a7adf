import { OpenAPIRoute } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings } from '../types';
import { doGetUserTasks } from '../service/supabase/task';

export class GetUserTasks extends OpenAPIRoute {
    schema = {
        summary: 'get user tasks',
        description: 'Get user tasks with pagination and filtering options. Date format should be YYYY-MM-DD',
        tags: ['task'],
        request: {
            query: z.object({
                page: z.string().optional().default('1').describe('Page number'),
                pageSize: z.string().optional().default('10').describe('Number of items per page'),
                app: z.string().optional().describe('Filter by app type'),
                status: z.string().optional().describe('Filter by task status'),
                startDate: z.string().optional().describe('Start date in YYYY-MM-DD format'),
                endDate: z.string().optional().describe('End date in YYYY-MM-DD format'),
            }),
        },
        responses: {
            200: {
                description: 'Success',
                content: {
                    'application/json': {
                        schema: z.object({
                            success: z.boolean(),
                            data: z.object({
                                tasks: z.array(z.any()),
                                total: z.number(),
                                page: z.number(),
                                pageSize: z.number(),
                                totalPages: z.number()
                            })
                        })
                    }
                }
            },
            400: {
                description: 'Bad Request - Invalid date format',
                content: {
                    'application/json': {
                        schema: z.object({
                            success: z.boolean(),
                            error: z.string()
                        })
                    }
                }
            }
        }
    };

    async handle(c: Context<{ Bindings: Bindings }>) {
        const data = await this.getValidatedData<typeof this.schema>();
        const jwtPayload = c.get('jwtPayload');
        const uid = jwtPayload?.uid;

        if (!uid) {
            return c.json({
                success: false,
                error: 'Unauthorized'
            }, 401);
        }

        const page = parseInt(data.query.page, 10);
        const pageSize = parseInt(data.query.pageSize, 10);
        const { app, status, startDate, endDate } = data.query;

        // Validate date format if provided
        if (startDate && !/^\d{4}-\d{2}-\d{2}$/.test(startDate)) {
            return c.json({
                success: false,
                error: 'Invalid startDate format. Use YYYY-MM-DD format'
            }, 400);
        }

        if (endDate && !/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
            return c.json({
                success: false,
                error: 'Invalid endDate format. Use YYYY-MM-DD format'
            }, 400);
        }

        try {
            const result = await doGetUserTasks(c.env, {
                uid,
                page,
                pageSize,
                app,
                status,
                startDate,
                endDate
            });

            return c.json({
                success: true,
                data: {
                    tasks: result.tasks,
                    total: result.total,
                    page: result.page,
                    pageSize: result.pageSize,
                    totalPages: result.totalPages
                }
            });
        } catch (error) {
            console.error('Error fetching user tasks:', error);
            return c.json({
                success: false,
                error: 'Failed to fetch user tasks'
            }, 500);
        }
    }
} 