import { <PERSON>AP<PERSON>oute, contentJson } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings, SOURCE, APP, VIDEO_UPSCALER_MODEL, VIDEO_QUALITY } from '../types';
import { doCreateTask } from '../service/task/create-task';
import { TaskInfoRequest, TaskStatus } from '../model/task';
import { nanoid } from 'nanoid';

export class VideoUpscaler extends OpenAPIRoute {
        schema = {
                summary: 'Video upscaler, submit Task',
                description: 'Submit video upscaler task',
                tags: ['api'],
                request: {
                        body: contentJson(
                                z.object({
                                        enableUpscale: z.boolean().optional().describe('Enable upscaling'),
                                        mimeType: z.string().optional().describe('Video MIME type, defaults to video/mp4'),
                                        model: z.string().optional().describe('Model'),
                                        videoQuality: z.string().optional().describe('Video quality'),
                                        videoUrl: z.string().describe('Video URL'),
                                        source: z.nativeEnum(SOURCE).describe('Channel source, possible values: "api", "web", "framer", "figma", "canva", "rapidapi", "mcp"')
                                })
                        )
                },
                response: {
                        200: contentJson({
                                taskId: z.string().describe('Task ID')
                        }),
                        400: contentJson({
                                error: z.string().describe('Request error message')
                        }),
                        401: contentJson({
                                error: z.string().describe('Authentication error message')
                        }),
                        500: contentJson({
                                error: z.string().describe('Internal server error message')
                        })
                }
        };

        async handle(c: Context<{ Bindings: Bindings }>) {
                const payload = c.get('jwtPayload');

                const data = await c.req.json();
                console.log('data', data);

                let { enableUpscale, mimeType, model, videoQuality, videoUrl, source } = data;

                // set default mimeType
                if (!mimeType) {
                        mimeType = 'video/mp4';
                }

                // verify model
                if (!model) {
                        model = VIDEO_UPSCALER_MODEL.GENERAL;
                } else if (!Object.values(VIDEO_UPSCALER_MODEL).includes(model as VIDEO_UPSCALER_MODEL)) {
                        return c.json({ error: `Invalid model value. Must be one of: ${Object.values(VIDEO_UPSCALER_MODEL).join(', ')}` }, 400);
                }
                // verify videoQuality
                if (!videoQuality) {
                        videoQuality = VIDEO_QUALITY['720P'];
                } else if (!Object.values(VIDEO_QUALITY).includes(videoQuality as VIDEO_QUALITY)) {
                        return c.json({ error: `Invalid videoQuality value. Must be one of: ${Object.values(VIDEO_QUALITY).join(', ')}` }, 400);
                }

                // verify mimeType - 默认设置为 mp4 格式，目前只支持 mp4
                if (!mimeType) {
                        mimeType = 'video/mp4';
                } else {
                        // 目前只支持 mp4 格式
                        if (mimeType.toLowerCase() !== 'video/mp4') {
                                return c.json({ error: 'Currently only video/mp4 format is supported' }, 400);
                        }
                }

                const task: TaskInfoRequest = {
                        taskId: nanoid(),
                        source,
                        uid: payload.uid as string,
                        app: APP.VIDEO_UPSCALER,
                        status: TaskStatus.WAITING,
                        params: { enableUpscale: enableUpscale || false, mimeType, model, videoQuality, videoUrl },
                        accountId: payload.accountId as string
                }

                try {
                        const taskResult = await doCreateTask(task, c.env);
                        return c.json({ taskId: taskResult?.taskId }, 200);
                } catch (error: any) {
                        console.error('Create task failed:', error);
                        console.error('Error details:', {
                                message: error.message,
                                stack: error.stack,
                                name: error.name
                        });

                        if (error instanceof z.ZodError) {
                                return c.json({ error: error.errors[0].message }, 400);
                        }
                        return c.json({ error: 'Create task failed' }, 500);
                }
        }
}
