import { OpenAPIRoute, contentJson } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings } from '../types';
import { doAnalyzeTasksByDateRange } from '../service/task/task-analysis';

export class TaskDataAnalysis extends OpenAPIRoute {
    schema = {
        summary: '分析任务数据',
        description: '分析指定日期范围内的任务数据统计。如果不提供日期，则默认分析昨天的数据。包含 startDate，不包含 endDate',
        tags: ['admin'],
        request: {
            body: contentJson(
                z.object({
                    startDate: z.string()
                        .regex(/^\d{4}-\d{2}-\d{2}$/)
                        .describe('开始日期，格式：YYYY-MM-DD')
                        .optional(),
                    endDate: z.string()
                        .regex(/^\d{4}-\d{2}-\d{2}$/)
                        .describe('结束日期，格式：YYYY-MM-DD')
                        .optional()
                })
            )
        },
        response: {
            200: contentJson({
                success: z.boolean()
            }),
            400: contentJson({
                success: z.boolean(),
                error: z.string().describe('Request error message')
            }),
            500: contentJson({
                success: z.boolean(),
                error: z.string().describe('Internal server error message')
            })
        }
    };

    async handle(c: Context<{ Bindings: Bindings }>) {
        try {
            const body = await c.req.json();
            const { startDate, endDate } = z.object({
                startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
                endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional()
            }).parse(body);

            // 如果同时提供了开始和结束日期，验证开始日期是否早于结束日期
            if (startDate && endDate && startDate > endDate) {
                return c.json({
                    success: false,
                    error: `开始日期 (${startDate}) 不能晚于结束日期 (${endDate})`
                }, 400);
            }

            await doAnalyzeTasksByDateRange(c.env, startDate, endDate);
            
            return c.json({
                success: true
            });
        } catch (error: any) {
            console.error('Task data analysis failed:', error);
            console.error('Error details:', {
                message: error.message,
                stack: error.stack,
                name: error.name
            });

            if (error instanceof z.ZodError) {
                return c.json({ 
                    success: false, 
                    error: '日期格式无效，请使用 YYYY-MM-DD 格式' 
                }, 400);
            }

            return c.json({ 
                success: false, 
                error: '分析任务数据失败' 
            }, 500);
        }
    }
}
