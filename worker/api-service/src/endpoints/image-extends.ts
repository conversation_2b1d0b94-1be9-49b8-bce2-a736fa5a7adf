import { <PERSON><PERSON><PERSON>oute, contentJson } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings, SOURCE, APP } from '../types';
import { doCreateTask } from '../service/task/create-task';
import { TaskInfoRequest, TaskStatus } from '../model/task';
import { nanoid } from 'nanoid';

// Ideogram V3 Reframe 支持的图片尺寸
const ImageSizeEnum = z.enum(['square_hd', 'square', 'portrait_4_3', 'portrait_16_9', 'landscape_4_3', 'landscape_16_9']);

const CustomImageSize = z.object({
	width: z.number().min(512).max(2048).describe('Image width (512-2048)'),
	height: z.number().min(512).max(2048).describe('Image height (512-2048)')
});

export class ImageExtends extends OpenAPIRoute {
	schema = {
		summary: 'Image Reframe with Ideogram V3, submit Task',
		description: 'Submit image reframe task using Ideogram V3, supports extension and recomposition',
		tags: ['api'],
		request: {
			body: contentJson(
				z.object({
					imageUrl: z.string().url().describe('Original image URL'),
					imageSize: z.union([ImageSizeEnum, CustomImageSize]).default('square_hd').describe('Output image size, can be preset size or custom size object'),
					renderingSpeed: z.enum(['TURBO', 'BALANCED', 'QUALITY']).default('BALANCED').describe('Rendering speed'),
					numImages: z.number().min(1).max(4).default(1).describe('Number of images to generate'),
					seed: z.number().optional().describe('Random seed for reproducible results'),
					source: z.nativeEnum(SOURCE).describe('Channel source, possible values: "api", "web", "framer", "figma", "canva", "rapidapi", "mcp"')
				})
			)
		},
		response: {
			200: contentJson({
				taskId: z.string().describe('Task ID')
			}),
			400: contentJson({
				error: z.string().describe('Request error message')
			}),
			401: contentJson({
				error: z.string().describe('Authentication error message')
			}),
			500: contentJson({
				error: z.string().describe('Internal server error message')
			})
		}
	};

	async handle(c: Context<{ Bindings: Bindings }>) {
		const payload = c.get('jwtPayload');
		const data = await c.req.json();
		console.log('Image reframe request data:', data);

		const {
			imageUrl,
			imageSize,
			renderingSpeed,
			numImages,
			seed,
			source
		} = data;

		// 构建任务参数
		const taskParams: Record<string, unknown> = {
			imageUrl,
			imageSize: imageSize || 'square_hd',
			renderingSpeed: renderingSpeed || 'BALANCED',
			numImages: numImages || 1
		};

		// 添加可选参数
		if (seed) {
			taskParams.seed = seed;
		}

		// 处理自定义尺寸
		if (typeof imageSize === 'object' && imageSize.width && imageSize.height) {
			taskParams.width = imageSize.width;
			taskParams.height = imageSize.height;
			taskParams.imageSize = 'custom';
		}



		const task: TaskInfoRequest = {
			taskId: nanoid(),
			source,
			uid: payload.uid as string,
			app: APP.IMAGE_EXTENDS,
			status: TaskStatus.WAITING,
			params: taskParams,
			accountId: payload.accountId as string
		};

		try {
			const taskResult = await doCreateTask(task, c.env);
			return c.json({ taskId: taskResult?.taskId }, 200);
		} catch (error: any) {
			console.error('Create image reframe task failed:', error);
			console.error('Error details:', {
				message: error.message,
				stack: error.stack,
				name: error.name
			});

			if (error instanceof z.ZodError) {
				return c.json({ error: error.errors[0].message }, 400);
			}
			return c.json({ error: 'Create image reframe task failed' }, 500);
		}
	}
}