import { contentJson, OpenAPIRoute } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';

import { APP, Bindings, R2_PREFIX } from '../types';
import { nanoid } from 'nanoid';
import { UrlProcessor } from '../utils/url-processor';

export class FileUploadBase64 extends OpenAPIRoute {
	schema = {
		summary: '将Base64图片上传R2',
		description: '上传Base64编码的图片到R2，并获得下载链接。',
		tags: ['file-upload'],
		request: {
			body: {
				content: {
					'application/json': {
						schema: z.object({
							base64Data: z.string().min(1),
							fileName: z.string().regex(/\.(jpg|jpeg|png|gif|bmp|webp)$/i),
							app: z.nativeEnum(APP).describe('Application identifier'),
							mimeType: z.string().default('image/jpeg'),
						}),
					},
				},
			},
		},
		response: {
			'200': {
				description: '下载链接',
				...contentJson({
					url: z.string().url(),
				}),
			},
		},
	};

	async handle(c: Context<{ Bindings: Bindings }>) {
		const prefix = R2_PREFIX;
		const body = await c.req.json();

		if (!body) {
			return c.json({ error: 'no body' }, 400);
		}

		const { base64Data, fileName, app, mimeType } = body;

		// 验证base64数据
		if (!base64Data.includes('base64,')) {
			return c.json({ error: 'invalid base64 data' }, 400);
		}

		// 提取实际的base64内容
		const base64Content = base64Data.split('base64,')[1];

		try {
			// 解码base64数据
			const binaryData = Buffer.from(base64Content, 'base64');
			const pathname = `${prefix}/${app}/${nanoid()}_${Date.now()}_${fileName}`;

			// 设置文件类型
			const headers = new Headers();
			headers.set('content-type', mimeType);

			// 上传到R2
			await c.env.MY_BUCKET.put(pathname, binaryData, { httpMetadata: headers });

			// 获取公开访问URL
			const r2_url = UrlProcessor.buildR2Url(c.env.R2_PUBLIC_DOMAIN, pathname);

			return c.json({ url: r2_url });
		} catch (e: any) {
			return c.json({ error: e.message }, 400);
		}
	}
}
