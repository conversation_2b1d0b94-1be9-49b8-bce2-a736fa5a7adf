import { OpenAPIRoute, contentJson } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings, SOURCE } from '../types';
import { doCalculateCredits } from '../service/credit/calculate-credits';

// 基础任务 schema - 所有任务都有的共同字段
const BaseTaskSchema = z.object({
    app: z.string().describe('App name'),
    source: z.nativeEnum(SOURCE).optional().describe('Source channel, defaults to "web"'),
});

// 图片放大器任务参数
const ImageUpscalerTaskSchema = BaseTaskSchema.extend({
    app: z.literal('iu').describe('Image upscaler app'),
    scale: z.number().describe('Scale parameter for image upscaler (2, 4, 8, 16)'),
});

// 速绘任务参数
const SpeedpainterTaskSchema = BaseTaskSchema.extend({
    app: z.literal('sp').describe('Speedpainter app'),
    sketchDuration: z.number().optional().describe('Sketch duration in seconds (default: 3)'),
    colorFillDuration: z.number().optional().describe('Color fill duration in seconds (default: 3)'),
});

// 视频放大器任务参数
const VideoUpscalerTaskSchema = BaseTaskSchema.extend({
    app: z.literal('vu').describe('Video upscaler app'),
    duration: z.number().optional().describe('Video duration in seconds'),
    quality: z.string().optional().describe('Video quality (720P, 1080P, 4K)'),
});

// 移除背景任务参数
const RemoveBgTaskSchema = BaseTaskSchema.extend({
    app: z.literal('remove-bg').describe('Remove background app'),
});

// 图片矢量化任务参数
const ImageVectorizationTaskSchema = BaseTaskSchema.extend({
    app: z.literal('image-vectorization').describe('Image vectorization app'),
});

// 图片扩展任务参数
const ImageExtendsTaskSchema = BaseTaskSchema.extend({
    app: z.literal('image-extends').describe('Image extends app'),
});

// 文字后置任务参数
const TextBehindImageTaskSchema = BaseTaskSchema.extend({
    app: z.literal('text-behind-image').describe('Text behind image app'),
});

// 图片生成器任务参数
const ImageGeneratorTaskSchema = BaseTaskSchema.extend({
    app: z.literal('image-generator').describe('Image generator app'),
    prompt: z.string().optional().describe('Generation prompt'),
});
// 302 AI任务参数
const AI302TaskSchema = BaseTaskSchema.extend({
    app: z.literal('302').describe('302 AI app'),
    path: z.string().describe('API path for pricing query'),
});

// Chat-2-Design 任务参数
const Chat2DesignTaskSchema = BaseTaskSchema.extend({
    app: z.literal('chat-2-design').describe('Chat 2 Design app'),
    prompt: z.string().describe('Chat prompt'),
    imageUrls: z.array(z.string()).optional().describe('Input image URLs'),
});

// 使用 discriminated union 根据 app 类型来区分不同的参数
const TaskItemSchema = z.discriminatedUnion('app', [
    ImageUpscalerTaskSchema,
    SpeedpainterTaskSchema,
    VideoUpscalerTaskSchema,
    RemoveBgTaskSchema,
    ImageVectorizationTaskSchema,
    ImageExtendsTaskSchema,
    TextBehindImageTaskSchema,
    ImageGeneratorTaskSchema,
    Chat2DesignTaskSchema,
    AI302TaskSchema,
]);

// 请求body schema
const CalculateCreditsRequestSchema = z.object({
    data: z.array(TaskItemSchema).min(1).describe('Array of tasks to calculate credits for')
});

export class CalculateCredits extends OpenAPIRoute {
    schema = {
        summary: 'Calculate Credits for Tasks',
        description: 'Calculate the total credits needed for a batch of tasks based on their parameters. Different app types require different parameters.',
        tags: ['credit'],
        request: {
            body: contentJson(CalculateCreditsRequestSchema)
        },
        responses: {
            200: {
                description: 'Credits calculation successful',
                content: {
                    'application/json': {
                        schema: z.object({
                            needCredits: z.number().describe('Total credits needed for all tasks')
                        })
                    }
                }
            },
            400: {
                description: 'Bad Request - Invalid input data',
                content: {
                    'application/json': {
                        schema: z.object({
                            error: z.string().describe('Error message')
                        })
                    }
                }
            },
            500: {
                description: 'Internal Server Error',
                content: {
                    'application/json': {
                        schema: z.object({
                            error: z.string().describe('Error message')
                        })
                    }
                }
            }
        }
    };

    async handle(c: Context<{ Bindings: Bindings }>) {
        try {
            const requestData = await c.req.json();
            console.log('Calculate credits request:', requestData);

            // 验证请求数据
            const validationResult = CalculateCreditsRequestSchema.safeParse(requestData);
            if (!validationResult.success) {
                return c.json({
                    error: `Invalid request data: ${validationResult.error.errors.map(e => e.message).join(', ')}`
                }, 400);
            }

            const { data: tasks } = validationResult.data;

            // 计算总credits - 使用服务层，如果有环境变量则查询数据库，否则使用离线计算
            const totalCredits = await doCalculateCredits(tasks as any[], c.env);

            return c.json({
                needCredits: totalCredits
            });

        } catch (error: any) {
            console.error('Calculate credits failed:', error);
            console.error('Error details:', {
                message: error.message,
                stack: error.stack,
                name: error.name
            });

            const errorMessage = error.message || 'Calculate credits failed';
            return c.json({ error: errorMessage }, 500);
        }
    }
} 