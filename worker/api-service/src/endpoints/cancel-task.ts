import { OpenAPIRoute } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings } from '../types';
import { TaskService } from '../service/task/task-service';

export class CancelTask extends OpenAPIRoute {
    schema = {
        summary: 'Cancel task',
        description: 'Cancel a task in progress, if task is completed return task result',
        tags: ['task'],
        request: {
            params: z.object({
                taskId: z.string(),
            }),
        },
    };

    async handle(c: Context<{ Bindings: Bindings }>) {
        const data = await this.getValidatedData<typeof this.schema>();
        const taskId = data.params.taskId;
        const jwtPayload = c.get('jwtPayload');
        const accountId = jwtPayload?.accountId;

        const result = await TaskService.cancelTask(c.env, { taskId, accountId });

        if (!result.success) {
            const statusCode = result.code === 'TASK_NOT_FOUND' ? 404 : 500;
            return c.json({ success: false, error: result.code }, statusCode);
        }

        return c.json({
            success: true,
            message: result.data.message,
            data: result.data.data
        });
    }
} 