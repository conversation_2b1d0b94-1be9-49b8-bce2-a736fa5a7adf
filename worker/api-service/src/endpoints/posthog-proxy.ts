import { <PERSON>AP<PERSON>oute } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings } from '../types';

export class <PERSON>hogProxy extends OpenAPIRoute {
    schema = {
        summary: 'PostHog Analytics Proxy',
        description: 'Proxies requests to PostHog analytics service',
        tags: ['proxy'],
        request: {
            params: z.object({
                '*': z.string().optional().describe('Path to proxy to PostHog')
            })
        },
        responses: {
            200: {
                description: 'Proxied response from PostHog',
                content: {
                    '*/*': {
                        schema: z.any()
                    }
                }
            },
            500: {
                description: 'Server error',
                content: {
                    'application/json': {
                        schema: z.object({
                            success: z.boolean(),
                            error: z.string()
                        })
                    }
                }
            }
        }
    };

    async handle(c: Context<{ Bindings: Bindings }>) {
        try {
            const url = new URL(c.req.url);
            // Remove /ph prefix from pathname
            const pathname = url.pathname.replace(/^\/ph/, '') || '/';
            const search = url.search;
            const pathWithParams = pathname + search;

            // Use environment variables with fallback to US region
            const API_HOST = c.env.POSTHOG_API_HOST || 'us.i.posthog.com';
            const ASSET_HOST = c.env.POSTHOG_ASSET_HOST || 'us-assets.i.posthog.com';

            if (pathname.startsWith('/static/')) {
                return await this.retrieveStatic(c.req.raw, pathWithParams, ASSET_HOST, c);
            } else {
                return await this.forwardRequest(c.req.raw, pathWithParams, API_HOST);
            }
        } catch (error) {
            console.error('PostHog proxy error:', error);
            return c.json({
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred'
            }, 500);
        }
    }

    private async retrieveStatic(
        request: Request,
        pathname: string,
        assetHost: string,
        c: Context<{ Bindings: Bindings }>
    ): Promise<Response> {
        let response = await caches.default.match(request);
        
        if (!response) {
            response = await fetch(`https://${assetHost}${pathname}`);
            c.executionCtx.waitUntil(caches.default.put(request, response.clone()));
        }
        
        return response;
    }

    private async forwardRequest(
        request: Request,
        pathWithSearch: string,
        apiHost: string
    ): Promise<Response> {
        // Create new headers without cookie
        const headers = new Headers(request.headers);
        headers.delete('cookie');
        
        // Create new request with modified headers
        const originRequest = new Request(`https://${apiHost}${pathWithSearch}`, {
            method: request.method,
            headers: headers,
            body: request.body,
            redirect: 'manual'
        });
        
        return await fetch(originRequest);
    }
}