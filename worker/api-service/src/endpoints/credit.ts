// import { doGetUserCredit, doInsertUserCredit } from '../service/supabase/credit';
import { Context } from 'hono';
import { OpenAPIRoute, contentJson } from 'chanfana';
import { z } from 'zod';
import { Bindings, APP, SOURCE } from '../types';

import { doGetOrCreateUserCreditRecord } from '../service/credit/service';
import { CreditError } from '../service/credit/errors';
import { doGetCreditRule, CreditRule } from '../service/supabase/credit-rules';

// Combined schema for query parameters (for GetAppCredit)
const AppCreditQuerySchema = z.object({
        app: z.nativeEnum(APP).optional().describe('The name of the application'), // app is now optional
        source: z.nativeEnum(SOURCE).optional().describe('The source of the application request'),
        path: z.string().optional().describe('Optional path parameter'), // Added optional path parameter
});

// Define the success response schema explicitly for better type inference (for GetAppCredit)
const SuccessResponseSchema = z.object({
        success: z.literal(true),
        // Allow any data for the proxy pass-through case, including arrays for all credit rules
        data: z.union([z.custom<CreditRule>(), z.array(z.custom<CreditRule>()), z.null(), z.any()]),
});

// Define a generic error response schema (for GetAppCredit)
const ErrorResponseSchema = z.object({
        success: z.literal(false),
        error: z.object({
                message: z.string(),
        }),
});

export class GetCredit extends OpenAPIRoute {
        schema = {
                summary: '获取 user credit',
                description: '获取 user credit',
                tags: ['credit'],
                response: {
                        200: contentJson(
                                z.object({
                                        success: z.boolean(),
                                        data: z.object({
                                                id: z.number().optional(),
                                                account_id: z.string(),
                                                pending_credits: z.number(),
                                                plan_credits: z.number(),
                                                pay_as_go_credits: z.number(),
                                                created_at: z.string().datetime(),
                                                updated_at: z.string().datetime()

                                        }),
                                        code: z.nativeEnum(CreditError).optional(),
                                        message: z.string().optional()

                                }).describe('用户的积分信息')
                        ),
                        400: contentJson({
                                error: z.string().describe('Request error message')
                        }),
                        401: contentJson({
                                error: z.string().describe('Authentication error message')
                        }),
                        500: contentJson({
                                error: z.string().describe('Internal server error message')
                        })
                }
        };

        async handle(c: Context<{ Bindings: Bindings }>) {
                try {
                        const uid = c.get('jwtPayload').uid;
                        const accountId = c.get('jwtPayload').accountId;

                        return await doGetOrCreateUserCreditRecord(c.env, accountId, uid);
                } catch (error: any) {
                        console.error('Tok:', error);
                        if (error instanceof z.ZodError) {
                                return c.json({ error: error.errors[0].message }, 400);
                        }
                        const errorMessage = error.message || 'Get User Credit failed';
                        return c.json({ error: errorMessage }, 500);
                }
        }
}

export class GetAppCredit extends OpenAPIRoute {
        schema = {
                summary: 'Get Credit Cost for an App Model',
                description: 'Retrieves the credit cost information for a specific application model, optionally filtered by source and path. If no app is provided, returns all credit rules. All parameters are via query string.',
                tags: ['credit'],
                request: {
                        query: AppCreditQuerySchema, // app, source, and path are now in query
                },
                responses: {
                        200: {
                                description: 'Credit rule for the specified app and source, or all credit rules if no app specified.',
                                content: {
                                        'application/json': {
                                                schema: SuccessResponseSchema,
                                        },
                                },
                        },
                        400: {
                                description: 'Bad Request - Invalid app, source, or path format',
                                content: {
                                        'application/json': {
                                                schema: ErrorResponseSchema,
                                        },
                                },
                        },
                        404: {
                                description: 'Not Found - No credit rule found for the specified app and source',
                                content: {
                                        'application/json': {
                                                schema: ErrorResponseSchema,
                                        },
                                },
                        },
                        500: {
                                description: 'Internal Server Error',
                                content: {
                                        'application/json': {
                                                schema: ErrorResponseSchema,
                                        },
                                },
                        },
                },
        };

        async handle(c: Context<{ Bindings: Bindings }>) {
                const query = c.req.query() as z.infer<typeof AppCreditQuerySchema>;
                const { app, source, path } = query;

                try {
                        const creditRule = await doGetCreditRule(c.env, app, source);

                        if (!creditRule) {
                                return c.json({ success: false, error: { message: 'Credit rule not found' } } as z.infer<typeof ErrorResponseSchema>, 404);
                        }

                        return c.json({ success: true, data: creditRule } as z.infer<typeof SuccessResponseSchema>);
                } catch (e: any) {
                        console.error(`Error fetching credit rule for app ${app}, source ${source}, path ${path}:`, e);
                        return c.json(
                                {
                                        success: false,
                                        error: { message: e.message || 'Failed to retrieve credit rule' },
                                } as z.infer<typeof ErrorResponseSchema>,
                                500
                        );
                }
        }
}
