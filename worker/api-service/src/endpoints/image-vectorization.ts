import { <PERSON>APIRoute, content<PERSON>son } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings, SOURCE, APP } from '../types';
import { doCreateTask } from '../service/task/create-task';
import { TaskInfoRequest, TaskStatus } from '../model/task';
import { nanoid } from 'nanoid';

export class ImageVectorization extends OpenAPIRoute {
        schema = {
                summary: 'Image vectorization, submit Task',
                description: 'Submit image vectorization task using fal.ai image2svg API',
                tags: ['api'],
                request: {
                        body: contentJson(
                                z.object({
                                        imageUrl: z.string().describe('Image URL to convert to SVG'),
                                        options: z.object({
                                                colormode: z.enum(['color', 'binary']).describe('Choose between color or binary (black and white) output').default('color').optional(),
                                                hierarchical: z.enum(['stacked', 'cutout']).describe('Hierarchical mode: stacked or cutout').default('stacked').optional(),
                                                mode: z.enum(['spline', 'polygon']).describe('Mode: spline (curved) or polygon (straight lines)').default('spline').optional(),
                                                filter_speckle: z.number().int().min(0).max(100).describe('Filter out small speckles and noise').default(4).optional(),
                                                color_precision: z.number().int().min(1).max(20).describe('Color quantization level').default(6).optional(),
                                                layer_difference: z.number().int().min(1).max(100).describe('Layer difference threshold for hierarchical mode').default(16).optional(),
                                                corner_threshold: z.number().int().min(0).max(180).describe('Corner detection threshold in degrees').default(60).optional(),
                                                length_threshold: z.number().min(0).max(100).describe('Length threshold for curves/lines').default(4).optional(),
                                                max_iterations: z.number().int().min(1).max(50).describe('Maximum number of iterations for optimization').default(10).optional(),
                                                splice_threshold: z.number().int().min(0).max(180).describe('Splice threshold for joining paths').default(45).optional(),
                                                path_precision: z.number().int().min(0).max(10).describe('Decimal precision for path coordinates').default(3).optional(),
                                        }).describe('Image vectorization configuration options').optional(),
                                        source: z.nativeEnum(SOURCE).describe('Channel source, possible values: "api", "web", "framer", "figma", "canva", "rapidapi", "mcp"')
                                })
                        ),
                },
                response: {
                        200: contentJson({
                                taskId: z.string().describe('Task ID')
                        }),
                        400: contentJson({
                                error: z.string().describe('Request error message')
                        }),
                        401: contentJson({
                                error: z.string().describe('Authentication error message')
                        }),
                        500: contentJson({
                                error: z.string().describe('Internal server error message')
                        })
                }
        };

        async handle(c: Context<{ Bindings: Bindings }>) {
                const payload = c.get('jwtPayload');

                const data = await c.req.json();

                const { imageUrl, source, options } = data;
                
                // 构建任务参数，包含所有 fal.ai 支持的选项
                const taskParams: Record<string, unknown> = { imageUrl };
                
                if (options) {
                        // 将所有选项添加到任务参数中
                        if (options.colormode) taskParams.colormode = options.colormode;
                        if (options.hierarchical) taskParams.hierarchical = options.hierarchical;
                        if (options.mode) taskParams.mode = options.mode;
                        if (options.filter_speckle !== undefined) taskParams.filter_speckle = options.filter_speckle;
                        if (options.color_precision !== undefined) taskParams.color_precision = options.color_precision;
                        if (options.layer_difference !== undefined) taskParams.layer_difference = options.layer_difference;
                        if (options.corner_threshold !== undefined) taskParams.corner_threshold = options.corner_threshold;
                        if (options.length_threshold !== undefined) taskParams.length_threshold = options.length_threshold;
                        if (options.max_iterations !== undefined) taskParams.max_iterations = options.max_iterations;
                        if (options.splice_threshold !== undefined) taskParams.splice_threshold = options.splice_threshold;
                        if (options.path_precision !== undefined) taskParams.path_precision = options.path_precision;
                }

                const task: TaskInfoRequest = {
                        taskId: nanoid(),
                        source,
                        uid: payload.uid as string,
                        app: APP.IMAGE_VECTORIZATION,
                        accountId: payload.accountId as string,
                        status: TaskStatus.WAITING,
                        params: taskParams
                }

                try {
                        const taskResult = await doCreateTask(task, c.env);
                        return c.json({ taskId: taskResult?.taskId }, 200);
                } catch (error: any) {
                        console.error('Create task failed:', error);
                        console.error('Error details:', {
                                message: error.message,
                                stack: error.stack,
                                name: error.name
                        });

                        if (error instanceof z.ZodError) {
                                return c.json({ error: error.errors[0].message }, 400);
                        }
                        return c.json({ error: 'Create task failed' }, 500);
                }
        }
}