import { OpenAPIRoute, contentJson } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings } from '../types';
import { doExchangeToken } from '../../../auth-service/src/service/exchange-token';

export class ExchangeToken extends OpenAPIRoute {
        schema = {
                summary: '获取A1D 统一下发的 token xxx',
                description: '更换不同的渠道来源的 token 为 A1D token, source 目前只支持 web',
                tags: ['exchange-token'],
                request: {
                        body: contentJson(
                                z.object({
                                        token: z.string().describe('渠道的 token')
                                })
                        ),
                },
                response: {
                        200: contentJson({
                                token: z.string().describe('A1D 签发的新 token')
                        }),
                        400: contentJson({
                                error: z.string().describe('Request error message')
                        }),
                        401: contentJson({
                                error: z.string().describe('Authentication error message')
                        }),
                        500: contentJson({
                                error: z.string().describe('Internal server error message')
                        })
                }
        };

        async handle(c: Context<{ Bindings: Bindings }>) {
                try {
                        const data = await this.getValidatedData<typeof this.schema>();
                        console.log('Validated data:', data);
                        const { token } = data.body;
                        let newToken = '';
                        // 只是为了本地调试的时候可以方便一些
                        if (c.env.ENV === 'dev') {
                                newToken = await doExchangeToken(token, c.env);
                        } else {
                                newToken = await c.env.AUTH_SERVICE.doExchangeToken(token);
                        }
                        return c.json({ token: newToken });
                } catch (error: any) {
                        console.error('Token exchange failed:', error);
                        console.error('Error details:', {
                                message: error.message,
                                stack: error.stack,
                                name: error.name
                        });

                        if (error instanceof z.ZodError) {
                                return c.json({ error: error.errors[0].message }, 400);
                        }
                        return c.json({ error: 'Token exchange failed' }, 500);
                }
        }
}
