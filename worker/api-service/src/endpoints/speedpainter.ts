import { <PERSON>APIRoute, contentJson } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings, SOURCE, APP } from '../types';
import { doCreateTask } from '../service/task/create-task';
import { TaskInfoRequest, TaskStatus } from '../model/task';
import { nanoid } from 'nanoid';

export class SpeedPainter extends OpenAPIRoute {
        schema = {
                summary: 'Speed painter, submit Task',
                description: 'Submit speed painter task',
                tags: ['api'],
                request: {
                        body: contentJson(
                                z.object({
                                        imageUrl: z.string().describe('Image URL'),
                                        mimeType: z.string().default('image/jpg'),
                                        sketchDuration: z.number().default(3),
                                        source: z.nativeEnum(SOURCE).describe('Channel source, possible values: "api", "web", "framer"'),
                                        colorFillDuration: z.number().default(3),
                                        needCanvas: z.boolean().default(false).optional(),
                                        canvasTitle: z.string().default('').describe('Canvas title').optional(),
                                        needHand: z.boolean().default(false).optional(),
                                        handTitle: z.string().default('').describe('Hand model title').optional(),
                                        needFadeout: z.boolean().default(false).optional(),
                                        fps: z.number().default(30).optional(),
                                })
                        ),
                },
                response: {
                        200: contentJson({
                                taskId: z.string().describe('Task ID')
                        }),
                        400: contentJson({
                                error: z.string().describe('Request error message')
                        }),
                        401: contentJson({
                                error: z.string().describe('Authentication error message')
                        }),
                        500: contentJson({
                                error: z.string().describe('Internal server error message')
                        })
                }
        };

        async handle(c: Context<{ Bindings: Bindings }>) {
                const payload = c.get('jwtPayload');

                const data = await c.req.json();
                console.log('data', data);

                const {
                        imageUrl,
                        mimeType,
                        sketchDuration,
                        source,
                        colorFillDuration,
                        needCanvas,
                        canvasTitle,
                        needHand,
                        handTitle,
                        needFadeout,
                        fps
                } = data;

                const task: TaskInfoRequest = {
                        taskId: nanoid(),
                        source,
                        uid: payload.uid as string,
                        app: APP.SPEEDPAINTER,
                        status: TaskStatus.WAITING,
                        params: {
                                imageUrl,
                                mimeType,
                                sketchDuration,
                                colorFillDuration,
                                needCanvas,
                                canvasTitle,
                                needHand,
                                handTitle,
                                needFadeout,
                                fps
                        },
                        accountId: payload.accountId as string
                }

                try {
                        const taskResult = await doCreateTask(task, c.env);
                        return c.json({ taskId: taskResult?.taskId }, 200);
                } catch (error: any) {
                        console.error('Create task failed:', error);
                        console.error('Error details:', {
                                message: error.message,
                                stack: error.stack,
                                name: error.name
                        });

                        if (error instanceof z.ZodError) {
                                return c.json({ error: error.errors[0].message }, 400);
                        }
                        return c.json({ error: 'Create task failed' }, 500);
                }
        }
}
