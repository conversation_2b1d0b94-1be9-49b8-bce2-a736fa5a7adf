import { contentJson, legacyTypeIntoZod, OpenAPIRoute } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';

import { APP, Bindings, FileBody, R2_PREFIX } from '../types';
import { nanoid } from 'nanoid';
import { UrlProcessor } from '../utils/url-processor';

export class FileUpload extends OpenAPIRoute {
        schema = {
                summary: '将图片上传R2',
                description: '上传文件到R2，并获得下载链接。',
                tags: ['file-upload'],
                request: {
                        params: z.object({
                                file_name: z.string().regex(/\.(jpg|jpeg|png|gif|bmp|webp)$/i),
                        }),
                        body: {
                                content: {
                                        'multipart/form-data': {
                                                schema: legacyTypeIntoZod({
                                                        file: FileBody({ format: 'binary' }),
                                                        app: z.nativeEnum(APP).describe('Application identifier')
                                                }),
                                        },
                                },
                        },
                },
                response: {
                        '200': {
                                description: '下载链接',
                                ...contentJson({
                                        url: z.string().url(),
                                }),
                        },
                },
        };

        async handle(c: Context<{ Bindings: Bindings }>) {
                const prefix = R2_PREFIX;
                const body = await c.req.parseBody();
                if (!body) {
                        return c.json({ error: 'no body' }, 400);
                }
                const app = body['app'] as string;
                const pathname = `${prefix}/${app}/${nanoid()}_${Date.now()}_${c.req.param('file_name')}`;

                try {
                        const file = body['file'] as File;
                        const headers = new Headers();
                        headers.set('content-type', file.type);

                        await c.env.MY_BUCKET.put(pathname, file, { httpMetadata: headers });
                        const r2_url = UrlProcessor.buildR2Url(c.env.R2_PUBLIC_DOMAIN, pathname);

                        return c.json({
                                url: r2_url,
                        });
                } catch (e: any) {
                        return c.json({ error: e.message }, 400);
                }
        }
}