import { <PERSON>APIRoute, contentJson } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings, SOURCE, APP } from '../types';
import { doCreateTask } from '../service/task/create-task';
import { TaskInfoRequest, TaskStatus } from '../model/task';
import { nanoid } from 'nanoid';

export class RemoveBg extends OpenAPIRoute {
        schema = {
                summary: 'Remove background, submit Task',
                description: 'Submit remove background task',
                tags: ['api'],
                request: {
                        body: contentJson(
                                z.object({
                                        imageUrl: z.string().describe('Image URL'),
                                        source: z.nativeEnum(SOURCE).describe('Channel source, possible values: "api", "web", "framer", "figma", "canva", "rapidapi", "mcp"')
                                })
                        ),
                },
                response: {
                        200: contentJson({
                                taskId: z.string().describe('Task ID')
                        }),
                        400: contentJson({
                                error: z.string().describe('Request error message')
                        }),
                        401: contentJson({
                                error: z.string().describe('Authentication error message')
                        }),
                        500: contentJson({
                                error: z.string().describe('Internal server error message')
                        })
                }
        };

        async handle(c: Context<{ Bindings: Bindings }>) {

                const payload = c.get('jwtPayload');

                // 从请求中获取数据
                const data = await c.req.json();
                console.log('data', data);

                const { imageUrl, source } = data;

                const task: TaskInfoRequest = {
                        taskId: nanoid(),
                        source,
                        uid: payload.uid as string,
                        app: APP.REMOVE_BG,
                        accountId: payload.accountId as string,
                        status: TaskStatus.WAITING,
                        params: { imageUrl }
                }

                try {
                        const taskResult = await doCreateTask(task, c.env);
                        console.log('taskResult', taskResult);
                        return c.json({ taskId: taskResult?.taskId }, 200);
                } catch (error: any) {
                        console.error('Create task failed:', error);
                        console.error('Error details:', {
                                message: error.message,
                                stack: error.stack,
                                name: error.name
                        });

                        if (error instanceof z.ZodError) {
                                return c.json({ error: error.errors[0].message }, 400);
                        }
                        return c.json({ error: 'Create task failed' }, 500);
                }
        }
}
