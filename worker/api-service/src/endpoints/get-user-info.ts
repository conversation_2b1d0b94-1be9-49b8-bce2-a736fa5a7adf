import { OpenAPIRoute, contentJson } from 'chanfana';
import { Context } from 'hono';
import { z } from 'zod';
import { Bindings } from '../types';
import { doGetAccountInfo } from '../service/supabase/a1d-user';

export class GetUserInfo extends OpenAPIRoute {
    schema = {
        summary: 'Get user information',
        description: 'Get current user name, email and picture_url information',
        tags: ['user'],
        response: {
            200: contentJson({
                success: z.boolean(),
                data: z.object({
                    name: z.string().nullable(),
                    email: z.string().nullable(),
                    picture_url: z.string().nullable(),
                    primary_owner_user_id: z.string().nullable()
                })
            }),
            400: contentJson({
                success: z.boolean(),
                error: z.string().describe('Request error message')
            }),
            500: contentJson({
                success: z.boolean(),
                error: z.string().describe('Internal server error message')
            })
        }
    };

    async handle(c: Context<{ Bindings: Bindings }>) {
        try {
            const payload = c.get('jwtPayload');
            const accountId = payload.accountId as string;

            if (!accountId) {
                return c.json({
                    success: false,
                    error: '无效的用户信息'
                }, 400);
            }

            const userInfo = await doGetAccountInfo(c.env, accountId);
            
            return c.json({
                success: true,
                data: userInfo
            });
        } catch (error: any) {
            console.error('Get user info failed:', error);
            console.error('Error details:', {
                message: error.message,
                stack: error.stack,
                name: error.name
            });

            if (error instanceof z.ZodError) {
                return c.json({ 
                    success: false, 
                    error: error.errors[0].message 
                }, 400);
            }
            
            if (error.message === 'Account not found') {
                return c.json({ 
                    success: false, 
                    error: '用户不存在' 
                }, 400);
            }

            return c.json({ 
                success: false, 
                error: '获取用户信息失败' 
            }, 500);
        }
    }
} 