import { createMiddleware } from 'hono/factory';
import { Context } from 'hono';
import { Bindings } from '../types';
import { doVerifyToken } from '../../../auth-service/src/service/jwt';

// 定义 Variables 接口
interface Variables {
        jwtPayload: {
                uid: number;
                [key: string]: any;
        };
}

// 更新 Context 类型
type AppContext = Context<{ Bindings: Bindings; Variables: Variables }>;

// Sign 验证函数
async function checkSignValid(sign: string, fixedSign: string, signMaxTimeDiff: number = 120000): Promise<boolean> {
        // 首先检查是否匹配固定的 sign
        if (fixedSign && sign === fixedSign) {
                return true;
        }

        // 检查 sign 的长度 (13位时间戳 + 32位签名)
        if (!sign || sign.length !== 45) {
                return false;
        }

        // 从 sign 中提取时间戳和签名
        const ts = parseInt(sign.slice(0, 13));
        const signature = sign.slice(13);

        // 获取当前UTC时间的毫秒数
        const now = Date.now();

        // 验证时间差
        if (Math.abs(ts - now) > signMaxTimeDiff) {
                console.log(`[X-Sign] Client time ${ts} and server time ${now} diff is too big.`);
                return false;
        }

        // 生成我们的签名，使用 Web Crypto API
        const encoder = new TextEncoder();
        const data = encoder.encode(Math.floor(ts * 1.01).toString());
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        const ourSignature = hashArray
                .map(b => b.toString(16).padStart(2, '0'))
                .join('')
                .toUpperCase()
                .slice(0, 32);

        // 比较签名
        return signature === ourSignature;
}

export const authMiddleware = createMiddleware(async (c: AppContext, next) => {


        // 这里增加一个 AdminKey 的验证
        const adminKey = c.req.header('X-Admin-Key');

        if (adminKey) {
                if (adminKey === c.env.ADMIN_KEY) {
                        c.set('jwtPayload', { uid: 0 });
                        await next();
                        return;
                }
        }

        // 验证 sign
        const sign = c.req.header('sign');
        if (sign) {
                // 从环境变量获取固定的 sign
                const fixedSign = c.env.FIXED_SIGN || '';
                if (!await checkSignValid(sign, fixedSign)) {
                        return new Response('Invalid signature', { status: 401 });
                } else {
                        // 如果验证通过，则设置 jwtPayload
                        c.set('jwtPayload', { uid: 0 });
                        await next();
                        return;
                }
        }

        const HEADER = 'Authorization';
        let headerToken = c.req.header(HEADER);

        // 处理 headerToken 为空的情况
        if (!headerToken) {
                return new Response('Missing authorization header', { status: 401 });
        }
        
        let jwtPayload;
        if (c.env.ENV === 'dev') {
                console.log(c.env);
                jwtPayload = await doVerifyToken(headerToken, c.env);
        } else {
                jwtPayload = await c.env.AUTH_SERVICE.doVerifyToken(headerToken);
        }

        if (jwtPayload instanceof Response) {
                return jwtPayload;
        }

        // 使用类型断言来解决 c.set 的类型问题
        c.set('jwtPayload', jwtPayload);

        await next();
});