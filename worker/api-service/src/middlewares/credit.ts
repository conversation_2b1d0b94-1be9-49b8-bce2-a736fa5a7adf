import { createMiddleware } from 'hono/factory';
import { Context } from 'hono';
import { calculateAvailableCredits, doGetUserCredit } from '../service/credit/service';
import { Variables, Bindings } from '../types';
import { doGetCreditRule } from '../service/supabase/credit-rules';
import { doCalculateDynamicCredits, DynamicCalculationParams } from '../service/credit/dynamic-calculator';
import { calculate302Credits } from '../utils/pricing-302';
import { isFree302Path } from '../config/free-paths';

export const creditEnoughMiddleware = createMiddleware(async (c: Context<{ Bindings: Bindings; Variables: Variables }>, next) => {
	// 获取app		
	const app = c.get('jwtPayload').app;
	const jwtPayload = c.get('jwtPayload');

	// 从请求体中获取数据，对于 302 应用，也需要检查 query 参数
	let requestBody = {};
	let source = 'web';
	let params: Record<string, unknown> = {};

	try {
		requestBody = await c.req.json();
		// 优先使用 JWT 中的 source，如果没有则使用请求体中的 source，最后使用默认值 'web'
		source = jwtPayload.source || (requestBody as any).source || 'web';
		
		// 处理参数提取：有些端点使用 params 对象，有些直接在 body 中
		if ((requestBody as any).params) {
			// 如果请求体中有 params 字段，使用它
			params = (requestBody as any).params;
		} else {
			// 否则，提取请求体中相关的参数（如 scale, sketchDuration 等）
			const { source: _, ...bodyParams } = requestBody as any;
			params = bodyParams;
		}
	} catch (error) {
		// 对于 GET 请求或没有 body 的请求，使用默认值
		console.log('No JSON body found, using defaults');
		// 即使没有请求体，也要检查 JWT 中的 source
		source = jwtPayload.source || 'web';
	}

	// 对于 302 应用，从 query 参数中获取 path
	if (app === '302') {
		const urlSearchParams = new URLSearchParams(c.req.query());
		const path = urlSearchParams.get('path');
		if (path) {
			params.path = path;
			
			// Check if this is a free path - skip credit check
			if (isFree302Path(path)) {
				console.log(`Free path detected: ${path} - skipping credit check`);
				return await next();
			}
		}
	}
	
	// Check if this is Canva user using chat-2-design - skip credit check
	if (app === 'chat-2-design' && jwtPayload.source === 'canva') {
		console.log('Canva user accessing chat-2-design - skipping credit check');
		return await next();
	}
	
	console.log('jwtPayload', c.get('jwtPayload'));

	if (!app) {
		return new Response('app is required', { status: 401 });
	}

	if (!source) {
		return new Response('source is required', { status: 401 });
	}

	// 判断credit 是否足够
	const accountId = c.get('jwtPayload').accountId;
	const uid = c.get('jwtPayload').uid;

	const credits = await doGetUserCredit(c.env, accountId, uid);

	if (credits.success) {
		const availableCredits = calculateAvailableCredits(credits.data);
		const neededCredits = await doGetNeededCredits(c.env, app, source, params);
		console.log('availableCredits', availableCredits);
		console.log('neededCredits', neededCredits);
		if (availableCredits < neededCredits) {
			return new Response('credit is not enough', { status: 401 });
		} else {
			return await next();
		}
	} else {
		return new Response('do get user credit record failed', { status: 500 });
	}
});

export async function doGetNeededCredits(env: Env, app: string, source: string, params?: Record<string, unknown>): Promise<number> {
	try {
		// Handle 302 AI app with real-time pricing
		if (app === '302') {
			const path = params?.path as string;
			if (!path) {
				throw new Error('Path parameter is required for 302 AI app');
			}
			
			console.log(`Fetching 302.ai pricing for path: ${path}`);
			return await calculate302Credits(path);
		}

		const creditRuleResult = await doGetCreditRule(env, app, source);

		// 确保我们得到的是单个 CreditRule，不是数组
		if (!creditRuleResult || Array.isArray(creditRuleResult)) {
			throw new Error(`No credit rule found for app: ${app} and source: ${source}`);
		}

		const creditRule = creditRuleResult;

		// 使用共享的动态计算服务
		if (creditRule.has_dynamic_rule && creditRule.dynamic_rule_config && params) {
			const calculationParams: DynamicCalculationParams = {
				app,
				scale: params.scale as number,
				sketchDuration: params.sketchDuration as number,
				colorFillDuration: params.colorFillDuration as number,
				...params
			};

			return doCalculateDynamicCredits(creditRule, calculationParams);
		}

		// 如果没有动态规则，使用基础 credits
		return creditRule.base_credits;
	} catch (error) {
		console.error("Error calculating credits:", {
			error: error instanceof Error ? error.message : 'Unknown error occurred',
			app,
			source,
			params
		});
		throw new Response('Failed to calculate credits', {
			status: 500,
			statusText: 'Internal Server Error'
		});
	}
}
