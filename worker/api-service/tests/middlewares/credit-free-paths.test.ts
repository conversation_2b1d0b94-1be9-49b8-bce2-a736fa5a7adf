import { describe, it, expect, beforeEach, vi } from 'vitest';
import { creditEnoughMiddleware } from '../../src/middlewares/credit';
import { createMockApiEnv } from '../setup';

// Mock dependencies
vi.mock('../../src/service/credit/service', () => ({
    doGetUserCredit: vi.fn().mockResolvedValue({
        success: true,
        data: { credit: 100, consumed_credit: 50 }
    }),
    calculateAvailableCredits: vi.fn().mockReturnValue(50)
}));

vi.mock('../../src/service/supabase/credit-rules', () => ({
    doGetCreditRule: vi.fn().mockResolvedValue({
        base_credits: 10,
        has_dynamic_rule: false
    })
}));

vi.mock('../../src/utils/pricing-302', () => ({
    calculate302Credits: vi.fn().mockResolvedValue(10)
}));

vi.mock('../../src/config/free-paths', () => ({
    isFree302Path: vi.fn((path: string) => path.includes('/v1/chat/completions'))
}));

describe('Credit Middleware - Free Paths', () => {
    let mockEnv: any;
    let mockContext: any;
    let nextFn: any;

    beforeEach(() => {
        mockEnv = createMockApiEnv();
        nextFn = vi.fn().mockResolvedValue(new Response('OK'));
        
        mockContext = {
            env: mockEnv,
            get: vi.fn((key: string) => {
                if (key === 'jwtPayload') {
                    return {
                        accountId: 'test-account',
                        uid: 'test-uid',
                        app: '302',
                        source: 'web'
                    };
                }
            }),
            req: {
                json: vi.fn().mockResolvedValue({ source: 'web' }),
                query: vi.fn().mockReturnValue('')
            }
        };

        vi.clearAllMocks();
    });

    it('should skip credit check for free path /v1/chat/completions', async () => {
        // Set up query with free path
        mockContext.req.query = vi.fn().mockReturnValue('path=/v1/chat/completions');
        
        const response = await creditEnoughMiddleware(mockContext as any, nextFn);
        
        // Should call next without checking credits
        expect(nextFn).toHaveBeenCalled();
        expect(response.status).toBe(200);
        
        // Should not call credit service
        const { doGetUserCredit } = await import('../../src/service/credit/service');
        expect(doGetUserCredit).not.toHaveBeenCalled();
    });

    it('should skip credit check for free path with sub-path', async () => {
        // Set up query with free sub-path
        mockContext.req.query = vi.fn().mockReturnValue('path=/v1/chat/completions/stream');
        
        const response = await creditEnoughMiddleware(mockContext as any, nextFn);
        
        // Should call next without checking credits
        expect(nextFn).toHaveBeenCalled();
        expect(response.status).toBe(200);
    });

    it('should check credits for non-free 302 paths', async () => {
        // Set up query with non-free path
        mockContext.req.query = vi.fn().mockReturnValue('path=/v1/models');
        
        const response = await creditEnoughMiddleware(mockContext as any, nextFn);
        
        // Should check credits
        const { doGetUserCredit } = await import('../../src/service/credit/service');
        expect(doGetUserCredit).toHaveBeenCalled();
        
        // Should call next if credits are sufficient
        expect(nextFn).toHaveBeenCalled();
        expect(response.status).toBe(200);
    });

    it('should reject when credits insufficient for non-free path', async () => {
        // Set up query with non-free path
        mockContext.req.query = vi.fn().mockReturnValue('path=/v1/embeddings');
        
        // Mock insufficient credits
        const { calculateAvailableCredits } = await import('../../src/service/credit/service');
        (calculateAvailableCredits as any).mockReturnValue(5); // Less than needed (10)
        
        const response = await creditEnoughMiddleware(mockContext as any, nextFn);
        
        // Should not call next
        expect(nextFn).not.toHaveBeenCalled();
        
        // Should return 401
        expect(response.status).toBe(401);
        const text = await response.text();
        expect(text).toBe('credit is not enough');
    });

    it('should handle missing path parameter for 302 app', async () => {
        // No path in query
        mockContext.req.query = vi.fn().mockReturnValue('');
        
        // Should throw an error during credit calculation because path is required for 302
        try {
            await creditEnoughMiddleware(mockContext as any, nextFn);
            expect.fail('Expected an error to be thrown');
        } catch (error) {
            expect(error).toBeInstanceOf(Response);
            expect((error as Response).status).toBe(500);
        }
    });

    it('should work correctly for non-302 apps', async () => {
        // Change app to something else
        mockContext.get = vi.fn((key: string) => {
            if (key === 'jwtPayload') {
                return {
                    accountId: 'test-account',
                    uid: 'test-uid',
                    app: 'image-upscaler',
                    source: 'web'
                };
            }
        });
       
        // Get the mocked functions before calling
        const { doGetUserCredit, calculateAvailableCredits } = await import('../../src/service/credit/service');
        
        // Ensure credits are sufficient
        (calculateAvailableCredits as any).mockReturnValue(50);
        
        const response = await creditEnoughMiddleware(mockContext as any, nextFn);
        
        // Should check credits normally

        expect(doGetUserCredit).toHaveBeenCalled();
        expect(nextFn).toHaveBeenCalled();
        expect(response.status).toBe(200);
    });
});