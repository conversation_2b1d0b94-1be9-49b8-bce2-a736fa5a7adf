import { describe, expect, it } from 'vitest';
import { isFree302Path, FREE_302_PATHS } from '../../src/config/free-paths';

describe('Free Paths Configuration', () => {
    describe('isFree302Path', () => {
        it('should return true for exact match of free path', () => {
            expect(isFree302Path('/v1/chat/completions')).toBe(true);
            expect(isFree302Path('v1/chat/completions')).toBe(true);
        });

        it('should return true for free path with trailing slash', () => {
            expect(isFree302Path('/v1/chat/completions/')).toBe(true);
            expect(isFree302Path('v1/chat/completions/')).toBe(true);
        });

        it('should return true for sub-paths of free paths', () => {
            expect(isFree302Path('/v1/chat/completions/stream')).toBe(true);
            expect(isFree302Path('v1/chat/completions/extra')).toBe(true);
        });

        it('should return false for non-free paths', () => {
            expect(isFree302Path('/v1/models')).toBe(false);
            expect(isFree302Path('/v1/embeddings')).toBe(false);
            expect(isFree302Path('/v2/chat/completions')).toBe(false);
        });

        it('should return false for partial matches', () => {
            expect(isFree302Path('/v1/chat')).toBe(false);
            expect(isFree302Path('/v1')).toBe(false);
        });

        it('should handle paths with multiple slashes correctly', () => {
            expect(isFree302Path('///v1/chat/completions///')).toBe(true);
            expect(isFree302Path('//v1//chat//completions')).toBe(true);
        });
    });

    describe('FREE_302_PATHS', () => {
        it('should contain /v1/chat/completions', () => {
            expect(FREE_302_PATHS).toContain('/v1/chat/completions');
        });

        it('should be an array', () => {
            expect(Array.isArray(FREE_302_PATHS)).toBe(true);
        });
    });
});