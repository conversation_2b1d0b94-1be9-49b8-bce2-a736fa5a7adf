import { describe, expect, it, vi, beforeEach } from 'vitest';
import { fetch302PricingAndCalculateCredits, calculate302Credits } from '../../src/utils/pricing-302';

// Mock the free-paths module
vi.mock('../../src/config/free-paths', () => ({
    isFree302Path: vi.fn((path: string) => path.includes('/v1/chat/completions'))
}));

// Mock fetch
global.fetch = vi.fn();

describe('302 Pricing Utilities', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe('fetch302PricingAndCalculateCredits', () => {
        it('should return 0 credits for free paths', async () => {
            const credits = await fetch302PricingAndCalculateCredits('/v1/chat/completions');
            expect(credits).toBe(0);
            expect(global.fetch).not.toHaveBeenCalled();
        });

        it('should return 0 credits for free sub-paths', async () => {
            const credits = await fetch302PricingAndCalculateCredits('/v1/chat/completions/stream');
            expect(credits).toBe(0);
            expect(global.fetch).not.toHaveBeenCalled();
        });

        it('should fetch pricing for non-free paths', async () => {
            (global.fetch as any).mockResolvedValueOnce({
                ok: true,
                json: async () => ({
                    code: 0,
                    msg: 'success',
                    data: [{
                        name: 'Test Model',
                        tag: 'test',
                        pricing: { input: 0.01, output: 0.02 },
                        pricing_prefix: '$',
                        pricing_suffix: '/1K tokens',
                        description: 'Test model'
                    }]
                })
            });

            const credits = await fetch302PricingAndCalculateCredits('/v1/models');
            
            expect(global.fetch).toHaveBeenCalledWith(
                'https://api.302.ai/dashboard/prices?path=%2Fv1%2Fmodels',
                { method: 'GET' }
            );
            
            // 0.02 USD * 100 = 2 credits
            expect(credits).toBe(2);
        });

        it('should return default credits when API fails', async () => {
            (global.fetch as any).mockResolvedValueOnce({
                ok: false,
                status: 500,
                statusText: 'Internal Server Error'
            });

            const credits = await fetch302PricingAndCalculateCredits('/v1/embeddings');
            
            // Default: 0.5 USD * 100 = 50 credits
            expect(credits).toBe(50);
        });

        it('should return default credits when API returns no data', async () => {
            (global.fetch as any).mockResolvedValueOnce({
                ok: true,
                json: async () => ({
                    code: 0,
                    msg: 'success',
                    data: []
                })
            });

            const credits = await fetch302PricingAndCalculateCredits('/v1/embeddings');
            expect(credits).toBe(50);
        });

        it('should handle fetch errors gracefully', async () => {
            (global.fetch as any).mockRejectedValueOnce(new Error('Network error'));

            const credits = await fetch302PricingAndCalculateCredits('/v1/embeddings');
            expect(credits).toBe(50);
        });
    });

    describe('calculate302Credits', () => {
        it('should return 0 for free paths', async () => {
            const credits = await calculate302Credits('/v1/chat/completions');
            expect(credits).toBe(0);
        });

        it('should calculate credits for non-free paths', async () => {
            (global.fetch as any).mockResolvedValueOnce({
                ok: true,
                json: async () => ({
                    code: 0,
                    msg: 'success',
                    data: [{
                        name: 'Test Model',
                        tag: 'test',
                        pricing: { input: 0.05, output: 0.10 },
                        pricing_prefix: '$',
                        pricing_suffix: '/1K tokens',
                        description: 'Test model'
                    }]
                })
            });

            const credits = await calculate302Credits('/v1/images/generations');
            
            // 0.10 USD * 100 = 10 credits
            expect(credits).toBe(10);
        });
    });
});