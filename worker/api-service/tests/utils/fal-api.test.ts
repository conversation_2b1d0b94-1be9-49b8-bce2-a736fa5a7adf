import { describe, it, expect, beforeEach, vi } from 'vitest'
import { createMockApiEnv } from '../setup'

// Mock fetch globally
const mockFetch = vi.fn()
global.fetch = mockFetch

describe('FalApiClient', () => {
  beforeEach(() => {
    createMockApiEnv()
    vi.clearAllMocks()
  })

  describe('API Client Initialization', () => {
    it('should initialize with correct API key', () => {
      const apiKey = 'test-fal-key'
      
      // Test API key configuration
      expect(apiKey).toBe('test-fal-key')
      expect(apiKey.length).toBeGreaterThan(0)
    })

    it('should set correct base URL', () => {
      const baseUrl = 'https://fal.run/fal-ai'
      
      expect(baseUrl).toBe('https://fal.run/fal-ai')
    })
  })

  describe('HTTP Request Methods', () => {
    it('should make GET request with correct headers', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({ data: 'test' })
      }
      
      mockFetch.mockResolvedValue(mockResponse)

      const url = 'https://fal.run/fal-ai/test-endpoint'
      const headers = {
        'Authorization': 'Bearer test-fal-key',
        'Content-Type': 'application/json'
      }

      await mockFetch(url, {
        method: 'GET',
        headers
      })

      expect(mockFetch).toHaveBeenCalledWith(url, {
        method: 'GET',
        headers
      })
    })

    it('should make POST request with body', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({ task_id: '123' })
      }
      
      mockFetch.mockResolvedValue(mockResponse)

      const url = 'https://fal.run/fal-ai/submit'
      const body = { prompt: 'test image' }
      const headers = {
        'Authorization': 'Bearer test-fal-key',
        'Content-Type': 'application/json'
      }

      await mockFetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(body)
      })

      expect(mockFetch).toHaveBeenCalledWith(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(body)
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle 401 unauthorized error', async () => {
      const mockResponse = {
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        json: () => Promise.resolve({ error: 'Invalid API key' })
      }
      
      mockFetch.mockResolvedValue(mockResponse)

      const url = 'https://fal.run/fal-ai/test'
      const response = await mockFetch(url)

      expect(response.ok).toBe(false)
      expect(response.status).toBe(401)
    })

    it('should handle 429 rate limit error', async () => {
      const mockResponse = {
        ok: false,
        status: 429,
        statusText: 'Too Many Requests',
        json: () => Promise.resolve({ error: 'Rate limit exceeded' })
      }
      
      mockFetch.mockResolvedValue(mockResponse)

      const url = 'https://fal.run/fal-ai/test'
      const response = await mockFetch(url)

      expect(response.ok).toBe(false)
      expect(response.status).toBe(429)
    })

    it('should handle network errors', async () => {
      const networkError = new Error('Network error')
      mockFetch.mockRejectedValue(networkError)

      try {
        await mockFetch('https://fal.run/fal-ai/test')
      } catch (error) {
        expect(error.message).toBe('Network error')
      }
    })

    it('should handle timeout errors', async () => {
      const timeoutError = new Error('Request timeout')
      mockFetch.mockRejectedValue(timeoutError)

      try {
        await mockFetch('https://fal.run/fal-ai/test')
      } catch (error) {
        expect(error.message).toBe('Request timeout')
      }
    })
  })

  describe('Response Processing', () => {
    it('should parse JSON response correctly', async () => {
      const mockData = {
        task_id: '123',
        status: 'submitted'
      }
      
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve(mockData)
      }
      
      mockFetch.mockResolvedValue(mockResponse)

      const response = await mockFetch('https://fal.run/fal-ai/test')
      const data = await response.json()

      expect(data).toEqual(mockData)
      expect(data.task_id).toBe('123')
      expect(data.status).toBe('submitted')
    })

    it('should handle empty response', async () => {
      const mockResponse = {
        ok: true,
        status: 204,
        json: () => Promise.resolve({})
      }
      
      mockFetch.mockResolvedValue(mockResponse)

      const response = await mockFetch('https://fal.run/fal-ai/test')
      const data = await response.json()

      expect(data).toEqual({})
    })
  })

  describe('Retry Logic', () => {
    it('should retry on temporary failures', async () => {
      const failureResponse = {
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      }
      
      const successResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({ success: true })
      }

      // First call fails, second succeeds
      mockFetch
        .mockResolvedValueOnce(failureResponse)
        .mockResolvedValueOnce(successResponse)

      // Simulate retry logic
      let response = await mockFetch('https://fal.run/fal-ai/test')
      if (!response.ok) {
        // Retry
        response = await mockFetch('https://fal.run/fal-ai/test')
      }

      expect(mockFetch).toHaveBeenCalledTimes(2)
      expect(response.ok).toBe(true)
    })

    it('should implement exponential backoff', async () => {
      const delays = [1000, 2000, 4000] // Exponential backoff delays
      
      for (let i = 0; i < delays.length; i++) {
        const delay = delays[i]
        expect(delay).toBe(Math.pow(2, i) * 1000)
      }
    })
  })

  describe('Task Status Polling', () => {
    it('should poll task status until completion', async () => {
      const taskId = '123'
      
      const responses = [
        { status: 'submitted' },
        { status: 'processing' },
        { status: 'processing' },
        { status: 'completed', result: { image_url: 'https://example.com/image.jpg' } }
      ]

      responses.forEach((responseData, _index) => {
        mockFetch.mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: () => Promise.resolve(responseData)
        })
      })

      // Simulate polling
      const results = []
      for (let i = 0; i < responses.length; i++) {
        const response = await mockFetch(`https://fal.run/fal-ai/status/${taskId}`)
        const data = await response.json()
        results.push(data)
        
        if (data.status === 'completed' || data.status === 'failed') {
          break
        }
      }

      expect(mockFetch).toHaveBeenCalledTimes(4)
      expect(results[results.length - 1].status).toBe('completed')
      expect(results[results.length - 1].result.image_url).toBe('https://example.com/image.jpg')
    })

    it('should handle failed task status', async () => {
      const taskId = '123'
      
      const failedResponse = {
        status: 'failed',
        error: 'Processing failed'
      }

      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        json: () => Promise.resolve(failedResponse)
      })

      const response = await mockFetch(`https://fal.run/fal-ai/status/${taskId}`)
      const data = await response.json()

      expect(data.status).toBe('failed')
      expect(data.error).toBe('Processing failed')
    })
  })

  describe('URL Construction', () => {
    it('should construct correct endpoint URLs', () => {
      const baseUrl = 'https://fal.run/fal-ai'
      const endpoints = {
        submit: `${baseUrl}/submit`,
        status: (taskId: string) => `${baseUrl}/status/${taskId}`,
        result: (taskId: string) => `${baseUrl}/result/${taskId}`
      }

      expect(endpoints.submit).toBe('https://fal.run/fal-ai/submit')
      expect(endpoints.status('123')).toBe('https://fal.run/fal-ai/status/123')
      expect(endpoints.result('123')).toBe('https://fal.run/fal-ai/result/123')
    })
  })
})