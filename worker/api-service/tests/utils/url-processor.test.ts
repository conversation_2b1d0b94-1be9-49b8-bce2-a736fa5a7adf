import { describe, it, expect, vi, beforeEach } from 'vitest';
import { UrlProcessor } from '../../src/utils/url-processor';
import { TaskInfoResult, TaskStatus } from '../../src/model/task';
import { createMockApiEnv, mockR2 } from '../setup';

describe('UrlProcessor', () => {
  let mockEnv: any;

  beforeEach(() => {
    vi.clearAllMocks();
    mockEnv = createMockApiEnv({
      R2_PUBLIC_DOMAIN: 'https://cdn.a1d.ai',
    });
  });

  describe('isA1dUrl', () => {
    it('should correctly identify a1d.ai URLs', () => {
      expect(UrlProcessor.isA1dUrl('https://a1d.ai/image.jpg')).toBe(true);
      expect(UrlProcessor.isA1dUrl('https://cdn.a1d.ai/image.jpg')).toBe(true);
      expect(UrlProcessor.isA1dUrl('https://api.a1d.ai/image.jpg')).toBe(true);
      expect(UrlProcessor.isA1dUrl('https://a1d.net/image.jpg')).toBe(true);
      expect(UrlProcessor.isA1dUrl('https://cdn.a1d.net/image.jpg')).toBe(true);
    });

    it('should correctly identify non-a1d URLs', () => {
      expect(UrlProcessor.isA1dUrl('https://example.com/image.jpg')).toBe(false);
      expect(UrlProcessor.isA1dUrl('https://fal.ai/image.jpg')).toBe(false);
      expect(UrlProcessor.isA1dUrl('https://storage.googleapis.com/image.jpg')).toBe(false);
    });

    it('should handle invalid URLs gracefully', () => {
      expect(UrlProcessor.isA1dUrl('')).toBe(false);
      expect(UrlProcessor.isA1dUrl(null as any)).toBe(false);
      expect(UrlProcessor.isA1dUrl(undefined as any)).toBe(false);
      expect(UrlProcessor.isA1dUrl('not-a-url')).toBe(false);
    });
  });

  describe('processUrlField', () => {
    it('should return original URL if it is already a1d URL', async () => {
      const a1dUrl = 'https://cdn.a1d.ai/image.jpg';
      const result = await UrlProcessor.processUrlField(mockEnv, a1dUrl, 'image', 'test-app');
      expect(result).toBe(a1dUrl);
    });

    it('should process non-a1d URLs', async () => {
      const externalUrl = 'https://example.com/image.jpg';

      // Mock fetch response
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        arrayBuffer: () => Promise.resolve(new ArrayBuffer(1024)),
        headers: new Headers({ 'content-type': 'image/jpeg' }),
      });

      // Mock R2 put operation
      mockR2.put.mockResolvedValue(undefined);

      const result = await UrlProcessor.processUrlField(mockEnv, externalUrl, 'image', 'test-app');

      expect(result).toMatch(/^https:\/\/cdn\.a1d\.ai\/result\/test-app\/originals\/images\/.+\.jpg$/);
      expect(mockR2.put).toHaveBeenCalled();
    });

    it('should return null for invalid URLs', async () => {
      const result = await UrlProcessor.processUrlField(mockEnv, '', 'image', 'test-app');
      expect(result).toBeNull();
    });
  });

  describe('processTaskResultWithUrlConversion', () => {
    it('should return original task if not in terminal state', async () => {
      const task: TaskInfoResult = {
        taskId: 'test-task',
        status: TaskStatus.PROCESSING,
        imageUrl: 'https://example.com/image.jpg',
      };

      const result = await UrlProcessor.processTaskResultWithUrlConversion(task, mockEnv, 'test-app');
      expect(result).toBe(task);
    });

    it('should return original task if all URLs are already a1d URLs', async () => {
      const task: TaskInfoResult = {
        taskId: 'test-task',
        status: TaskStatus.SUCCESS,
        imageUrl: 'https://cdn.a1d.ai/image.jpg',
        videoUrl: 'https://cdn.a1d.ai/video.mp4',
      };

      const result = await UrlProcessor.processTaskResultWithUrlConversion(task, mockEnv, 'test-app');
      expect(result).toBe(task);
    });

    it('should process non-a1d URLs in terminal tasks', async () => {
      const task: TaskInfoResult = {
        taskId: 'test-task',
        status: TaskStatus.SUCCESS,
        imageUrl: 'https://example.com/image.jpg',
        videoUrl: 'https://cdn.a1d.ai/video.mp4', // Already a1d URL
      };

      // Mock fetch response
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        arrayBuffer: () => Promise.resolve(new ArrayBuffer(1024)),
        headers: new Headers({ 'content-type': 'image/jpeg' }),
      });

      mockR2.put.mockResolvedValue(undefined);

      const result = await UrlProcessor.processTaskResultWithUrlConversion(task, mockEnv, 'test-app');

      expect(result.imageUrl).toMatch(/^https:\/\/cdn\.a1d\.ai\/result\/test-app\/originals\/images\/.+\.jpg$/);
      expect(result.videoUrl).toBe('https://cdn.a1d.ai/video.mp4'); // Should remain unchanged
      expect(result.originalImageUrl).toBe('https://example.com/image.jpg');
    });

    it('should handle URL processing errors gracefully', async () => {
      const task: TaskInfoResult = {
        taskId: 'test-task',
        status: TaskStatus.SUCCESS,
        imageUrl: 'https://example.com/image.jpg',
      };

      // Mock fetch to fail
      global.fetch = vi.fn().mockRejectedValue(new Error('Network error'));

      const result = await UrlProcessor.processTaskResultWithUrlConversion(task, mockEnv, 'test-app');

      // Should return original task when URL processing fails
      expect(result).toStrictEqual(task);
    });

    it('should process all URL fields correctly', async () => {
      const task: TaskInfoResult = {
        taskId: 'test-task',
        status: TaskStatus.FINISHED,
        imageUrl: 'https://example.com/image.jpg',
        thumbUrl: 'https://example.com/thumb.jpg',
        videoUrl: 'https://example.com/video.mp4',
        sketchImageUrl: 'https://example.com/sketch.jpg',
        colorImageUrl: 'https://example.com/color.jpg',
      };

      // Mock fetch response
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        arrayBuffer: () => Promise.resolve(new ArrayBuffer(1024)),
        headers: new Headers({ 'content-type': 'image/jpeg' }),
      });

      mockR2.put.mockResolvedValue(undefined);

      const result = await UrlProcessor.processTaskResultWithUrlConversion(task, mockEnv, 'test-app');

      // All URLs should be converted
      expect(result.imageUrl).toMatch(/^https:\/\/cdn\.a1d\.ai\/result\/test-app\/originals\/images\/.+\.jpg$/);
      expect(result.thumbUrl).toMatch(/^https:\/\/cdn\.a1d\.ai\/result\/test-app\/originals\/images\/.+\.jpg$/);
      expect(result.videoUrl).toMatch(/^https:\/\/cdn\.a1d\.ai\/result\/test-app\/originals\/videos\/.+\.mp4$/);
      expect(result.sketchImageUrl).toMatch(/^https:\/\/cdn\.a1d\.ai\/result\/test-app\/originals\/images\/.+\.jpg$/);
      expect(result.colorImageUrl).toMatch(/^https:\/\/cdn\.a1d\.ai\/result\/test-app\/originals\/images\/.+\.jpg$/);

      // Original URLs should be preserved
      expect(result.originalImageUrl).toBe('https://example.com/image.jpg');
      expect(result.originalThumbUrl).toBe('https://example.com/thumb.jpg');
      expect(result.originalVideoUrl).toBe('https://example.com/video.mp4');
      expect(result.originalSketchImageUrl).toBe('https://example.com/sketch.jpg');
      expect(result.originalColorImageUrl).toBe('https://example.com/color.jpg');
    });

    it('should skip special fields when skipSpecialFields is true', async () => {
      const task: TaskInfoResult = {
        taskId: 'test-task',
        status: TaskStatus.SUCCESS,
        imageUrl: 'https://example.com/image.jpg',
        thumbUrl: 'https://example.com/thumb.jpg',
        videoUrl: 'https://example.com/video.mp4',
        sketchImageUrl: 'https://example.com/sketch.jpg',
        colorImageUrl: 'https://example.com/color.jpg',
      };

      // Mock fetch response
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        arrayBuffer: () => Promise.resolve(new ArrayBuffer(1024)),
        headers: new Headers({ 'content-type': 'image/jpeg' }),
      });

      mockR2.put.mockResolvedValue(undefined);

      const result = await UrlProcessor.processTaskResultWithUrlConversion(
        task,
        mockEnv,
        'test-app',
        { skipSpecialFields: true }
      );

      // Only imageUrl and thumbUrl should be converted
      expect(result.imageUrl).toMatch(/^https:\/\/cdn\.a1d\.ai\/result\/test-app\/originals\/images\/.+\.jpg$/);
      expect(result.thumbUrl).toMatch(/^https:\/\/cdn\.a1d\.ai\/result\/test-app\/originals\/images\/.+\.jpg$/);

      // Special fields should remain unchanged
      expect(result.videoUrl).toBe('https://example.com/video.mp4');
      expect(result.sketchImageUrl).toBe('https://example.com/sketch.jpg');
      expect(result.colorImageUrl).toBe('https://example.com/color.jpg');

      // Original URLs should only exist for converted fields
      expect(result.originalImageUrl).toBe('https://example.com/image.jpg');
      expect(result.originalThumbUrl).toBe('https://example.com/thumb.jpg');
      expect(result.originalVideoUrl).toBeUndefined();
      expect(result.originalSketchImageUrl).toBeUndefined();
      expect(result.originalColorImageUrl).toBeUndefined();
    });

    it('should process all fields when skipSpecialFields is false (default)', async () => {
      const task: TaskInfoResult = {
        taskId: 'test-task',
        status: TaskStatus.SUCCESS,
        imageUrl: 'https://example.com/image.jpg',
        thumbUrl: 'https://example.com/thumb.jpg',
        videoUrl: 'https://example.com/video.mp4',
        sketchImageUrl: 'https://example.com/sketch.jpg',
        colorImageUrl: 'https://example.com/color.jpg',
      };

      // Mock fetch response
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        arrayBuffer: () => Promise.resolve(new ArrayBuffer(1024)),
        headers: new Headers({ 'content-type': 'image/jpeg' }),
      });

      mockR2.put.mockResolvedValue(undefined);

      const result = await UrlProcessor.processTaskResultWithUrlConversion(
        task,
        mockEnv,
        'test-app'
        // No options passed - should use default behavior (skipSpecialFields: false)
      );

      // All URLs should be converted
      expect(result.imageUrl).toMatch(/^https:\/\/cdn\.a1d\.ai\/result\/test-app\/originals\/images\/.+\.jpg$/);
      expect(result.thumbUrl).toMatch(/^https:\/\/cdn\.a1d\.ai\/result\/test-app\/originals\/images\/.+\.jpg$/);
      expect(result.videoUrl).toMatch(/^https:\/\/cdn\.a1d\.ai\/result\/test-app\/originals\/videos\/.+\.mp4$/);
      expect(result.sketchImageUrl).toMatch(/^https:\/\/cdn\.a1d\.ai\/result\/test-app\/originals\/images\/.+\.jpg$/);
      expect(result.colorImageUrl).toMatch(/^https:\/\/cdn\.a1d\.ai\/result\/test-app\/originals\/images\/.+\.jpg$/);

      // All original URLs should be preserved
      expect(result.originalImageUrl).toBe('https://example.com/image.jpg');
      expect(result.originalThumbUrl).toBe('https://example.com/thumb.jpg');
      expect(result.originalVideoUrl).toBe('https://example.com/video.mp4');
      expect(result.originalSketchImageUrl).toBe('https://example.com/sketch.jpg');
      expect(result.originalColorImageUrl).toBe('https://example.com/color.jpg');
    });
  });

  describe('buildR2Url', () => {
    it('should correctly build R2 URLs', () => {
      expect(UrlProcessor.buildR2Url('https://cdn.a1d.ai', '/path/file.jpg'))
        .toBe('https://cdn.a1d.ai/path/file.jpg');

      expect(UrlProcessor.buildR2Url('https://cdn.a1d.ai/', 'path/file.jpg'))
        .toBe('https://cdn.a1d.ai/path/file.jpg');

      expect(UrlProcessor.buildR2Url('https://cdn.a1d.ai', 'path/file.jpg'))
        .toBe('https://cdn.a1d.ai/path/file.jpg');
    });
  });
});
