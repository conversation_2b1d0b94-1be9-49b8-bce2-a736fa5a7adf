import { describe, it, expect, vi, beforeEach } from 'vitest';
import { TaskInfoResult, TaskStatus, TaskQueueMessage as _TaskQueueMessage } from '../../src/model/task';
import { TaskService as _TaskService } from '../../src/service/task/task-service';
import { UrlProcessor } from '../../src/utils/url-processor';
import { handleStaleTasksCleanup as _handleStaleTasksCleanup } from '../../src/service/task/task-cleanup';
import { createMockApiEnv, mockR2 } from '../setup';

describe('URL Conversion Consistency Integration Tests', () => {
  let mockEnv: Env;
  const externalImageUrl = 'https://example.com/image.jpg';
  const externalVideoUrl = 'https://example.com/video.mp4';
  const a1dImageUrl = 'https://cdn.a1d.ai/image.jpg';

  beforeEach(() => {
    vi.clearAllMocks();
    mockEnv = createMockApiEnv({
      R2_PUBLIC_DOMAIN: 'https://cdn.a1d.ai',
    });

    // Mock fetch response for external URLs
    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      arrayBuffer: () => Promise.resolve(new ArrayBuffer(1024)),
      headers: new Headers({ 'content-type': 'image/jpeg' }),
    });

    mockR2.put.mockResolvedValue(undefined);
  });

  describe('Centralized URL Processing Method', () => {
    it('should provide consistent URL conversion across all components', async () => {
      const taskResult: TaskInfoResult = {
        taskId: 'test-task',
        status: TaskStatus.SUCCESS,
        imageUrl: externalImageUrl,
        videoUrl: externalVideoUrl,
      };

      // Test the centralized method directly
      const processed1 = await UrlProcessor.processTaskResultWithUrlConversion(
        taskResult,
        mockEnv,
        'test-app'
      );

      // Test it again to ensure consistency
      const processed2 = await UrlProcessor.processTaskResultWithUrlConversion(
        taskResult,
        mockEnv,
        'test-app'
      );

      // Both should produce the same pattern (though different filenames due to nanoid)
      expect(processed1.imageUrl).toMatch(/^https:\/\/cdn\.a1d\.ai\/result\/test-app\/originals\/images\/.+\.jpg$/);
      expect(processed1.videoUrl).toMatch(/^https:\/\/cdn\.a1d\.ai\/result\/test-app\/originals\/videos\/.+\.mp4$/);
      expect(processed1.originalImageUrl).toBe(externalImageUrl);
      expect(processed1.originalVideoUrl).toBe(externalVideoUrl);

      // Structure should be consistent
      expect(processed2.imageUrl).toMatch(/^https:\/\/cdn\.a1d\.ai\/result\/test-app\/originals\/images\/.+\.jpg$/);
      expect(processed2.videoUrl).toMatch(/^https:\/\/cdn\.a1d\.ai\/result\/test-app\/originals\/videos\/.+\.mp4$/);
    });

    it('should handle mixed a1d and external URLs correctly', async () => {
      const taskResult: TaskInfoResult = {
        taskId: 'test-task',
        status: TaskStatus.SUCCESS,
        imageUrl: a1dImageUrl, // Already a1d URL
        videoUrl: externalVideoUrl, // External URL
      };

      const processed = await UrlProcessor.processTaskResultWithUrlConversion(
        taskResult,
        mockEnv,
        'test-app'
      );

      // a1d URL should remain unchanged
      expect(processed.imageUrl).toBe(a1dImageUrl);
      expect(processed.originalImageUrl).toBeUndefined();

      // External URL should be converted
      expect(processed.videoUrl).toMatch(/^https:\/\/cdn\.a1d\.ai\/result\/test-app\/originals\/videos\/.+\.mp4$/);
      expect(processed.originalVideoUrl).toBe(externalVideoUrl);
    });
  });

  describe('TaskService URL Processing', () => {
    it('should use centralized URL conversion in updateTaskStatus', async () => {
      const taskResult: TaskInfoResult = {
        taskId: 'test-task',
        status: TaskStatus.SUCCESS,
        imageUrl: externalImageUrl,
      };

      // Mock doUpdateTask
      let capturedTask: any;
      vi.doMock('../../src/service/supabase/task', () => ({
        doUpdateTask: vi.fn(async (task: any) => {
          capturedTask = task;
          return Promise.resolve();
        }),
      }));

      // Clear module cache and reimport
      vi.resetModules();
      const { TaskService: FreshTaskService } = await import('../../src/service/task/task-service');

      await FreshTaskService.updateTaskStatus(
        mockEnv,
        'test-task',
        TaskStatus.SUCCESS,
        taskResult,
        'test-app'
      );

      // Verify that the update was called with processed URLs
      expect(capturedTask).toBeDefined();
      expect(capturedTask.status).toBe(TaskStatus.SUCCESS);
      expect(capturedTask.result).toBeDefined();
      expect(capturedTask.result.imageUrl).toMatch(/^https:\/\/cdn\.a1d\.ai\/result\/test-app\/originals\/images\/.+\.jpg$/);
      expect(capturedTask.result.originalImageUrl).toBe(externalImageUrl);
    });

  });

  describe('Scheduled Task URL Processing', () => {
    it('should process URLs when updating completed tasks', async () => {
      // Mock all the required services
      const staleTasks = [{
        task_id: 'stale-task',
        app: 'test-app',
        uid: 'test-uid',
        credit: 1,
      }];

      const mockFetchExternalTaskStatus = vi.fn().mockResolvedValue({
        taskId: 'stale-task',
        status: TaskStatus.SUCCESS,
        imageUrl: externalImageUrl,
      });

      let updateTaskCalled = false;
      
      vi.doMock('../../src/service/supabase/task', () => ({
        doFindStaleTasks: vi.fn().mockResolvedValue(staleTasks),
        doUpdateTask: vi.fn().mockImplementation(() => {
          updateTaskCalled = true;
          return Promise.resolve();
        }),
      }));

      vi.doMock('../../src/service/supabase/a1d-user', () => ({
        doGetA1dUser: vi.fn().mockResolvedValue({ account_id: 'test-account' }),
      }));

      vi.doMock('../../src/service/credit/service', () => ({
        doCommitCreditDeduction: vi.fn().mockResolvedValue({ success: true }),
      }));

      vi.doMock('../../src/service/task/task-service', () => ({
        TaskService: {
          fetchExternalTaskStatus: mockFetchExternalTaskStatus,
          updateTaskStatus: async (env: any, taskId: string, status: any, result: any, app: string) => {
            // Import the mocked doUpdateTask
            const { doUpdateTask } = await import('../../src/service/supabase/task');
            
            // Process URLs for terminal status
            if (status === TaskStatus.FINISHED || status === TaskStatus.SUCCESS) {
              const processed = await UrlProcessor.processTaskResultWithUrlConversion(result, env, app);
              await doUpdateTask({
                task_id: taskId,
                status,
                result: processed,
              }, env);
            }
          },
          findStaleTasks: async () => staleTasks,
        },
      }));

      // Clear module cache
      vi.resetModules();

      // Import with fresh mocks
      const { handleStaleTasksCleanup: freshHandleStaleTasksCleanup } = await import('../../src/service/task/task-cleanup');

      // Run the cleanup
      await freshHandleStaleTasksCleanup(mockEnv, 10);

      // Verify external task status was fetched
      expect(mockFetchExternalTaskStatus).toHaveBeenCalledWith('test-app', 'stale-task', mockEnv);
      
      // Verify task was updated
      expect(updateTaskCalled).toBe(true);
    }, 10000);
  });

  describe('Error Handling Consistency', () => {
    it('should handle URL processing failures gracefully across all components', async () => {
      // Mock fetch to fail
      global.fetch = vi.fn().mockRejectedValue(new Error('Network error'));

      const taskResult: TaskInfoResult = {
        taskId: 'test-task',
        status: TaskStatus.SUCCESS,
        imageUrl: externalImageUrl,
      };

      // Test centralized method error handling
      const processed = await UrlProcessor.processTaskResultWithUrlConversion(
        taskResult,
        mockEnv,
        'test-app'
      );

      // Should return original task when URL processing fails
      expect(processed).toStrictEqual(taskResult);
      expect(processed.imageUrl).toBe(externalImageUrl);
    });
  });

  describe('URL Field Coverage', () => {
    it('should process all supported URL fields consistently', async () => {
      const taskResult: TaskInfoResult = {
        taskId: 'test-task',
        status: TaskStatus.SUCCESS,
        imageUrl: 'https://example.com/image.jpg',
        thumbUrl: 'https://example.com/thumb.jpg',
        videoUrl: 'https://example.com/video.mp4',
        sketchImageUrl: 'https://example.com/sketch.jpg',
        colorImageUrl: 'https://example.com/color.jpg',
      };

      // Reset fetch mock to succeed
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        arrayBuffer: () => Promise.resolve(new ArrayBuffer(1024)),
        headers: new Headers({ 'content-type': 'image/jpeg' }),
      });

      const processed = await UrlProcessor.processTaskResultWithUrlConversion(
        taskResult,
        mockEnv,
        'test-app'
      );

      // Verify all URL fields are processed
      const urlFields = ['imageUrl', 'thumbUrl', 'videoUrl', 'sketchImageUrl', 'colorImageUrl'];
      const originalFields = ['originalImageUrl', 'originalThumbUrl', 'originalVideoUrl', 'originalSketchImageUrl', 'originalColorImageUrl'];

      urlFields.forEach((field) => {
        expect((processed as any)[field]).toMatch(/^https:\/\/cdn\.a1d\.ai\/result\/test-app\/originals\/(images|videos)\/.+\.(jpg|mp4)$/);
      });

      originalFields.forEach((field, index) => {
        expect((processed as any)[field]).toBe((taskResult as any)[urlFields[index]]);
      });
    });
  });
});
