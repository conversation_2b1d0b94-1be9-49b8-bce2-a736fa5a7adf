import { beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest'

// API Service specific test setup
beforeAll(async () => {
  // Setup for API service tests
})

afterAll(async () => {
  // Cleanup for API service tests
})

beforeEach(async () => {
  // Setup before each API service test
})

afterEach(async () => {
  // Cleanup after each API service test
})

// Mock KV namespace
export const mockKV = {
  get: vi.fn(),
  put: vi.fn(),
  delete: vi.fn(),
  list: vi.fn(),
}

// Mock R2 bucket
export const mockR2 = {
  get: vi.fn(),
  put: vi.fn(),
  delete: vi.fn(),
  list: vi.fn(),
}

// Mock Queue
export const mockQueue = {
  send: vi.fn(),
  sendBatch: vi.fn(),
}

// Mock Hyperdrive
export const mockHyperdrive = {
  connectionString: 'postgres://test@localhost:5432/test',
}

// Mock Supabase client
export const mockSupabase = {
  from: vi.fn(() => ({
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    single: vi.fn(),
    then: vi.fn(),
  })),
}

export function createMockApiEnv(overrides: Record<string, any> = {}) {
  return {
    JWT_SECRET: 'test-secret',
    FIXED_SIGN: '1234',
    SUPABASE_URL: 'https://test.supabase.co',
    SUPABASE_SERVICE_ROLE_KEY: 'test-key',
    SUPABASE_JWT_SECRET: 'test-jwt-secret',
    DATABASE_URL: 'postgres://test@localhost:5432/test',
    ENV: 'test',
    EXPIRATION_TTL: 3600,
    FAL_API_KEY: 'test-fal-key',
    RMBG_API_URL: 'https://test-rmbg.com',
    RMBG_TOKEN: 'test-token',
    GEMINI_API_KEY: 'test-gemini-key',
    ADMIN_KEY: 'test-admin-key',
    MY_KV_NAMESPACE: mockKV,
    MY_BUCKET: mockR2,
    TASK_QUEUE: mockQueue,
    HYPERDRIVE: mockHyperdrive,
    ...overrides,
  }
}