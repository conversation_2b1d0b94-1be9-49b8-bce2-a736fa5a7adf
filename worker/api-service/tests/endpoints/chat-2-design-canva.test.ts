import { describe, it, expect, beforeEach, vi } from 'vitest';
import { Chat2Design } from '../../src/endpoints/chat-2-design';
import { createMockApiEnv } from '../setup';

// Mock dependencies
vi.mock('../../src/service/task/create-task', () => ({
    doCreateTask: vi.fn().mockResolvedValue({
        taskId: 'test-task-123',
        imageUrls: ['https://example.com/result.jpg'],
        message: 'Task completed successfully'
    })
}));

vi.mock('nanoid', () => ({
    nanoid: vi.fn().mockReturnValue('test-task-123')
}));

describe('Chat2Design Endpoint - Canva Integration', () => {
    let endpoint: Chat2Design;
    let mockEnv: any;
    let mockContext: any;

    beforeEach(() => {
        endpoint = new Chat2Design();
        mockEnv = createMockApiEnv();
        
        vi.clearAllMocks();
    });

    describe('Canva User Authentication', () => {
        it('should handle Canva JWT with userId and brandId', async () => {
            // Mock Canva JWT payload
            mockContext = {
                env: mockEnv,
                get: vi.fn((key: string) => {
                    if (key === 'jwtPayload') {
                        return {
                            iat: 1753782383,
                            nbf: 1753782383,
                            exp: 1753782683,
                            aud: "AAGi7fjkabQ",
                            userId: "AQPvhWHL3jKPvuZAO_U9Z0Tqwbo9ZI9d1kmvxrFg9tw=",
                            brandId: "AQPvhWFLz5xfKWT5vScJyrfpeO_GvWv1jPy7Watxl_A=",
                            source: "canva",
                            app: "chat-2-design"
                        };
                    }
                }),
                req: {
                    json: vi.fn().mockResolvedValue({
                        prompt: 'Create a beautiful design',
                        imageUrls: []
                    })
                },
                json: vi.fn().mockImplementation((data) => ({
                    status: 200,
                    json: () => Promise.resolve(data)
                }))
            };

            const response = await endpoint.handle(mockContext as any);
            
            // Should succeed without credit issues
            expect(response.status).toBe(200);
            
            // Check that task was created with proper fallback values
            const { doCreateTask } = await import('../../src/service/task/create-task');
            expect(doCreateTask).toHaveBeenCalledWith(
                expect.objectContaining({
                    source: 'canva',
                    uid: 'canva_AQPvhWHL3jKPvuZAO_U9Z0Tqwbo9ZI9d1kmvxrFg9tw=',
                    accountId: 'canva_AQPvhWFLz5xfKWT5vScJyrfpeO_GvWv1jPy7Watxl_A=',
                    app: 'chat-2-design'
                }),
                mockEnv
            );
        });

        it('should handle Canva JWT without userId/brandId', async () => {
            // Mock incomplete Canva JWT payload
            mockContext = {
                env: mockEnv,
                get: vi.fn((key: string) => {
                    if (key === 'jwtPayload') {
                        return {
                            source: "canva",
                            app: "chat-2-design"
                            // Missing userId and brandId
                        };
                    }
                }),
                req: {
                    json: vi.fn().mockResolvedValue({
                        prompt: 'Create a beautiful design',
                        imageUrls: []
                    })
                },
                json: vi.fn().mockImplementation((data) => ({
                    status: 200,
                    json: () => Promise.resolve(data)
                }))
            };

            const response = await endpoint.handle(mockContext as any);
            
            // Should succeed with default values
            expect(response.status).toBe(200);
            
            // Check that task was created with default fallback values
            const { doCreateTask } = await import('../../src/service/task/create-task');
            expect(doCreateTask).toHaveBeenCalledWith(
                expect.objectContaining({
                    source: 'canva',
                    uid: 'canva_anonymous',
                    accountId: 'canva_default',
                    app: 'chat-2-design'
                }),
                mockEnv
            );
        });

        it('should use regular JWT fields when available', async () => {
            // Mock regular JWT payload with uid and accountId
            mockContext = {
                env: mockEnv,
                get: vi.fn((key: string) => {
                    if (key === 'jwtPayload') {
                        return {
                            uid: 'regular-user-123',
                            accountId: 'regular-account-456',
                            source: "web",
                            app: "chat-2-design"
                        };
                    }
                }),
                req: {
                    json: vi.fn().mockResolvedValue({
                        prompt: 'Create a beautiful design',
                        imageUrls: []
                    })
                },
                json: vi.fn().mockImplementation((data) => ({
                    status: 200,
                    json: () => Promise.resolve(data)
                }))
            };

            const response = await endpoint.handle(mockContext as any);
            
            // Should succeed
            expect(response.status).toBe(200);
            
            // Check that task was created with regular JWT values
            const { doCreateTask } = await import('../../src/service/task/create-task');
            expect(doCreateTask).toHaveBeenCalledWith(
                expect.objectContaining({
                    source: 'web',
                    uid: 'regular-user-123',
                    accountId: 'regular-account-456',
                    app: 'chat-2-design'
                }),
                mockEnv
            );
        });
    });

    describe('Source Priority Logic', () => {
        it('should prioritize JWT source over request body source', async () => {
            mockContext = {
                env: mockEnv,
                get: vi.fn((key: string) => {
                    if (key === 'jwtPayload') {
                        return {
                            uid: 'test-user',
                            accountId: 'test-account',
                            source: 'canva', // JWT source
                            app: 'chat-2-design'
                        };
                    }
                }),
                req: {
                    json: vi.fn().mockResolvedValue({
                        prompt: 'Create a beautiful design',
                        imageUrls: [],
                        source: 'web' // Request body source
                    })
                },
                json: vi.fn().mockImplementation((data) => ({
                    status: 200,
                    json: () => Promise.resolve(data)
                }))
            };

            const response = await endpoint.handle(mockContext as any);
            
            expect(response.status).toBe(200);
            
            // Should use JWT source (canva), not request body source (web)
            const { doCreateTask } = await import('../../src/service/task/create-task');
            expect(doCreateTask).toHaveBeenCalledWith(
                expect.objectContaining({
                    source: 'canva'
                }),
                mockEnv
            );
        });

        it('should use request body source when JWT source is not available', async () => {
            mockContext = {
                env: mockEnv,
                get: vi.fn((key: string) => {
                    if (key === 'jwtPayload') {
                        return {
                            uid: 'test-user',
                            accountId: 'test-account',
                            // No source in JWT
                            app: 'chat-2-design'
                        };
                    }
                }),
                req: {
                    json: vi.fn().mockResolvedValue({
                        prompt: 'Create a beautiful design',
                        imageUrls: [],
                        source: 'web' // Request body source
                    })
                },
                json: vi.fn().mockImplementation((data) => ({
                    status: 200,
                    json: () => Promise.resolve(data)
                }))
            };

            const response = await endpoint.handle(mockContext as any);
            
            expect(response.status).toBe(200);
            
            // Should use request body source
            const { doCreateTask } = await import('../../src/service/task/create-task');
            expect(doCreateTask).toHaveBeenCalledWith(
                expect.objectContaining({
                    source: 'web'
                }),
                mockEnv
            );
        });

        it('should default to canva source when neither JWT nor request body has source', async () => {
            mockContext = {
                env: mockEnv,
                get: vi.fn((key: string) => {
                    if (key === 'jwtPayload') {
                        return {
                            uid: 'test-user',
                            accountId: 'test-account',
                            // No source in JWT
                            app: 'chat-2-design'
                        };
                    }
                }),
                req: {
                    json: vi.fn().mockResolvedValue({
                        prompt: 'Create a beautiful design',
                        imageUrls: []
                        // No source in request body
                    })
                },
                json: vi.fn().mockImplementation((data) => ({
                    status: 200,
                    json: () => Promise.resolve(data)
                }))
            };

            const response = await endpoint.handle(mockContext as any);
            
            expect(response.status).toBe(200);
            
            // Should default to canva
            const { doCreateTask } = await import('../../src/service/task/create-task');
            expect(doCreateTask).toHaveBeenCalledWith(
                expect.objectContaining({
                    source: 'canva'
                }),
                mockEnv
            );
        });
    });

    describe('Error Handling', () => {
        it('should handle invalid request body gracefully', async () => {
            mockContext = {
                env: mockEnv,
                get: vi.fn((key: string) => {
                    if (key === 'jwtPayload') {
                        return {
                            source: 'canva',
                            app: 'chat-2-design'
                        };
                    }
                }),
                req: {
                    json: vi.fn().mockRejectedValue(new Error('Invalid JSON'))
                },
                json: vi.fn().mockImplementation((data) => ({
                    status: 400,
                    json: () => Promise.resolve(data)
                }))
            };

            const response = await endpoint.handle(mockContext as any);
            
            expect(response.status).toBe(400);
        });

        it('should handle task creation failure', async () => {
            // Mock task creation failure
            const { doCreateTask } = await import('../../src/service/task/create-task');
            (doCreateTask as any).mockRejectedValueOnce(new Error('Task creation failed'));

            mockContext = {
                env: mockEnv,
                get: vi.fn((key: string) => {
                    if (key === 'jwtPayload') {
                        return {
                            source: 'canva',
                            app: 'chat-2-design'
                        };
                    }
                }),
                req: {
                    json: vi.fn().mockResolvedValue({
                        prompt: 'Create a beautiful design',
                        imageUrls: []
                    })
                },
                json: vi.fn().mockImplementation((data) => ({
                    status: 500,
                    json: () => Promise.resolve(data)
                }))
            };

            const response = await endpoint.handle(mockContext as any);
            
            expect(response.status).toBe(500);
        });
    });
});