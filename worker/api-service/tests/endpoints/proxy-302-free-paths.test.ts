import { describe, it, expect, beforeEach, vi } from 'vitest';
import { Proxy302 } from '../../src/endpoints/proxy-302';
import { createMockApiEnv } from '../setup';

// Mock dependencies
vi.mock('../../src/service/task/create-task', () => ({
    doCreateTask: vi.fn().mockResolvedValue({
        taskId: 'test-task-123'
    })
}));

vi.mock('../../src/config/free-paths', () => ({
    isFree302Path: vi.fn((path: string) => path.includes('/v1/chat/completions'))
}));

vi.mock('nanoid', () => ({
    nanoid: vi.fn().mockReturnValue('test-task-123')
}));

// Mock global fetch
global.fetch = vi.fn();

describe('Proxy302 Endpoint - Free Paths', () => {
    let endpoint: Proxy302;
    let mockEnv: any;
    let mockContext: any;

    beforeEach(() => {
        endpoint = new Proxy302();
        mockEnv = createMockApiEnv();
        mockEnv['302_API_KEY'] = 'test-api-key';
        
        vi.clearAllMocks();
    });

    describe('Free Path Handling', () => {
        it('should not create task for free path POST request', async () => {
            // Mock successful API response
            (global.fetch as any).mockResolvedValueOnce({
                ok: true,
                json: async () => ({ message: 'Success', choices: [{ message: { content: 'Test response' } }] })
            });

            mockContext = {
                env: mockEnv,
                get: vi.fn((key: string) => {
                    if (key === 'jwtPayload') {
                        return {
                            uid: 'test-user',
                            accountId: 'test-account'
                        };
                    }
                }),
                req: {
                    method: 'POST',
                    query: vi.fn().mockReturnValue('path=/v1/chat/completions'),
                    header: vi.fn().mockReturnValue('Bearer test-token'),
                    json: vi.fn().mockResolvedValue({
                        model: 'gpt-3.5-turbo',
                        messages: [{ role: 'user', content: 'Hello' }]
                    })
                },
                json: vi.fn().mockImplementation((data) => ({
                    status: 200,
                    json: () => Promise.resolve(data)
                }))
            };

            const response = await endpoint.handle(mockContext as any);
            
            // Should succeed
            expect(response.status).toBe(200);
            
            // Should call 302.ai API
            expect(global.fetch).toHaveBeenCalledWith(
                'https://api.302.ai/v1/chat/completions',
                expect.objectContaining({
                    method: 'POST',
                    headers: expect.any(Headers)
                })
            );
            
            // Should NOT create a billing task for free path
            const { doCreateTask } = await import('../../src/service/task/create-task');
            expect(doCreateTask).not.toHaveBeenCalled();
        });

        it('should not create task for free path GET request', async () => {
            // Mock successful API response
            (global.fetch as any).mockResolvedValueOnce({
                ok: true,
                json: async () => ({ data: 'test response' })
            });

            mockContext = {
                env: mockEnv,
                get: vi.fn((key: string) => {
                    if (key === 'jwtPayload') {
                        return {
                            uid: 'test-user',
                            accountId: 'test-account'
                        };
                    }
                }),
                req: {
                    method: 'GET',
                    query: vi.fn().mockReturnValue('path=/v1/chat/completions'),
                    header: vi.fn().mockReturnValue('Bearer test-token'),
                    json: vi.fn().mockResolvedValue({})

                },
                json: vi.fn().mockImplementation((data) => ({
                    status: 200,
                    json: () => Promise.resolve(data)
                }))
            };

            const response = await endpoint.handle(mockContext as any);
            
            // Should succeed
            expect(response.status).toBe(200);
            
            // Should call 302.ai API
            expect(global.fetch).toHaveBeenCalledWith(
                'https://api.302.ai/v1/chat/completions',
                expect.objectContaining({
                    method: 'GET',
                    headers: expect.any(Headers)
                })
            );
            
            // Should NOT create a billing task for free path
            const { doCreateTask } = await import('../../src/service/task/create-task');
            expect(doCreateTask).not.toHaveBeenCalled();
        });

        it('should create task for non-free path POST request', async () => {
            // Mock successful API response
            (global.fetch as any).mockResolvedValueOnce({
                ok: true,
                json: async () => ({ data: 'test response' })
            });

            mockContext = {
                env: mockEnv,
                get: vi.fn((key: string) => {
                    if (key === 'jwtPayload') {
                        return {
                            uid: 'test-user',
                            accountId: 'test-account'
                        };
                    }
                }),
                req: {
                    method: 'POST',
                    query: vi.fn().mockReturnValue('path=/v1/embeddings'),
                    header: vi.fn().mockReturnValue('Bearer test-token'),
                    json: vi.fn().mockResolvedValue({
                        model: 'text-embedding-ada-002',
                        input: 'test text'
                    })
                },
                json: vi.fn().mockImplementation((data) => ({
                    status: 200,
                    json: () => Promise.resolve(data)
                }))
            };

            const response = await endpoint.handle(mockContext as any);
            
            // Should succeed
            expect(response.status).toBe(200);
            
            // Should call 302.ai API
            expect(global.fetch).toHaveBeenCalledWith(
                'https://api.302.ai/v1/embeddings',
                expect.objectContaining({
                    method: 'POST',
                    headers: expect.any(Headers)
                })
            );
            
            // Should create a billing task for non-free path
            const { doCreateTask } = await import('../../src/service/task/create-task');
            expect(doCreateTask).toHaveBeenCalledWith(
                expect.objectContaining({
                    app: '302',
                    params: expect.objectContaining({
                        path: '/v1/embeddings'
                    })
                }),
                mockEnv
            );
        });

        it('should not create task for non-free path GET request', async () => {
            // Mock successful API response - return raw response for GET
            const mockResponse = new Response(JSON.stringify({ data: 'test response' }), {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            });
            (global.fetch as any).mockResolvedValueOnce(mockResponse);

            mockContext = {
                env: mockEnv,
                get: vi.fn((key: string) => {
                    if (key === 'jwtPayload') {
                        return {
                            uid: 'test-user',
                            accountId: 'test-account'
                        };
                    }
                }),
                req: {
                    method: 'GET',
                    query: vi.fn().mockReturnValue('path=/v1/models'),
                    header: vi.fn().mockReturnValue('Bearer test-token'),
                    json: vi.fn().mockResolvedValue({})

                }
            };

            const response = await endpoint.handle(mockContext as any);
            
            // Should return the raw response for GET requests
            expect(response).toBe(mockResponse);
            
            // Should call 302.ai API
            expect(global.fetch).toHaveBeenCalledWith(
                'https://api.302.ai/v1/models',
                expect.objectContaining({
                    method: 'GET',
                    headers: expect.any(Headers)
                })
            );
            
            // Should NOT create a billing task for GET request
            const { doCreateTask } = await import('../../src/service/task/create-task');
            expect(doCreateTask).not.toHaveBeenCalled();
        });
    });

    describe('API Key Handling', () => {
        it('should return error when 302_API_KEY is missing', async () => {
            // Remove API key from environment
            delete mockEnv['302_API_KEY'];

            mockContext = {
                env: mockEnv,
                get: vi.fn((key: string) => {
                    if (key === 'jwtPayload') {
                        return {
                            uid: 'test-user',
                            accountId: 'test-account'
                        };
                    }
                }),
                req: {
                    method: 'POST',
                    query: vi.fn().mockReturnValue('path=/v1/chat/completions'),
                    header: vi.fn().mockReturnValue('Bearer test-token'),
                    json: vi.fn().mockResolvedValue({})
                },
                json: vi.fn().mockImplementation((data) => ({
                    status: 500,
                    json: () => Promise.resolve(data)
                }))
            };

            const response = await endpoint.handle(mockContext as any);
            
            expect(response.status).toBe(500);
        });

        it('should forward request with correct authorization header', async () => {
            // Mock successful API response
            (global.fetch as any).mockResolvedValueOnce({
                ok: true,
                json: async () => ({ data: 'test response' })
            });

            mockContext = {
                env: mockEnv,
                get: vi.fn((key: string) => {
                    if (key === 'jwtPayload') {
                        return {
                            uid: 'test-user',
                            accountId: 'test-account'
                        };
                    }
                }),
                req: {
                    method: 'POST',
                    query: vi.fn().mockReturnValue('path=/v1/chat/completions'),
                    header: vi.fn().mockReturnValue('Bearer original-token'),
                    json: vi.fn().mockResolvedValue({})
                },
                json: vi.fn().mockImplementation((data) => ({
                    status: 200,
                    json: () => Promise.resolve(data)
                }))
            };

            await endpoint.handle(mockContext as any);
            
            // Should call 302.ai API with correct authorization header
            expect(global.fetch).toHaveBeenCalledWith(
                'https://api.302.ai/v1/chat/completions',
                expect.objectContaining({
                    headers: expect.objectContaining({
                        get: expect.any(Function)
                    })
                })
            );

            // Verify the Authorization header was set correctly
            const fetchCall = (global.fetch as any).mock.calls[0];
            const headers = fetchCall[1].headers;
            expect(headers.get('Authorization')).toBe(`Bearer ${mockEnv['302_API_KEY']}`);
        });
    });

    describe('Error Handling', () => {
        it('should handle missing path parameter', async () => {
            mockContext = {
                env: mockEnv,
                get: vi.fn((key: string) => {
                    if (key === 'jwtPayload') {
                        return {
                            uid: 'test-user',
                            accountId: 'test-account'
                        };
                    }
                }),
                req: {
                    method: 'POST',
                    query: vi.fn().mockReturnValue(''), // No path
                    header: vi.fn().mockReturnValue('Bearer test-token'),
                    json: vi.fn().mockResolvedValue({})
                },
                json: vi.fn().mockImplementation((data) => ({
                    status: 400,
                    json: () => Promise.resolve(data)
                }))
            };

            const response = await endpoint.handle(mockContext as any);
            
            expect(response.status).toBe(400);
        });

        it('should handle 302.ai API errors', async () => {
            // Mock API error response
            (global.fetch as any).mockResolvedValueOnce({
                ok: false,
                status: 429,
                headers: new Headers({ 'Content-Type': 'application/json' }),
                text: async () => JSON.stringify({ error: 'Rate limit exceeded' })
            });

            mockContext = {
                env: mockEnv,
                get: vi.fn((key: string) => {
                    if (key === 'jwtPayload') {
                        return {
                            uid: 'test-user',
                            accountId: 'test-account'
                        };
                    }
                }),
                req: {
                    method: 'POST',
                    query: vi.fn().mockReturnValue('path=/v1/chat/completions'),
                    header: vi.fn().mockReturnValue('Bearer test-token'),
                    json: vi.fn().mockResolvedValue({})
                },
                json: vi.fn().mockImplementation((data) => ({
                    status: 200,
                    json: async () => data
                }))

            };

            const response = await endpoint.handle(mockContext as any);
            
            // Should forward the error response
            expect(response.status).toBe(429);
        });

        it('should handle network errors gracefully', async () => {
            // Mock network error
            (global.fetch as any).mockRejectedValueOnce(new Error('Network error'));

            mockContext = {
                env: mockEnv,
                get: vi.fn((key: string) => {
                    if (key === 'jwtPayload') {
                        return {
                            uid: 'test-user',
                            accountId: 'test-account'
                        };
                    }
                }),
                req: {
                    method: 'POST',
                    query: vi.fn().mockReturnValue('path=/v1/chat/completions'),
                    header: vi.fn().mockReturnValue('Bearer test-token'),
                    json: vi.fn().mockResolvedValue({})
                },
                json: vi.fn().mockImplementation((data) => ({
                    status: 500,
                    json: () => Promise.resolve(data)
                }))
            };

            const response = await endpoint.handle(mockContext as any);
            
            expect(response.status).toBe(500);
        });
    });
});