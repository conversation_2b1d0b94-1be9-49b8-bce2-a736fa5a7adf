# API Service Architecture

```mermaid
graph TB
    subgraph Client
        C[Client Applications]
    end

    subgraph <PERSON><PERSON>lareWorker["Cloudflare Worker (api-service)"]
        Router[API Router]
        
        subgraph Middlewares
            Auth[Auth Middleware]
            Credit[Credit Check]
            CORS[CORS Handler]
        end
        
        subgraph CoreServices["Core Services"]
            TaskQueue[Task Queue Handler]
            Schedule[Scheduled Tasks]
            FileProcess[File Processing]
        end
        
        subgraph Endpoints["API Endpoints"]
            Auth_EP[Authentication APIs]
            File_EP[File Processing APIs]
            Credit_EP[Credit Management]
            Task_EP[Task Management]
        end
    end

    subgraph Infrastructure["Infrastructure"]
        DO[Durable Objects]
        KV[KV Storage]
        Queue[Queue Service]
        Supabase[(Supabase DB)]
    end

    subgraph ExternalServices["External Services"]
        Stripe[Stripe Payment]
        AI[AI Services]
    end

    %% Connections
    C -->|HTTP/WebSocket| Router
    Router --> Middlewares
    Middlewares --> Endpoints
    Endpoints --> CoreServices
    CoreServices --> Infrastructure
    Credit_EP --> Stripe
    FileProcess --> AI
    TaskQueue --> Queue
    CoreServices --> Supabase
```

## Component Description

### Client Layer
- **Client Applications**: External applications that interact with the API service

### Cloudflare Worker Layer
- **API Router**: Main entry point for all HTTP requests
- **Middlewares**: 
  - Authentication
  - Credit verification
  - CORS handling
- **Core Services**:
  - Task queue processing
  - Scheduled tasks
  - File processing logic
- **API Endpoints**:
  - Authentication (token exchange, API keys)
  - File processing
  - Credit management
  - Task status and management

### Infrastructure Layer
- **Durable Objects**: State management
- **KV Storage**: Key-value data storage
- **Queue Service**: Async task processing
- **Supabase**: Primary database

### External Services
- **Stripe**: Payment processing
- **AI Services**: External AI processing services

## Task State Flow

```mermaid
stateDiagram-v2
    [*] --> WAITING: Task Created
    WAITING --> PROCESSING: Start Processing
    PROCESSING --> SUCCESS: Task Completed Successfully
    PROCESSING --> FINISHED: Task Completed
    PROCESSING --> FAILED: Task Failed
    WAITING --> FAILED: Task Failed
    WAITING --> CANCEL: User Cancelled
    PROCESSING --> CANCEL: User Cancelled
    SUCCESS --> [*]
    FINISHED --> [*]
    FAILED --> [*]
    CANCEL --> [*]

    note right of WAITING
        Initial state when task is created
        Credit pre-deducted
    end note

    note right of PROCESSING
        Task is being processed by
        external services
    end note

    note right of SUCCESS
        Task completed successfully
        Credit deduction confirmed
    end note

    note right of FINISHED
        Task completed
        Credit deduction confirmed
    end note

    note right of FAILED
        Task failed
        Credit refunded
    end note

    note right of CANCEL
        Task cancelled by user
        Credit refunded
    end note
```

### Task State Descriptions

1. **WAITING**
   - Initial state when task is created
   - Credits are pre-deducted
   - Task is queued for processing

2. **PROCESSING**
   - Task is being processed by external services
   - System periodically checks task status
   - Can transition to SUCCESS, FINISHED, FAILED, or CANCEL

3. **SUCCESS**
   - Task completed successfully
   - Final state
   - Credit deduction is confirmed

4. **FINISHED**
   - Task completed (alternative success state)
   - Final state
   - Credit deduction is confirmed

5. **FAILED**
   - Task processing failed
   - Final state
   - Credits are refunded to user
   - Error message is stored

6. **CANCEL**
   - Task cancelled by user
   - Final state
   - Credits are refunded to user

### State Transition Rules

1. **Terminal States**
   - SUCCESS, FINISHED, FAILED, and CANCEL are terminal states
   - Once a task reaches a terminal state, no further transitions are possible

2. **Updatable States**
   - WAITING and PROCESSING are updatable states
   - System can poll and update task status in these states

3. **Credit Management States**
   - Credit pre-deduction occurs in WAITING state
   - Credit deduction is confirmed in SUCCESS and FINISHED states
   - Credits are refunded in FAILED and CANCEL states

4. **Timeout Handling**
   - Tasks in WAITING or PROCESSING states are monitored for timeout
   - If timeout occurs, task is marked as FAILED
   - System performs automatic cleanup of stale tasks
