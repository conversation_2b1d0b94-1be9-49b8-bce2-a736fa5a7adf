#!/bin/bash

# Run tests in smaller batches to avoid timeouts
echo "Running tests in batches..."

# Run config tests
echo "Running config tests..."
npx vitest run tests/config/*.test.ts --reporter=dot

# Run utils tests
echo "Running utils tests..."
npx vitest run tests/utils/*.test.ts --reporter=dot

# Run service tests
echo "Running service tests..."
npx vitest run tests/service/*.test.ts --reporter=dot

# Run middleware tests
echo "Running middleware tests..."
npx vitest run tests/middlewares/*.test.ts --reporter=dot

# Run endpoint tests
echo "Running endpoint tests..."
npx vitest run tests/endpoints/*.test.ts --reporter=dot

# Run integration tests
echo "Running integration tests..."
npx vitest run tests/integration/*.test.ts --reporter=dot

echo "All tests completed!"