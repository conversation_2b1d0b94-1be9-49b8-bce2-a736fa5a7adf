import { defineConfig } from 'vitest/config'
import { resolve } from 'path'

export default defineConfig({
  test: {
    environment: 'node',
    globals: true,
    coverage: {
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'dist/',
        '**/*.d.ts',
        '**/*.config.{js,ts}',
        '**/wrangler.*.toml',
      ],
    },
    setupFiles: ['./test/setup.ts'],
  },
  resolve: {
    alias: {
      '@api': resolve(__dirname, './worker/api-service/src'),
      '@auth': resolve(__dirname, './worker/auth-service/src'),
      '@shared': resolve(__dirname, './shared'),
    },
  },
})