# Testing Guide

A1D API服务的完整测试指南，包含单元测试、集成测试和Git集成。

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 运行测试
```bash
# 运行所有测试
npm test

# 运行特定服务测试
npm run test:api-service     # API服务测试
npm run test:auth-service    # 认证服务测试

# 监听模式运行测试
npm run test:watch

# 生成覆盖率报告
npm run test:coverage

# 运行完整的质量检查（推荐在提交前运行）
./test-runner.sh
```

## 📋 测试框架

### 技术栈
- **Vitest**: 现代化的测试框架，支持Cloudflare Workers
- **@cloudflare/vitest-pool-workers**: Cloudflare Workers专用测试池
- **MSW**: 用于模拟外部API调用
- **TypeScript**: 完整的类型支持

### 配置文件
- `vitest.config.ts` - 根级别配置
- `worker/api-service/vitest.config.ts` - API服务专用配置
- `worker/auth-service/vitest.config.ts` - 认证服务专用配置

## 🗂 测试结构

```
tests/
├── endpoints/           # API端点测试
│   ├── image-generator.test.ts
│   ├── image-upscaler.test.ts
│   ├── credit.test.ts
│   └── ...
├── middlewares/         # 中间件测试
│   ├── auth.test.ts
│   └── credit.test.ts
├── service/            # 服务层测试
│   ├── calculate-credits.test.ts
│   ├── task-service.test.ts
│   └── ...
├── utils/              # 工具函数测试
│   ├── fal-api.test.ts
│   ├── task-utils.test.ts
│   └── ...
└── setup.ts           # 测试配置和模拟
```

## 🧪 测试类型

### 1. 单元测试
测试独立的函数和方法：

```typescript
import { describe, it, expect } from 'vitest'

describe('Credit Calculation', () => {
  it('should calculate credits for image upscaler', () => {
    const result = calculateCredits('iu', { scale: 2 })
    expect(result).toBe(15)
  })
})
```

### 2. API端点测试
测试HTTP端点的请求/响应：

```typescript
import { describe, it, expect } from 'vitest'

describe('Image Generator Endpoint', () => {
  it('should validate required parameters', async () => {
    const response = await request('/image-generator', {
      method: 'POST',
      body: JSON.stringify({ prompt: 'test' })
    })
    expect(response.status).toBe(200)
  })
})
```

### 3. 中间件测试
测试认证、授权等中间件：

```typescript
describe('Auth Middleware', () => {
  it('should reject invalid tokens', async () => {
    const result = await authMiddleware(invalidToken)
    expect(result.success).toBe(false)
  })
})
```

## 🔧 模拟和配置

### 环境变量模拟
测试中使用的环境变量在`setup.ts`中配置：

```typescript
export function createMockApiEnv() {
  return {
    JWT_SECRET: 'test-secret',
    SUPABASE_URL: 'https://test.supabase.co',
    FAL_API_KEY: 'test-fal-key',
    // ... 其他环境变量
  }
}
```

### 外部服务模拟
使用MSW模拟外部API调用：

```typescript
import { rest } from 'msw'

const handlers = [
  rest.post('https://fal.run/fal-ai/submit', (req, res, ctx) => {
    return res(ctx.json({ task_id: '123' }))
  })
]
```

## 🎯 测试指南

### 编写好的测试
1. **明确的测试名称**: 描述测试的具体行为
2. **AAA模式**: Arrange（准备）、Act（执行）、Assert（断言）
3. **独立性**: 每个测试应该独立运行
4. **覆盖边界情况**: 测试正常情况和异常情况

### 示例：完整的测试用例
```typescript
describe('Task Creation Service', () => {
  beforeEach(() => {
    // 清理和初始化
    vi.clearAllMocks()
  })

  it('should create task successfully with valid parameters', async () => {
    // Arrange
    const taskData = {
      app: 'iu',
      userId: 123,
      parameters: { scale: 2 }
    }

    // Act
    const result = await createTask(taskData)

    // Assert
    expect(result.success).toBe(true)
    expect(result.data.taskId).toBeDefined()
    expect(result.data.status).toBe('WAITING')
  })

  it('should handle insufficient credits', async () => {
    // Arrange
    const taskData = { app: 'iu', userId: 123 }
    mockGetUserCredits.mockResolvedValue(0)

    // Act & Assert
    await expect(createTask(taskData)).rejects.toThrow('Insufficient credits')
  })
})
```

## 🔄 Git集成

### Pre-commit Hooks
项目配置了pre-commit hooks，在每次提交前自动运行：

1. **ESLint**: 代码风格检查和修复
2. **TypeScript**: 类型检查
3. **单元测试**: 运行所有测试
4. **覆盖率检查**: 确保测试覆盖率

### CI/CD流程
GitHub Actions会在以下情况运行测试：

- 推送到`main`、`develop`、`test`分支
- 创建Pull Request
- 部署前验证

### 绕过测试（不推荐）
如果确实需要跳过测试（仅限紧急情况）：

```bash
git commit --no-verify -m "紧急修复"
```

## 📊 覆盖率报告

运行覆盖率测试：
```bash
npm run test:coverage
```

覆盖率报告会生成在`coverage/`目录下，包含：
- HTML报告：`coverage/index.html`
- LCOV文件：`coverage/lcov.info`

### 覆盖率目标
- **API端点**: >85%
- **业务逻辑**: >90%
- **工具函数**: >80%
- **中间件**: >90%

## 🐛 调试测试

### 调试失败的测试
```bash
# 运行特定测试文件
npm test tests/service/calculate-credits.test.ts

# 运行特定测试用例
npm test -- --grep "should calculate credits"

# 详细输出
npm test -- --verbose
```

### 常见问题

1. **测试超时**
   ```bash
   # 增加超时时间
   npm test -- --timeout 30000
   ```

2. **环境变量问题**
   检查`setup.ts`中的mock配置

3. **异步测试问题**
   确保使用`async/await`或返回Promise

## 📝 最佳实践

### DO ✅
- 为每个API端点编写测试
- 测试错误处理逻辑
- 使用有意义的测试数据
- 保持测试简单和专注
- 定期运行完整测试套件

### DON'T ❌
- 不要测试第三方库的功能
- 不要忽略异步操作
- 不要使用真实的外部服务
- 不要让测试相互依赖
- 不要跳过边界情况测试

## 🤝 贡献指南

添加新功能时，请确保：

1. 为新的API端点添加测试
2. 为新的业务逻辑添加单元测试
3. 更新相关的mock配置
4. 运行完整测试套件
5. 检查覆盖率报告

## 📞 支持

如果遇到测试相关问题：

1. 检查控制台错误信息
2. 查看测试日志
3. 确认环境配置
4. 查阅相关文档

运行`./test-runner.sh`获取详细的测试状态报告。