name: Test Suite

on:
  push:
    branches: [ main, develop, test ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x, 20.x]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Generate Cloudflare Worker types
      run: npm run cf-typegen

    - name: Run ESLint
      run: npm run lint

    - name: Run TypeScript type check
      run: npm run type-check

    - name: Run API Service tests
      run: npm run test:api-service

    - name: Run Auth Service tests
      run: npm run test:auth-service

    - name: Run tests with coverage
      run: npm run test:coverage

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        files: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

  deploy-test:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/test' && github.event_name == 'push'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Deploy to test environment
      run: |
        echo "Deploying to test environment..."
        # Deployment commands would go here
        # wrangler deploy --config worker/api-service/wrangler.test.toml
        # wrangler deploy --config worker/auth-service/wrangler.test.toml

  deploy-prod:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # Production deployment commands would go here
        # wrangler deploy --config worker/api-service/wrangler.prod.toml
        # wrangler deploy --config worker/auth-service/wrangler.prod.toml