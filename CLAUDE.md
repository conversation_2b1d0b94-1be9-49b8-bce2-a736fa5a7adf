# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Root level commands
- `npm install` - Install dependencies for both services
- `npm run cf-typegen` - Generate TypeScript types for Cloudflare Workers

### Testing Commands
- `npm test` - Run all tests with Vitest
- `npm run test:api-service` - Run API service tests only
- `npm run test:auth-service` - Run auth service tests only
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage report
- `./test-runner.sh` - Run complete test suite with quality checks

### Code Quality Commands
- `npm run lint` - Run ESLint and fix issues
- `npm run type-check` - Run TypeScript type checking
- `npm run pre-commit` - Run pre-commit checks (lint-staged)

### Service-specific commands
Navigate to `worker/api-service/` or `worker/auth-service/` for service-specific operations:
- `wrangler dev` - Start local development server
- `wrangler deploy` - Deploy to production (handled by CI/CD)
- `wrangler types` - Generate worker types

### Configuration
Each service has environment-specific wrangler config files:
- `wrangler.toml` - Local development
- `wrangler.test.toml` - Test environment  
- `wrangler.prod.toml` - Production environment

## Architecture Overview

This is a Cloudflare Workers-based API service with two main components:

### api-service
Core API functionality handling:
- Image processing (upscaling, background removal, vectorization, generation)
- Video processing (upscaling)
- AI services (SpeedPainter, text detection)
- Task management with queue processing
- Credit system and payment integration
- File upload and storage

### auth-service  
Authentication service providing:
- Token exchange (Supabase to internal JWT)
- API key generation and management
- Multi-provider authentication (Supabase, Canva, JWT)

## Key Architectural Patterns

### Task State Machine
Tasks follow this state flow: WAITING → PROCESSING → SUCCESS/FINISHED/FAILED/CANCEL

- **WAITING**: Credits pre-deducted, task queued
- **PROCESSING**: External service processing
- **SUCCESS/FINISHED**: Completed successfully, credits confirmed
- **FAILED/CANCEL**: Failed or cancelled, credits refunded

### Infrastructure Components
- **Queue**: Background task processing with batching (5 messages, 5s timeout)
- **KV Storage**: Fast access cache for task data
- **Hyperdrive**: Database connection acceleration
- **R2**: Object storage for files
- **Supabase**: Primary PostgreSQL database

### Code Organization
```
src/
├── endpoints/        # API handlers organized by feature
├── middlewares/      # Auth, credit checks, CORS
├── service/          # Business logic layers
│   ├── app/         # Application services
│   ├── credit/      # Credit calculation system
│   ├── supabase/    # Database operations
│   └── task/        # Task management
├── utils/           # Utility functions
├── index.ts         # Main entry point with Hono router
├── queue.ts         # Queue message processing
└── scheduled.ts     # Scheduled tasks (cleanup, analysis)
```

### Framework Usage
- **Hono**: Web framework with OpenAPI integration via `chanfana`
- **Zod**: Schema validation for request/response
- **JWT**: Token authentication with multiple providers
- **TypeScript**: Full type safety with worker bindings

## Development Guidelines

### Authentication Flow
1. External tokens → auth-service → internal JWT
2. API keys generated through auth-service
3. All requests require Bearer token authentication

### Error Handling
Use consistent error format:
```typescript
return c.json({
  success: false,
  error: {
    message: 'Error description',
    code: 'ERROR_CODE'
  }
}, statusCode);
```

### Task Creation Pattern
1. Validate request and check credits
2. Create task record in database
3. Pre-deduct credits from user
4. Queue task for background processing
5. Return task ID for status tracking

### Credit System
- Credits pre-deducted on task creation
- Confirmed on successful completion
- Refunded on failure or cancellation
- Dynamic calculation based on task parameters

## Scheduled Operations
- Task cleanup: Every 5 minutes (`*/5 * * * *`)
- Task analysis: Daily at 1 AM (`0 1 * * *`)

## Testing Framework

### Overview
This project uses **Vitest** with Cloudflare Workers pool for testing. All tests must pass before code can be committed.

### Test Structure
```
tests/
├── endpoints/        # API endpoint tests
├── middlewares/      # Middleware tests  
├── service/          # Business logic tests
├── utils/           # Utility function tests
└── setup.ts         # Test configuration and mocks
```

### Testing Guidelines
1. **Unit Tests**: Test individual functions and methods
2. **Integration Tests**: Test API endpoints with mocked dependencies
3. **Mock External Services**: Use MSW or manual mocks for external APIs
4. **Coverage**: Aim for >80% code coverage on critical paths
5. **Performance**: Test credit calculation and task processing logic

### Key Test Files
- `tests/endpoints/image-generator.test.ts` - Image generation API tests
- `tests/middlewares/auth.test.ts` - Authentication middleware tests
- `tests/service/calculate-credits.test.ts` - Credit calculation tests
- `tests/service/task-service.test.ts` - Task management tests
- `tests/utils/fal-api.test.ts` - External API client tests
- `tests/utils/task-utils.test.ts` - Task utility function tests

### Git Hooks
- **Pre-commit**: Automatically runs linting, type checking, and tests
- **CI/CD**: GitHub Actions runs full test suite on push/PR
- **Deployment**: Tests must pass before deployment to test/prod environments

### Running Tests Locally
```bash
# Install dependencies
npm install

# Run all tests
npm test

# Run specific service tests
npm run test:api-service
npm run test:auth-service

# Run with coverage
npm run test:coverage

# Run complete quality check
./test-runner.sh
```