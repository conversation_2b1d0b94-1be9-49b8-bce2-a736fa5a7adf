# Token Validation Error Codes

本文档描述了在验证 JWT token 时可能遇到的所有错误类型及其含义。

## 错误结构

当 token 验证失败时，API 将返回一个 401 状态码的响应，响应体格式如下：

```json
{
    "error": "错误描述信息",
    "code": "错误代码"
}
```

## 错误代码列表

### JWT_SECRET_MISSING
- **描述**: JWT 密钥未配置
- **原因**: 服务器端环境变量中未设置 JWT_SECRET
- **解决方案**: 请确保服务器正确配置了 JWT_SECRET 环境变量

### TOKEN_EXPIRED
- **描述**: Token 已过期
- **原因**: Token 的有效期已经超过了预设时间（默认7天）
- **解决方案**: 客户端需要重新调用 token exchange 接口获取新的 token

### INVALID_SIGNATURE
- **描述**: Token 签名无效
- **原因**: Token 可能被篡改或使用了错误的密钥签名
- **解决方案**: 确保使用正确的 token，如有疑问请重新获取

### MALFORMED_TOKEN
- **描述**: Token 格式错误
- **原因**: 提供的 token 不符合 JWT 标准格式
- **解决方案**: 检查 token 格式是否完整，确保包含 header、payload 和 signature 三个部分

### INVALID_TOKEN
- **描述**: Token 格式无效
- **原因**: Token 的内容不符合预期格式或缺少必要字段
- **解决方案**: 确保使用正确的 token 格式和必要的 payload 字段

### UNKNOWN_ERROR
- **描述**: 未知验证错误
- **原因**: 发生了未预期的验证错误
- **解决方案**: 检查日志获取详细错误信息，必要时联系技术支持

### VERIFICATION_FAILED
- **描述**: Token 验证失败
- **原因**: Token 验证过程中发生一般性错误
- **解决方案**: 检查 token 是否有效，必要时重新获取

## 处理建议

1. 当收到 `TOKEN_EXPIRED` 错误时，客户端应该自动尝试刷新 token
2. 对于 `INVALID_SIGNATURE` 和 `MALFORMED_TOKEN`，建议直接重新登录获取新 token
3. 如遇到 `JWT_SECRET_MISSING` 错误，请联系系统管理员
4. 其他错误类型通常需要开发人员介入调查

## 示例代码

### 错误处理示例（TypeScript）

```typescript
async function handleTokenError(response: Response) {
    const error = await response.json();
    switch (error.code) {
        case 'TOKEN_EXPIRED':
            // 重新获取 token
            return await refreshToken();
        case 'INVALID_SIGNATURE':
        case 'MALFORMED_TOKEN':
            // 重定向到登录页面
            redirectToLogin();
            break;
        default:
            // 显示错误信息
            showError(error.message);
    }
}
```

## 注意事项

1. 所有 token 验证错误都会返回 401 状态码
2. 建议在生产环境中不要展示详细的错误信息给最终用户
3. 确保所有 token 相关的操作都在 HTTPS 环境下进行
4. 定期检查日志中的 token 错误，及时发现潜在问题