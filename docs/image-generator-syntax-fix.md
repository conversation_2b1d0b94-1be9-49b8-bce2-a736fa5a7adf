# Image Generator 语法修复文档

## 问题描述

在 `image-generator.ts` 文件中发现了多个语法错误：

1. **Switch 语句缺少 case 标签**: switch 语句直接包含 if 语句，没有 case 标签
2. **类型定义错误**: 函数参数使用了 `Env` 类型而不是正确的 `Bindings` 类型
3. **缺少类型导入**: 没有导入 `Bindings` 类型

## 错误详情

### 1. Switch 语句语法错误

**错误代码:**
```typescript
switch (task.source) {
  
    if (checkpoint == 'recraft-v3') {
        // ... 代码逻辑
    }
    // ...
    
    default:
        throw new Error(`Unsupported source: ${task.source}`);
}
```

**问题**: switch 语句缺少 case 标签，直接包含 if 语句

### 2. 类型定义错误

**错误代码:**
```typescript
export async function doCreateImageGeneratorTask(task: TaskInfoRequest, env: Env): Promise<TaskInfoResult>
export async function doGetRecraftV3ImageGeneratorTask(taskId: string, env: Env): Promise<TaskInfoResult>
```

**问题**: 使用了未定义的 `Env` 类型

### 3. 缺少类型导入

**错误代码:**
```typescript
import { SOURCE } from "../../types";
```

**问题**: 没有导入 `Bindings` 类型

## 解决方案

### 1. 修复 Switch 语句

添加了正确的 case 标签来处理所有支持的源类型：

```typescript
switch (task.source) {
    case SOURCE.WEB:
    case SOURCE.CANVA:
    case SOURCE.API:
    case SOURCE.FRAMER:
    case SOURCE.FIGMA:
    case SOURCE.RAPIDAPI:
    case SOURCE.MCP:
        if (checkpoint == 'recraft-v3') {
            // ... 代码逻辑
        } else if (checkpoint == 'flux-dev' || checkpoint == 'flux-schnell') {
            // TODO: 待实现
        } else {
            throw new Error(`Unsupported checkpoint: ${checkpoint}`);
        }
        break;

    default:
        throw new Error(`Unsupported source: ${task.source}`);
}
```

### 2. 修复类型定义

将所有函数参数的类型从 `Env` 更改为 `Bindings`：

```typescript
export async function doCreateImageGeneratorTask(task: TaskInfoRequest, env: Bindings): Promise<TaskInfoResult>
export async function doGetRecraftV3ImageGeneratorTask(taskId: string, env: Bindings): Promise<TaskInfoResult>
```

### 3. 添加类型导入

更新导入语句以包含 `Bindings` 类型：

```typescript
import { SOURCE, Bindings } from "../../types";
```

## 修复后的功能

修复后的代码能够正确处理：

1. ✅ **多源支持**: 支持 WEB、CANVA、API、FRAMER、FIGMA、RAPIDAPI、MCP 等多种来源
2. ✅ **Recraft V3**: 完整的 Recraft V3 图片生成功能
3. ✅ **智能尺寸匹配**: 自动匹配预定义尺寸或使用自定义尺寸
4. ✅ **颜色处理**: 支持多种颜色格式的处理
5. ✅ **错误处理**: 正确的错误处理和类型安全

## 代码结构

修复后的代码结构清晰：

```typescript
export async function doCreateImageGeneratorTask(task: TaskInfoRequest, env: Bindings): Promise<TaskInfoResult> {
    // 1. 提取参数
    const { prompt, checkpoint, width, height, colors, style, style_id } = task.params;
    
    // 2. 根据来源进行路由
    switch (task.source) {
        case SOURCE.WEB:
        case SOURCE.CANVA:
        // ... 其他来源
            // 3. 根据模型类型处理
            if (checkpoint === 'recraft-v3') {
                // Recraft V3 处理逻辑
            } else if (checkpoint === 'flux-dev' || checkpoint === 'flux-schnell') {
                // Flux 处理逻辑 (待实现)
            } else {
                throw new Error(`Unsupported checkpoint: ${checkpoint}`);
            }
            break;
            
        default:
            throw new Error(`Unsupported source: ${task.source}`);
    }
}
```

## 相关文件

- `src/service/app/image-generator.ts` - 主要修复文件
- `src/types.ts` - 类型定义文件
- `docs/recraft-v3-api-update.md` - 相关的 Recraft V3 API 更新文档

## 注意事项

1. **类型一致性**: 确保所有函数使用正确的 `Bindings` 类型
2. **Switch 语句**: 新增功能时需要添加相应的 case 标签
3. **错误处理**: 保持一致的错误处理模式
4. **TODO 项目**: Flux 模型的实现仍待完成

## 影响范围

- ✅ **语法修复**: 解决了所有语法错误
- ✅ **类型安全**: 使用正确的类型定义
- ✅ **功能完整**: Recraft V3 功能完全可用
- ✅ **向后兼容**: 保持了现有的 API 接口 