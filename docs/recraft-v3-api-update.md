# Recraft V3 API 更新文档

## 概述

根据 [fal.ai 官方文档](https://fal.ai/models/fal-ai/recraft/v3/text-to-image/api)，我们更新了 `image-generator.ts` 文件以符合最新的 Recraft V3 API 规范。

## 主要变更

### 1. API 端点更新

**之前:**
```typescript
RECRAFT_V3: 'recraft-v3'
```

**现在:**
```typescript
RECRAFT_V3: 'recraft/v3/text-to-image'
```

### 2. 类型定义更新

#### 颜色类型
```typescript
// 更新前
interface Color {
    r: number;
    g: number;
    b: number;
}

// 更新后 - 符合官方文档
interface RGBColor {
    r: number;
    g: number;
    b: number;
}
```

#### 图片尺寸类型
```typescript
// 新增 - 支持预定义尺寸和自定义尺寸
type ImageSize = 
    | 'square_hd' 
    | 'square' 
    | 'portrait_4_3' 
    | 'portrait_16_9' 
    | 'landscape_4_3' 
    | 'landscape_16_9'
    | { width: number; height: number };
```

#### 样式类型
```typescript
// 新增 - 支持官方定义的样式枚举
type StyleEnum = 
    | 'any'
    | 'realistic_image'
    | 'digital_illustration'
    | 'vector_illustration'
    | string; // 支持其他样式值
```

### 3. 请求参数更新

#### 输入参数 (RecraftV3Params)
```typescript
interface RecraftV3Params {
    prompt: string;                    // 必需
    image_size?: ImageSize;           // 可选，支持预定义尺寸
    style?: StyleEnum;                // 可选
    colors?: RGBColor[];              // 可选
    style_id?: string;                // 可选
    enable_safety_checker?: boolean;  // 新增 - 可选
    apiKey?: string;
}
```

#### 响应格式更新
```typescript
// 文件对象格式
interface RecraftV3ImageFile {
    url: string;
    content_type?: string;
    file_name?: string;
    file_size?: number;
    file_data?: string;
}

// 结果格式
interface RecraftV3ImageGeneratorResult {
    images: RecraftV3ImageFile[];
}
```

### 4. 智能尺寸匹配

新增了智能尺寸匹配逻辑，自动将自定义尺寸转换为预定义尺寸（如果匹配）：

```typescript
// 正方形
if (w === h) {
    imageSize = w > 1024 ? 'square_hd' : 'square';
}
// 纵向
else if (w < h) {
    const ratio = h / w;
    if (Math.abs(ratio - 4/3) < 0.1) {
        imageSize = 'portrait_4_3';
    } else if (Math.abs(ratio - 16/9) < 0.1) {
        imageSize = 'portrait_16_9';
    } else {
        imageSize = { width: w, height: h };
    }
}
// 横向
else {
    const ratio = w / h;
    if (Math.abs(ratio - 4/3) < 0.1) {
        imageSize = 'landscape_4_3';
    } else if (Math.abs(ratio - 16/9) < 0.1) {
        imageSize = 'landscape_16_9';
    } else {
        imageSize = { width: w, height: h };
    }
}
```

## API 使用示例

### 基本调用
```bash
curl --request POST \
  --url https://queue.fal.run/fal-ai/recraft/v3/text-to-image \
  --header "Authorization: Key $FAL_KEY" \
  --header "Content-Type: application/json" \
  --data '{
     "prompt": "a red panda eating a bamboo in front of a poster that says \"recraft V3 now available at fal\""
   }'
```

### 完整参数示例
```typescript
const params: RecraftV3Params = {
    prompt: "a beautiful landscape",
    image_size: "landscape_16_9",
    style: "realistic_image",
    colors: [
        { r: 255, g: 0, b: 0 },    // 红色
        { r: 0, g: 255, b: 0 }     // 绿色
    ],
    enable_safety_checker: true
};
```

## 兼容性

- ✅ 向后兼容现有的自定义尺寸参数
- ✅ 自动转换为最优的预定义尺寸
- ✅ 保持现有的错误处理逻辑
- ✅ 使用统一的 FalApiClient 工具类

## 相关文件

- `src/service/app/image-generator.ts` - 主要更新文件
- `src/utils/fal-api.ts` - 更新端点常量
- `docs/fal-api-refactor.md` - fal API 重构文档

## 参考链接

- [Recraft V3 官方文档](https://fal.ai/models/fal-ai/recraft/v3/text-to-image/api#schema-input)
- [fal.ai API 文档](https://fal.ai/models/fal-ai/recraft/v3/text-to-image/api#schema-output) 