# Ideogram API 响应格式适配修复

## 问题描述

在实际测试中发现，Ideogram API 的状态查询响应格式与之前的假设不同：

### 预期的响应格式（错误）
```typescript
{
  status: 'COMPLETED',
  data: {
    images: [...],
    seed: number
  }
}
```

### 实际的响应格式
```typescript
{
  images: [
    {
      url: 'https://v3.fal.media/files/kangaroo/1QGijTbBJHBQ-pET0bs4s_image.png',
      content_type: 'image/png',
      file_name: 'image.png',
      file_size: 1037543
    }
  ],
  seed: 1511551052
}
```

## 关键差异

1. **无状态字段**: 实际响应中没有 `status` 字段
2. **直接结果**: 图片数据直接在根级别，不在 `data` 字段中
3. **任务状态判断**: 需要通过响应内容来判断任务状态
   - 有 `images` 数组 = 任务成功完成
   - 有 `error` 字段 = 任务失败
   - 404 或空响应 = 任务还在处理中

## 解决方案

### 1. 更新类型定义

移除了复杂的状态响应类型，简化为实际的响应格式：

```typescript
// 移除的类型
interface IdeogramStatusResponse {
  status: 'IN_QUEUE' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
  // ...
}

interface IdeogramResultResponse extends IdeogramStatusResponse {
  data?: IdeogramReframeResponse;
  // ...
}

// 新的简化类型
interface IdeogramActualResponse {
  images: IdeogramFile[];
  seed: number;
  error?: {
    message: string;
    code?: string;
  };
}
```

### 2. 更新结果处理逻辑

重写了结果处理器，基于实际响应内容判断任务状态：

```typescript
const resultProcessor = (result: IdeogramActualResponse): TaskInfoResult => {
  console.log("Ideogram status response: ", result);

  // 检查是否有错误
  if (result.error) {
    return {
      taskId: taskId,
      status: TaskStatus.FAILED,
      error: result.error.message
    } as TaskInfoResult;
  }

  // 检查是否有图片结果
  if (result.images && result.images.length > 0) {
    const firstImage = result.images[0];
    return {
      taskId: taskId,
      status: TaskStatus.SUCCESS,
      imageUrl: firstImage.url,
      mimeType: firstImage.content_type
    } as TaskInfoResult;
  }

  // 如果没有图片也没有错误，说明任务还在处理中
  return {
    taskId: taskId,
    status: TaskStatus.PROCESSING
  } as TaskInfoResult;
};
```

### 3. 状态判断逻辑

| 响应情况 | 任务状态 | 说明 |
|---------|---------|------|
| 包含 `images` 数组且非空 | `SUCCESS` | 任务完成，返回图片URL |
| 包含 `error` 字段 | `FAILED` | 任务失败，返回错误信息 |
| 404 或空响应 | `PROCESSING` | 任务还在处理中 |
| 其他情况 | `PROCESSING` | 默认为处理中状态 |

## 测试验证

修复后的响应处理能够正确处理：

1. ✅ **成功响应**: 正确提取图片URL和MIME类型
2. ✅ **错误响应**: 正确处理错误信息
3. ✅ **处理中状态**: 正确处理404或空响应

## 相关文件

- `src/service/app/image-extends.ts` - 主要修改文件
- `docs/fal-api-status-endpoint-fix.md` - 相关的端点修复文档

## 注意事项

1. **API一致性**: Ideogram API 的响应格式与其他 fal.ai 服务不同
2. **错误处理**: 需要依赖 HTTP 状态码和响应内容来判断任务状态
3. **文档更新**: 后续集成其他 fal.ai 服务时需要验证其响应格式

## 影响范围

- ✅ **Ideogram 服务**: 修复了响应处理逻辑
- ✅ **其他服务**: 不受影响，继续使用默认处理逻辑
- ✅ **向后兼容**: 保持了接口的一致性 