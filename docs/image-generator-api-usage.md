# Image Generator API 使用文档

## 概述

图片生成 API 基于 [fal.ai Recraft V3](https://fal.ai/models/fal-ai/recraft/v3/text-to-image/api#schema-input) 模型，提供高质量的文本到图片生成服务。该 API 完全遵循 fal.ai 官方规范，确保最佳的兼容性和效果。

## API 端点

```
POST /api/image-generator
```

## 请求参数

### 必需参数

| 参数 | 类型 | 描述 |
|------|------|------|
| `prompt` | `string` | 图片生成的文本提示词 |
| `source` | `SOURCE` | 来源标识，可选值：`api`, `web`, `canva`, `framer`, `figma`, `rapidapi`, `mcp` |

### 可选参数

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `image_size` | `ImageSize` | `square_hd` | 图片尺寸，可以是预定义枚举或自定义对象 |
| `style` | `StyleEnum` | `realistic_image` | 图片样式，矢量样式费用为 2X |
| `colors` | `RGBColor[]` | `[]` | 首选颜色数组 |
| `style_id` | `string` | - | 自定义样式参考 ID |
| `enable_safety_checker` | `boolean` | - | 是否启用安全检查器 |

## 数据类型

### ImageSize

**预定义尺寸:**
- `square_hd` - 高清正方形
- `square` - 标准正方形  
- `portrait_4_3` - 竖版 4:3
- `portrait_16_9` - 竖版 16:9
- `landscape_4_3` - 横版 4:3
- `landscape_16_9` - 横版 16:9

**自定义尺寸:**
```json
{
  "width": 1280,
  "height": 720
}
```

### StyleEnum

**主要样式:**
- `any` - 任意样式
- `realistic_image` - 写实图像（默认）
- `digital_illustration` - 数字插画
- `vector_illustration` - 矢量插画

**写实图像子样式:**
- `realistic_image/hdr` - HDR 效果
- `realistic_image/natural_light` - 自然光线
- `realistic_image/studio_portrait` - 工作室肖像
- `realistic_image/urban_drama` - 都市戏剧
- 等等...

**数字插画子样式:**
- `digital_illustration/pixel_art` - 像素艺术
- `digital_illustration/hand_drawn` - 手绘风格
- `digital_illustration/pop_art` - 波普艺术
- 等等...

**矢量插画子样式:**
- `vector_illustration/line_art` - 线条艺术
- `vector_illustration/infographical` - 信息图形
- `vector_illustration/editorial` - 编辑插画
- 等等...

### RGBColor

```json
{
  "r": 255,
  "g": 128, 
  "b": 0
}
```

## 示例请求

### 基础请求

```bash
curl -X POST https://your-api.com/api/image-generator \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "a red panda eating bamboo in a zen garden",
    "source": "api"
  }'
```

### 完整参数请求

```bash
curl -X POST https://your-api.com/api/image-generator \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "a futuristic cityscape at sunset with flying cars",
    "image_size": "landscape_16_9",
    "style": "digital_illustration/cyberpunk",
    "colors": [
      {"r": 255, "g": 100, "b": 0},
      {"r": 0, "g": 150, "b": 255}
    ],
    "enable_safety_checker": true,
    "source": "api"
  }'
```

### 自定义尺寸请求

```bash
curl -X POST https://your-api.com/api/image-generator \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "minimalist logo design for a tech company",
    "image_size": {
      "width": 512,
      "height": 512
    },
    "style": "vector_illustration/line_art",
    "source": "api"
  }'
```

## 响应格式

### 成功响应 (200)

```json
{
  "taskId": "abc123def456"
}
```

### 错误响应

**验证错误 (400):**
```json
{
  "error": "Validation error: prompt is required"
}
```

**认证错误 (401):**
```json
{
  "error": "Authentication failed"
}
```

**服务器错误 (500):**
```json
{
  "error": "Failed to create image generation task"
}
```

## 任务状态查询

使用返回的 `taskId` 查询任务状态：

```bash
curl -X GET https://your-api.com/api/tasks/{taskId} \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 最佳实践

1. **提示词优化**: 使用具体、描述性的提示词以获得最佳效果
2. **样式选择**: 根据需求选择合适的样式，注意矢量样式费用较高
3. **尺寸规划**: 优先使用预定义尺寸以获得最佳性能
4. **颜色搭配**: 提供主色调以确保生成图片符合品牌要求
5. **安全检查**: 对于面向公众的应用建议启用安全检查器

## 费用说明

- 标准样式（realistic_image, digital_illustration）: 1x 费用
- 矢量样式（vector_illustration）: 2x 费用
- 自定义尺寸可能产生额外费用

## 技术规范

 **最大提示词长度**: （请确认 API 文档）
 **支持的输入图片格式**: PNG, JPG, WebP, GIF, AVIF  
 **输出图片格式**: WebP  
 **最大分辨率**: 4 MP（任意一边 ≤ 4096 像素）  
 **最小分辨率**: ≥ 32×32 像素（某些场景下 ≥ 256×256）  
 **输入文件大小上限**: 5 MB  
 **处理时间**: 通常 10–30 秒

## 相关链接

- [fal.ai Recraft V3 官方文档](https://fal.ai/models/fal-ai/recraft/v3/text-to-image/api#schema-input)
- [任务状态查询 API](/docs/task-status-api.md)
- [错误码参考](/docs/error-codes.md) 