# Fal API 状态查询端点修复文档

## 问题描述

在使用 fal.ai API 时发现，不同服务的状态查询端点格式不一致：

- **大部分服务**: `https://queue.fal.run/fal-ai/{service}/requests/{taskId}`
- **Ideogram 服务**: `https://queue.fal.run/fal-ai/ideogram/requests/{taskId}`

具体来说，Ideogram 服务的创建端点是 `ideogram/v3/reframe`，但状态查询端点是 `ideogram/requests/{taskId}`，而不是 `ideogram/v3/reframe/requests/{taskId}`。

## 解决方案

### 1. 扩展 FalGetTaskOptions 接口

添加了可选的 `statusEndpoint` 参数，允许自定义状态查询端点：

```typescript
export interface FalGetTaskOptions extends FalApiOptions {
    taskId: string;
    statusEndpoint?: string; // 可选的自定义状态查询端点
    resultProcessor?: (result: any) => TaskInfoResult;
}
```

### 2. 修改 doGetTaskStatus 方法

更新方法以支持自定义状态端点：

```typescript
static async doGetTaskStatus(options: FalGetTaskOptions): Promise<TaskInfoResult> {
    const { endpoint, apiKey, taskId, statusEndpoint: customStatusEndpoint, taskName = 'fal task', resultProcessor } = options;
    // 使用自定义状态端点，如果没有提供则使用默认格式
    const statusEndpoint = customStatusEndpoint || `${endpoint}/requests/${taskId}`;
    
    // ... 其余逻辑保持不变
}
```

### 3. 新增 buildStatusEndpoint 方法

添加专门的方法来构建状态查询端点，处理特殊情况：

```typescript
/**
 * 构建 fal API 状态查询端点 URL
 * 某些服务的状态查询端点与创建端点不同
 */
static buildStatusEndpoint(service: string, taskId: string): string {
    // 对于 ideogram 服务，状态查询端点使用简化路径
    if (service.startsWith('ideogram/')) {
        return `https://queue.fal.run/fal-ai/ideogram/requests/${taskId}`;
    }
    
    // 默认格式
    return `https://queue.fal.run/fal-ai/${service}/requests/${taskId}`;
}
```

### 4. 更新 image-extends.ts

修改 Ideogram 服务的状态查询调用：

```typescript
return await FalApiClient.doGetTaskStatus({
    endpoint: FalApiClient.buildEndpoint(FAL_ENDPOINTS.IDEOGRAM_V3_REFRAME),
    apiKey: env.FAL_API_KEY,
    taskId: taskId,
    statusEndpoint: FalApiClient.buildStatusEndpoint(FAL_ENDPOINTS.IDEOGRAM_V3_REFRAME, taskId),
    taskName: 'Image Extends (Ideogram)',
    resultProcessor: resultProcessor
});
```

## 端点对比

### Ideogram 服务

**创建任务:**
```bash
curl --request POST \
  --url https://queue.fal.run/fal-ai/ideogram/v3/reframe \
  --header "Authorization: Key $FAL_KEY"
```

**查询状态:**
```bash
curl --request GET \
  --url https://queue.fal.run/fal-ai/ideogram/requests/$REQUEST_ID \
  --header "Authorization: Key $FAL_KEY"
```

### 其他服务 (如 Recraft V3)

**创建任务:**
```bash
curl --request POST \
  --url https://queue.fal.run/fal-ai/recraft/v3/text-to-image \
  --header "Authorization: Key $FAL_KEY"
```

**查询状态:**
```bash
curl --request GET \
  --url https://queue.fal.run/fal-ai/recraft/v3/text-to-image/requests/$REQUEST_ID \
  --header "Authorization: Key $FAL_KEY"
```

## 向后兼容性

- ✅ 现有的服务调用无需修改，继续使用默认的状态查询格式
- ✅ 新的 `statusEndpoint` 参数是可选的
- ✅ `buildStatusEndpoint` 方法自动处理特殊情况

## 影响的服务

### 需要特殊处理的服务
- **Ideogram**: 使用简化的状态查询路径

### 使用默认格式的服务
- **Recraft V3**: `recraft/v3/text-to-image/requests/{taskId}`
- **Clarity Upscaler**: `clarity-upscaler/requests/{taskId}`
- **Image2SVG**: `image2svg/requests/{taskId}`

## 测试验证

修复后需要验证：

1. **Ideogram 服务**: 确认状态查询使用正确的端点
2. **其他服务**: 确认仍使用默认格式且功能正常
3. **错误处理**: 验证端点错误时的处理逻辑

## 相关文件

- `src/utils/fal-api.ts` - 核心修改文件
- `src/service/app/image-extends.ts` - 使用新端点的服务
- `docs/fal-api-refactor.md` - 原始重构文档

## 注意事项

1. **端点一致性**: 新增服务时需要检查其状态查询端点格式
2. **错误处理**: 确保自定义端点的错误处理逻辑正确
3. **文档更新**: 新增服务时需要更新相关文档 