# Video Upscaler 环境变量重构文档

## 概述

将 `video-upscaler.ts` 文件中硬编码的 API URL 和认证 token 提炼到环境变量中，提高代码的可维护性和安全性。

## 重构内容

### 1. 环境变量定义

在 `worker-configuration.d.ts` 中新增了两个环境变量：

```typescript
interface Env {
    // ... 其他环境变量
    VIDEO_UPSCALER_API_URL: string;
    VIDEO_UPSCALER_TOKEN: string;
}
```

### 2. 配置文件更新

在所有环境的 `wrangler.toml` 配置文件的 `[vars]` 部分添加了配置：

**开发环境 (`wrangler.toml`):**
```toml
# Video Upscaler API 配置
VIDEO_UPSCALER_API_URL="https://app-vu-2.a1d.ai/api/task"
VIDEO_UPSCALER_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkExRCBBZG1pbiIsImlhdCI6MTUyNjIzOTAyMn0.UV-u7-0GIvWtkeZyrua-LfPOcpXCRbYfpFdwL6-w1nw"
```

**测试环境 (`wrangler.test.toml`):**
```toml
# Video Upscaler API 配置
VIDEO_UPSCALER_API_URL="https://app-vu-2.a1d.ai/api/task"
VIDEO_UPSCALER_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkExRCBBZG1pbiIsImlhdCI6MTUyNjIzOTAyMn0.UV-u7-0GIvWtkeZyrua-LfPOcpXCRbYfpFdwL6-w1nw"
```

**生产环境 (`wrangler.prod.toml`):**
```toml
# Video Upscaler API 配置
VIDEO_UPSCALER_API_URL="https://app-vu-2.a1d.ai/api/task"
VIDEO_UPSCALER_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkExRCBBZG1pbiIsImlhdCI6MTUyNjIzOTAyMn0.UV-u7-0GIvWtkeZyrua-LfPOcpXCRbYfpFdwL6-w1nw"
```

### 3. 代码重构

#### 函数签名更新

**之前:**
```typescript
export async function doCreateVideoUpscalerTask(task: TaskInfoRequest): Promise<any>
```

**现在:**
```typescript
export async function doCreateVideoUpscalerTask(task: TaskInfoRequest, env: Env): Promise<any>
```

#### URL 配置更新

**之前:**
```typescript
// 创建任务
const url = "https://app-vu-2.a1d.ai/api/task";

// 获取任务状态
const url = `https://app-vu-2.a1d.ai/api/task/${taskId}`;
```

**现在:**
```typescript
// 创建任务
const url = env.VIDEO_UPSCALER_API_URL;

// 获取任务状态
const url = `${env.VIDEO_UPSCALER_API_URL}/${taskId}`;
```

#### 认证 Token 更新

**之前:**
```typescript
// 创建任务
'Authorization': `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwicm9sZXMiOlsiYWRtaW4iLCJ1c2VyIl0sImlhdCI6MTUyNjIzOTAyMn0.PTcqwU7B_yMxZZoHXcnD1c576fTF6pDif1L_SaKHIag`

// 获取任务状态
'Authorization': `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkExRCBBZG1pbiIsImlhdCI6MTUyNjIzOTAyMn0.UV-u7-0GIvWtkeZyrua-LfPOcpXCRbYfpFdwL6-w1nw`
```

**现在:**
```typescript
// 统一使用环境变量
'Authorization': `Bearer ${env.VIDEO_UPSCALER_TOKEN}`
```

### 4. 调用方更新

在 `create-task.ts` 中更新了函数调用：

**之前:**
```typescript
case 'vu':
    result = await doCreateVideoUpscalerTask(task);
    break;
```

**现在:**
```typescript
case 'vu':
    result = await doCreateVideoUpscalerTask(task, env);
    break;
```

## 重构优势

### 1. 安全性提升
- 敏感的认证 token 不再硬编码在源代码中
- 可以通过 Cloudflare Workers 的 secrets 管理敏感信息

### 2. 可维护性提升
- API URL 和 token 可以通过配置文件统一管理
- 不同环境（开发、测试、生产）可以使用不同的配置
- 修改配置无需重新编译代码

### 3. 灵活性提升
- 支持动态切换不同的 Video Upscaler 服务端点
- 便于进行 A/B 测试或服务迁移

### 4. 代码规范
- 遵循了 12-Factor App 的配置管理原则
- 与项目中其他服务的配置方式保持一致

## 环境配置说明

### 多环境配置
已在所有环境配置文件中添加了相应的环境变量：

- **开发环境**: `wrangler.toml`
- **测试环境**: `wrangler.test.toml` 
- **生产环境**: `wrangler.prod.toml`

### 生产环境安全建议
对于生产环境，建议使用 Cloudflare Workers 的 secrets 功能来管理敏感信息：

```bash
# 设置生产环境的 token（推荐）
wrangler secret put VIDEO_UPSCALER_TOKEN --env production

# 或者继续使用配置文件中的值
```

### 环境变量说明

| 环境变量 | 说明 | 示例值 |
|---------|------|--------|
| `VIDEO_UPSCALER_API_URL` | Video Upscaler API 的基础 URL | `https://app-vu-2.a1d.ai/api/task` |
| `VIDEO_UPSCALER_TOKEN` | 认证 token | JWT token 字符串 |

## 相关文件

- `src/service/app/video-upscaler.ts` - 主要重构文件
- `src/service/task/create-task.ts` - 调用方更新
- `worker-configuration.d.ts` - 环境变量类型定义
- `wrangler.toml` - 开发环境配置
- `wrangler.test.toml` - 测试环境配置
- `wrangler.prod.toml` - 生产环境配置

## 注意事项

1. **Token 安全性**: 生产环境建议使用 `wrangler secret` 命令管理敏感的 token
2. **URL 格式**: 确保 `VIDEO_UPSCALER_API_URL` 不包含尾部斜杠，因为代码中会自动拼接路径
3. **环境一致性**: 确保所有环境的配置格式保持一致

## 最新更新

### API 参数优化 (2024-12-19)

将 `mimeType` 参数改为可选参数，并设置默认值：

**API Schema 更新:**
```typescript
mimeType: z.string().optional().describe('Video MIME type, defaults to video/mp4')
```

**处理逻辑更新:**
```typescript
// set default mimeType
if (!mimeType) {
    mimeType = 'video/mp4';
}
```

**影响:**
- 客户端调用时可以不传 `mimeType` 参数
- 默认使用 `video/mp4` 作为视频类型
- 向后兼容，仍支持显式传入 `mimeType`

## 测试验证

重构完成后，建议进行以下测试：

1. **功能测试**: 验证 video upscaler 功能是否正常工作
2. **配置测试**: 验证不同环境配置是否正确加载
3. **错误处理**: 验证配置缺失时的错误处理是否正确
4. **参数测试**: 验证 `mimeType` 参数可选和默认值功能 