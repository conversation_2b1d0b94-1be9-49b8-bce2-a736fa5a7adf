# Ideogram V3 Reframe API 参考

## 端点

```
POST /api/image-extends
```

## 描述

使用 Ideogram V3 Reframe API 对图像进行智能重构和扩展。该 API 可以将图像调整为不同的尺寸比例，同时保持主要内容的完整性。

## 请求参数

### 必需参数

| 参数 | 类型 | 描述 |
|------|------|------|
| `imageUrl` | string | 要重构的原始图片 URL。支持 JPEG、PNG、WebP 格式 |
| `source` | string | 渠道来源，可选值：`api`, `web`, `framer`, `figma`, `canva`, `rapidapi`, `mcp` |

### 可选参数

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `imageSize` | string \| object | `"square_hd"` | 输出图片尺寸，详见下方说明 |
| `renderingSpeed` | string | `"BALANCED"` | 渲染速度，可选值：`TURBO`, `BALANCED`, `QUALITY` |
| `numImages` | number | `1` | 生成图片数量，范围：1-4 |
| `seed` | number | - | 随机种子，用于生成可重复的结果 |

## imageSize 参数详解

`imageSize` 参数用于指定重构输出图像的分辨率。

### 预设尺寸枚举值

| 值 | 分辨率 | 描述 |
|---|--------|------|
| `square_hd` | 1024×1024 | 高清正方形 |
| `square` | 512×512 | 标准正方形 |
| `portrait_4_3` | 768×1024 | 4:3 竖向 |
| `portrait_16_9` | 576×1024 | 16:9 竖向 |
| `landscape_4_3` | 1024×768 | 4:3 横向 |
| `landscape_16_9` | 1024×576 | 16:9 横向 |

### 自定义尺寸对象

对于自定义图像尺寸，可以传递包含宽度和高度的对象：

```json
{
  "imageSize": {
    "width": 1280,
    "height": 720
  }
}
```

## renderingSpeed 参数详解

| 值 | 描述 | 适用场景 |
|---|------|----------|
| `TURBO` | 最快速度，质量较低 | 快速预览、批量处理 |
| `BALANCED` | 平衡速度和质量 | 大多数使用场景（推荐） |
| `QUALITY` | 最高质量，速度较慢 | 最终输出、高质量要求 |

## 请求示例

### 基本请求
```json
{
  "imageUrl": "https://v3.fal.media/files/lion/0qJs_qW8nz0wYsXhFa6Tk.png",
  "imageSize": "landscape_16_9",
  "source": "api"
}
```

### 完整参数请求
```json
{
  "imageUrl": "https://v3.fal.media/files/lion/0qJs_qW8nz0wYsXhFa6Tk.png",
  "imageSize": "square_hd",
  "renderingSpeed": "QUALITY",
  "numImages": 1,
  "seed": 123456,
  "source": "api"
}
```

### 自定义尺寸请求
```json
{
  "imageUrl": "https://v3.fal.media/files/lion/0qJs_qW8nz0wYsXhFa6Tk.png",
  "imageSize": {
    "width": 1920,
    "height": 1080
  },
  "renderingSpeed": "BALANCED",
  "source": "api"
}
```

## 响应格式

### 成功响应
```json
{
  "taskId": "abc123def456"
}
```

### 错误响应
```json
{
  "success": false,
  "error": "Create image reframe task failed: Invalid image URL"
}
```

## 任务状态查询

使用返回的 `taskId` 查询任务状态：

```
GET /api/task/{taskId}
```

### 状态响应示例

#### 处理中
```json
{
  "taskId": "abc123def456",
  "status": "PROCESSING",
  "progress": 50
}
```

#### 完成
```json
{
  "taskId": "abc123def456",
  "status": "SUCCESS",
  "imageUrl": "https://v3.fal.media/files/zebra/LVW4AhVs3sCxsVKdg3EfT_image.png",
  "progress": 100
}
```

#### 失败
```json
{
  "taskId": "abc123def456",
  "status": "FAILED",
  "error": "Task processing failed",
  "progress": 0
}
```

## 错误代码

| HTTP 状态码 | 描述 |
|-------------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权，JWT token 无效 |
| 403 | 权限不足或积分不够 |
| 500 | 服务器内部错误 |

## 使用限制

1. **图片格式**：支持 JPEG、PNG、WebP 格式
2. **文件大小**：建议不超过 10MB
3. **处理时间**：根据渲染质量设置，可能从几秒到几分钟不等
4. **API 限制**：受 Ideogram V3 API 使用限制约束

## 认证

所有请求都需要在 Header 中包含有效的 JWT token：

```
Authorization: Bearer YOUR_JWT_TOKEN
```

## cURL 示例

```bash
curl -X POST https://api.a1d.ai/api/image-extends \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "imageUrl": "https://v3.fal.media/files/lion/0qJs_qW8nz0wYsXhFa6Tk.png",
    "imageSize": "landscape_16_9",
    "renderingSpeed": "BALANCED",
    "source": "api"
  }'
``` 