# Chat-2-Design 任务创建功能实现

## 概述

为 chat-2-design 端点添加了任务创建逻辑，使其能够像其他端点一样进行积分扣除和任务跟踪，同时保持原有的响应格式。

## 实现的功能

### 1. 任务创建和处理
- 在 chat-2-design 端点中创建任务记录
- 执行 AI 处理逻辑（调用 302.ai API）
- 处理图片下载和上传到 R2
- 返回原有的响应格式（保持向后兼容）

### 2. 积分系统集成
- 添加了 chat-2-design 的信用规则配置
- 支持积分计算和扣除
- 在任务完成时自动处理积分

### 3. URL 处理增强
- 扩展了 UrlProcessor 以支持 `imageUrls` 数组字段
- 自动将外部图片 URL 转换为 A1D CDN URL
- 保留原始 URL 作为备份

### 4. Chat-2-Design 特殊处理
- 当 app 为 'chat-2-design' 且没有提供 source 参数时，自动默认为 'canva'
- 其他应用仍然要求明确提供 source 参数
- 提高了 chat-2-design 的易用性



## 文件修改清单

### 新增文件
1. `worker/api-service/src/service/app/chat-2-design.ts` - Chat-2-Design 服务实现
2. `worker/api-service/tests/service/chat-2-design.test.ts` - 单元测试
3. `docs/chat-2-design-implementation.md` - 本文档

### 修改的文件
1. `worker/api-service/src/endpoints/chat-2-design.ts`
   - 添加任务创建逻辑
   - 保持原有响应格式
   - 集成积分扣除
   - 添加 source 参数默认值处理（默认为 'canva'）

2. `worker/api-service/src/service/task/create-task.ts`
   - 添加 chat-2-design 的处理分支
   - 导入 chat-2-design 服务

3. `worker/api-service/src/service/task/task-service.ts`
   - 添加 chat-2-design 的获取任务逻辑

4. `worker/api-service/src/model/task.ts`
   - 添加 `imageUrls` 和 `message` 字段到 TaskInfoResult

5. `worker/api-service/src/utils/url-processor.ts`
   - 添加对 `imageUrls` 数组的处理支持
   - 更新 URL 检查逻辑

6. `worker/api-service/src/endpoints/calculate-credits.ts`
   - 添加 Chat2DesignTaskSchema
   - 更新 TaskItemSchema

7. `worker/api-service/src/service/credit/calculate-credits.ts`
   - 添加 chat-2-design 的 TaskItem 类型

8. `worker/api-service/src/middlewares/credit.ts`
   - 添加 chat-2-design 应用的特殊 source 默认值处理

9. `worker/api-service/src/queue.ts`
    - 更新队列处理逻辑以支持 imageUrls 数组字段的 URL 转换
    - 修复任务结果数据库更新问题

10. `worker/api-service/src/service/task/task-mapper.ts`
    - 添加 imageUrls 和 message 字段到 normalizeTaskResult 方法
    - 确保 GET 任务接口能返回完整的结果

11. `worker/api-service/src/endpoints/fetch-task-sse.ts`
    - 更新响应模式以包含 imageUrls 和 message 字段

12. `worker/api-service/src/service/app/chat-2-design.ts`
    - 更新 doGetChat2DesignTask 以从数据库获取实际结果

## 数据库更改

需要在 `credit_rules` 表中添加 chat-2-design 的信用规则配置。具体的信用点数和规则配置请根据业务需求设置。

## API 使用方式

### 请求格式
```json
POST /api/chat-2-design
{
  "imageUrls": ["https://example.com/input.jpg"],
  "prompt": "Create a beautiful design based on this image",
  "source": "canva"  // 可选，默认为 'canva'
}
```

**注意**: `source` 参数对于 chat-2-design 是可选的，如果不提供会自动默认为 'canva'。

### 响应格式
```json
{
  "success": true,
  "taskId": "v4PGHA3ewoXlfUQK_ooPx",
  "imageUrls": ["https://cdn.a1d.ai/result/chat-2-design/generated_image.png"],
  "message": ""
}
```

## 任务流程

1. **请求接收**: 端点接收请求并验证用户身份
2. **积分检查**: 中间件检查用户是否有足够积分
3. **任务创建**: 创建任务记录并调用处理服务
4. **AI 处理**: 调用 302.ai API 进行图像生成
5. **图片处理**: 下载生成的图片并上传到 R2
6. **URL 转换**: 将外部 URL 转换为 A1D CDN URL
7. **积分扣除**: 任务完成后自动扣除积分
8. **响应返回**: 返回处理结果

## 特点

### 向后兼容
- 保持原有的 API 接口不变
- 响应格式增加了 taskId 字段
- source 参数可选，默认为 'canva'
- 客户端无需修改（可选择性地使用新字段）

### 积分集成
- 自动积分检查和扣除
- 支持不同来源的不同积分规则
- 失败时不扣除积分

### URL 管理
- 自动将外部图片转换为 CDN URL
- 支持数组形式的多个图片 URL
- 保留原始 URL 作为备份

### 错误处理
- 完善的错误处理和日志记录
- API 失败时返回适当的错误信息
- 网络问题时的优雅降级

## 测试

运行测试：
```bash
cd worker/api-service
npm test tests/service/chat-2-design.test.ts
```

## 部署注意事项

1. **数据库配置**: 需要在 `credit_rules` 表中添加 chat-2-design 的信用规则
2. **环境变量**: 确保 `302_API_KEY` 已配置
3. **R2 配置**: 确保 R2 存储桶和域名配置正确
4. **积分规则**: 根据需要调整信用点数配置
