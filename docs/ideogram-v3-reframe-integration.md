# Ideogram V3 Reframe API 集成

## 概述

我们已经将现有的图像扩展服务升级为使用 Ideogram V3 Reframe API。这个新的 API 提供了更强大的图像重构和扩展功能。

## API 变更

### 新的请求参数

```json
{
  "imageUrl": "https://example.com/image.jpg",
  "imageSize": "square_hd",
  "renderingSpeed": "BALANCED",
  "numImages": 1,
  "seed": 123456,
  "source": "api"
}
```

### API 参数详细说明

| 参数 | 类型 | 必需 | 默认值 | 描述 |
|------|------|------|--------|------|
| imageUrl | string | 是 | - | 原始图片的 URL |
| imageSize | string/object | 否 | "square_hd" | 输出图片尺寸，可以是预设尺寸或自定义尺寸对象 |
| renderingSpeed | string | 否 | "BALANCED" | 渲染速度：TURBO/BALANCED/QUALITY |
| numImages | number | 否 | 1 | 生成图片数量 (1-4) |
| seed | number | 否 | - | 随机种子，用于可重复的结果 |
| source | string | 是 | - | 渠道来源：api/web/framer/figma/canva/rapidapi/mcp |

### 支持的图片尺寸

`image_size` 参数用于指定重构输出图像的分辨率。

#### 预设尺寸枚举值
- `square_hd`: 高清正方形 (1024x1024)
- `square`: 标准正方形 (512x512)
- `portrait_4_3`: 4:3 竖向 (768x1024)
- `portrait_16_9`: 16:9 竖向 (576x1024)
- `landscape_4_3`: 4:3 横向 (1024x768)
- `landscape_16_9`: 16:9 横向 (1024x576)

#### 自定义尺寸对象
对于自定义图像尺寸，可以传递包含宽度和高度的对象：

```json
{
  "imageSize": {
    "width": 1280,
    "height": 720
  }
}
```

#### 完整请求示例
```json
{
  "rendering_speed": "BALANCED",
  "num_images": 1,
  "image_url": "https://v3.fal.media/files/lion/0qJs_qW8nz0wYsXhFa6Tk.png",
  "image_size": "square_hd"
}
```

### 渲染速度选项
- `TURBO`: 快速渲染
- `BALANCED`: 平衡质量和速度（默认）
- `QUALITY`: 高质量渲染

## 使用示例

### 1. 基本图像重构（使用预设尺寸）
```bash
curl -X POST https://api.a1d.ai/api/image-extends \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "imageUrl": "https://v3.fal.media/files/lion/0qJs_qW8nz0wYsXhFa6Tk.png",
    "imageSize": "landscape_16_9",
    "source": "api"
  }'
```

### 2. 自定义尺寸重构
```bash
curl -X POST https://api.a1d.ai/api/image-extends \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "imageUrl": "https://v3.fal.media/files/lion/0qJs_qW8nz0wYsXhFa6Tk.png",
    "imageSize": {
      "width": 1920,
      "height": 1080
    },
    "renderingSpeed": "QUALITY",
    "source": "api"
  }'
```

### 3. 高质量渲染
```bash
curl -X POST https://api.a1d.ai/api/image-extends \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "imageUrl": "https://v3.fal.media/files/lion/0qJs_qW8nz0wYsXhFa6Tk.png",
    "imageSize": "square_hd",
    "renderingSpeed": "QUALITY",
    "numImages": 1,
    "source": "api"
  }'
```

### 4. 快速渲染（TURBO 模式）
```bash
curl -X POST https://api.a1d.ai/api/image-extends \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "imageUrl": "https://v3.fal.media/files/lion/0qJs_qW8nz0wYsXhFa6Tk.png",
    "imageSize": "portrait_4_3",
    "renderingSpeed": "TURBO",
    "source": "api"
  }'
```

### 5. 使用随机种子确保可重复结果
```bash
curl -X POST https://api.a1d.ai/api/image-extends \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "imageUrl": "https://v3.fal.media/files/lion/0qJs_qW8nz0wYsXhFa6Tk.png",
    "imageSize": "landscape_4_3",
    "renderingSpeed": "BALANCED",
    "seed": 123456,
    "source": "api"
  }'
```

## 重要变更

⚠️ **破坏性变更**: 我们已经完全移除了对旧参数的支持：
- `maskUrl`: 遮罩 URL（已移除）
- `imageRatio`: 图片比例（已移除）

请使用新的 `imageSize` 参数来实现图像重构功能。

## 环境变量配置

确保在 `wrangler.toml` 中配置了 FAL API Key：

```toml
[vars]
FAL_API_KEY = "your-fal-api-key"
```

或者使用 wrangler secrets：

```bash
wrangler secret put FAL_API_KEY
```

## 任务状态映射

Ideogram API 状态 → 我们的任务状态：
- `IN_QUEUE` → `PROCESSING`
- `IN_PROGRESS` → `PROCESSING`
- `COMPLETED` → `SUCCESS`
- `FAILED` → `FAILED`

## 错误处理

API 会返回详细的错误信息：

```json
{
  "success": false,
  "error": "Create image reframe task failed: Invalid image URL"
}
```

## 测试

可以使用以下测试图片进行测试：
- https://v3.fal.media/files/lion/0qJs_qW8nz0wYsXhFa6Tk.png

## 注意事项

1. **API 限制**: Ideogram V3 API 有使用限制，请合理使用
2. **图片格式**: 支持 JPEG、PNG、WebP 格式
3. **文件大小**: 建议图片大小不超过 10MB
4. **处理时间**: 根据渲染质量设置，处理时间可能从几秒到几分钟不等

## 迁移指南

如果你正在使用旧的图像扩展 API，**必须**进行以下更改：

1. **移除旧参数**: 删除 `maskUrl` 和 `imageRatio` 参数
2. **使用新参数**: 使用 `imageSize` 参数指定输出尺寸
3. **调整渲染速度**: 根据需要设置 `renderingSpeed` 参数
4. **测试新响应**: 验证新的 API 响应格式

## 技术实现

### 核心文件
- `src/service/app/image-extends.ts`: 主要的 Ideogram API 集成逻辑
- `src/endpoints/image-extends.ts`: API 端点定义和参数验证

### 关键函数
- `doCreateImageExtendsTask()`: 创建 Ideogram 重构任务
- `doGetImageExtendsTask()`: 获取任务状态和结果

### 类型定义
```typescript
interface IdeogramReframeRequest {
  image_url: string;
  image_size: IdeogramImageSizeEnum | IdeogramImageSize;
  num_images?: number;
  seed?: number;
  rendering_speed?: 'TURBO' | 'BALANCED' | 'QUALITY';
}
``` 