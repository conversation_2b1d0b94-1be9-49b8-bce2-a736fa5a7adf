# Ideogram V3 Reframe API 测试示例

## 基本测试

### 1. 简单图像重构
```bash
curl -X POST https://your-api-domain.com/api/image-extends \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "imageUrl": "https://v3.fal.media/files/lion/0qJs_qW8nz0wYsXhFa6Tk.png",
    "imageSize": "landscape_16_9",
    "source": "api"
  }'
```

### 2. 高质量渲染重构
```bash
curl -X POST https://your-api-domain.com/api/image-extends \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "imageUrl": "https://v3.fal.media/files/lion/0qJs_qW8nz0wYsXhFa6Tk.png",
    "imageSize": "portrait_4_3",
    "renderingSpeed": "QUALITY",
    "source": "api"
  }'
```

### 3. 自定义尺寸重构
```bash
curl -X POST https://your-api-domain.com/api/image-extends \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "imageUrl": "https://v3.fal.media/files/lion/0qJs_qW8nz0wYsXhFa6Tk.png",
    "imageSize": {
      "width": 1920,
      "height": 1080
    },
    "renderingSpeed": "BALANCED",
    "numImages": 1,
    "source": "api"
  }'
```

### 4. 快速渲染测试
```bash
curl -X POST https://your-api-domain.com/api/image-extends \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "imageUrl": "https://v3.fal.media/files/lion/0qJs_qW8nz0wYsXhFa6Tk.png",
    "imageSize": "square_hd",
    "renderingSpeed": "TURBO",
    "source": "api"
  }'
```

## 预期响应

### 成功响应
```json
{
  "taskId": "abc123def456"
}
```

### 错误响应
```json
{
  "error": "Create image reframe task failed: Invalid image URL"
}
```

## 任务状态查询

创建任务后，使用返回的 `taskId` 查询任务状态：

```bash
curl -X GET https://your-api-domain.com/api/task/abc123def456 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 任务状态响应示例

#### 处理中
```json
{
  "taskId": "abc123def456",
  "status": "PROCESSING",
  "progress": 50
}
```

#### 完成
```json
{
  "taskId": "abc123def456",
  "status": "SUCCESS",
  "imageUrl": "https://v3.fal.media/files/zebra/LVW4AhVs3sCxsVKdg3EfT_image.png",
  "progress": 100
}
```

#### 失败
```json
{
  "taskId": "abc123def456",
  "status": "FAILED",
  "error": "Task processing failed",
  "progress": 0
}
```

## 测试用图片

可以使用以下测试图片进行测试：

1. **狮子图片**: `https://v3.fal.media/files/lion/0qJs_qW8nz0wYsXhFa6Tk.png`
2. **风景图片**: 上传你自己的图片到文件服务

## 参数说明

### imageSize 选项

`image_size` 参数用于指定重构输出图像的分辨率。

#### 预设尺寸枚举值
- `square_hd`: 1024x1024 高清正方形
- `square`: 512x512 标准正方形  
- `portrait_4_3`: 768x1024 竖向 4:3
- `portrait_16_9`: 576x1024 竖向 16:9
- `landscape_4_3`: 1024x768 横向 4:3
- `landscape_16_9`: 1024x576 横向 16:9

#### 自定义尺寸对象
对于自定义图像尺寸，可以传递包含宽度和高度的对象：

```json
{
  "imageSize": {
    "width": 1280,
    "height": 720
  }
}
```

### renderingSpeed 选项
- `TURBO`: 最快速度，质量较低
- `BALANCED`: 平衡速度和质量（推荐）
- `QUALITY`: 最高质量，速度较慢

### 完整 API 请求示例
```json
{
  "rendering_speed": "BALANCED",
  "num_images": 1,
  "image_url": "https://v3.fal.media/files/lion/0qJs_qW8nz0wYsXhFa6Tk.png",
  "image_size": "square_hd"
}
```

## 注意事项

1. **认证**: 确保使用有效的 JWT token
2. **图片格式**: 支持 JPEG、PNG、WebP
3. **图片大小**: 建议不超过 10MB
4. **处理时间**: 根据渲染质量，可能需要几秒到几分钟
5. **API 限制**: 注意 FAL API 的使用限制

## 错误排查

### 常见错误

1. **401 Unauthorized**: 检查 JWT token 是否有效
2. **400 Bad Request**: 检查请求参数格式
3. **500 Internal Server Error**: 检查服务器日志
4. **图片 URL 无效**: 确保图片 URL 可访问

### 调试建议

1. 先使用简单的参数测试
2. 检查图片 URL 是否可访问
3. 验证 JWT token 的有效性
4. 查看服务器日志获取详细错误信息 